# ThinkPHP URL重写规则
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>

# CORS配置 - 为所有静态文件添加跨域支持
<IfModule mod_headers.c>
    # 为HLS文件添加CORS头
    <FilesMatch "\.(m3u8|ts)$">
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key, Range"
        Header always set Access-Control-Max-Age "86400"
    </FilesMatch>
    
    # 为视频文件添加CORS头
    <FilesMatch "\.(mp4|avi|mov|wmv|flv|webm|mkv)$">
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key, Range"
        Header always set Access-Control-Expose-Headers "Content-Length, Content-Range"
    </FilesMatch>
    
    # 为图片文件添加CORS头
    <FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
    </FilesMatch>
</IfModule>

# OPTIONS预检请求交给ThinkPHP的CORS中间件处理
# 不在.htaccess中处理OPTIONS请求

# 缓存配置
<IfModule mod_expires.c>
    ExpiresActive On
    
    # HLS文件不缓存（实时性要求）
    ExpiresByType application/vnd.apple.mpegurl "access plus 0 seconds"
    ExpiresByType video/mp2t "access plus 0 seconds"
    
    # 视频文件缓存1天
    ExpiresByType video/mp4 "access plus 1 day"
    ExpiresByType video/webm "access plus 1 day"
    
    # 图片文件缓存7天
    ExpiresByType image/jpeg "access plus 7 days"
    ExpiresByType image/png "access plus 7 days"
    ExpiresByType image/gif "access plus 7 days"
</IfModule>

# 安全配置
<IfModule mod_headers.c>
    # 防止点击劫持
    Header always append X-Frame-Options SAMEORIGIN
    
    # 防止MIME类型嗅探
    Header always set X-Content-Type-Options nosniff
    
    # XSS保护
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
