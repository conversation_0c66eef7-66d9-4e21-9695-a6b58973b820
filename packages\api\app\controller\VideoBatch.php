<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Db;
use app\service\ResponseService;
use app\service\ValidationService;

/**
 * 视频批量操作控制器
 * 
 * 处理视频的批量操作功能
 */
class VideoBatch
{
    protected ResponseService $responseService;
    protected ValidationService $validationService;

    public function __construct()
    {
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
    }

    /**
     * 批量操作视频
     *
     * @param Request $request
     * @return Response
     */
    public function batchOperation(Request $request): Response
    {
        try {
            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            $data = $request->post();
            
            // 验证参数
            $rules = [
                'operation' => 'require|in:delete,favorite,unfavorite,like,unlike',
                'videoIds' => 'require|array'
            ];
            
            $validation = $this->validationService->validate($data, $rules);
            if (!$validation['valid']) {
                return $this->responseService->error($validation['message'], 400);
            }

            $operation = $data['operation'];
            $videoIds = array_map('intval', $data['videoIds']);

            if (empty($videoIds)) {
                return $this->responseService->error('视频ID列表不能为空', 400);
            }

            // 根据操作类型执行相应的批量操作
            switch ($operation) {
                case 'favorite':
                    return $this->batchFavorite($userId, $videoIds);
                case 'unfavorite':
                    return $this->batchUnfavorite($userId, $videoIds);
                case 'like':
                    return $this->batchLike($userId, $videoIds);
                case 'unlike':
                    return $this->batchUnlike($userId, $videoIds);
                case 'delete':
                    return $this->batchDelete($userId, $videoIds);
                default:
                    return $this->responseService->error('不支持的操作类型', 400);
            }

        } catch (\Exception $e) {
            return $this->responseService->error('批量操作失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量收藏视频
     *
     * @param int $userId
     * @param array $videoIds
     * @return Response
     */
    private function batchFavorite(int $userId, array $videoIds): Response
    {
        Db::startTrans();
        try {
            $successCount = 0;
            $failedIds = [];
            $errors = [];

            foreach ($videoIds as $videoId) {
                // 检查视频是否存在
                $video = Db::table('videos')
                    ->where('id', $videoId)
                    ->where('status', 'published')
                    ->find();

                if (!$video) {
                    $failedIds[] = $videoId;
                    $errors[$videoId] = '视频不存在';
                    continue;
                }

                // 检查是否已经收藏
                $existingCollection = Db::table('video_collections')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->find();

                if ($existingCollection) {
                    $failedIds[] = $videoId;
                    $errors[$videoId] = '已经收藏过了';
                    continue;
                }

                // 添加收藏记录
                Db::table('video_collections')->insert([
                    'video_id' => $videoId,
                    'user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // 更新视频收藏数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->inc('collect_count', 1)
                    ->update();

                $successCount++;
            }

            Db::commit();

            return $this->responseService->success([
                'success' => array_diff($videoIds, $failedIds),
                'failed' => $failedIds,
                'errors' => $errors,
                'success_count' => $successCount,
                'message' => "成功收藏 {$successCount} 个视频"
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 批量取消收藏视频
     *
     * @param int $userId
     * @param array $videoIds
     * @return Response
     */
    private function batchUnfavorite(int $userId, array $videoIds): Response
    {
        Db::startTrans();
        try {
            $successCount = 0;
            $failedIds = [];
            $errors = [];

            foreach ($videoIds as $videoId) {
                // 检查收藏记录是否存在
                $existingCollection = Db::table('video_collections')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->find();

                if (!$existingCollection) {
                    $failedIds[] = $videoId;
                    $errors[$videoId] = '还没有收藏';
                    continue;
                }

                // 删除收藏记录
                Db::table('video_collections')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->delete();

                // 更新视频收藏数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->dec('collect_count', 1)
                    ->update();

                $successCount++;
            }

            Db::commit();

            return $this->responseService->success([
                'success' => array_diff($videoIds, $failedIds),
                'failed' => $failedIds,
                'errors' => $errors,
                'success_count' => $successCount,
                'message' => "成功取消收藏 {$successCount} 个视频"
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 批量点赞视频
     *
     * @param int $userId
     * @param array $videoIds
     * @return Response
     */
    private function batchLike(int $userId, array $videoIds): Response
    {
        Db::startTrans();
        try {
            $successCount = 0;
            $failedIds = [];
            $errors = [];

            foreach ($videoIds as $videoId) {
                // 检查视频是否存在
                $video = Db::table('videos')
                    ->where('id', $videoId)
                    ->where('status', 'published')
                    ->find();

                if (!$video) {
                    $failedIds[] = $videoId;
                    $errors[$videoId] = '视频不存在';
                    continue;
                }

                // 检查是否已经点赞
                $existingLike = Db::table('video_likes')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->find();

                if ($existingLike) {
                    $failedIds[] = $videoId;
                    $errors[$videoId] = '已经点赞过了';
                    continue;
                }

                // 添加点赞记录
                Db::table('video_likes')->insert([
                    'video_id' => $videoId,
                    'user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // 更新视频点赞数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->inc('like_count', 1)
                    ->update();

                $successCount++;
            }

            Db::commit();

            return $this->responseService->success([
                'success' => array_diff($videoIds, $failedIds),
                'failed' => $failedIds,
                'errors' => $errors,
                'success_count' => $successCount,
                'message' => "成功点赞 {$successCount} 个视频"
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 批量取消点赞视频
     *
     * @param int $userId
     * @param array $videoIds
     * @return Response
     */
    private function batchUnlike(int $userId, array $videoIds): Response
    {
        Db::startTrans();
        try {
            $successCount = 0;
            $failedIds = [];
            $errors = [];

            foreach ($videoIds as $videoId) {
                // 检查点赞记录是否存在
                $existingLike = Db::table('video_likes')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->find();

                if (!$existingLike) {
                    $failedIds[] = $videoId;
                    $errors[$videoId] = '还没有点赞';
                    continue;
                }

                // 删除点赞记录
                Db::table('video_likes')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->delete();

                // 更新视频点赞数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->dec('like_count', 1)
                    ->update();

                $successCount++;
            }

            Db::commit();

            return $this->responseService->success([
                'success' => array_diff($videoIds, $failedIds),
                'failed' => $failedIds,
                'errors' => $errors,
                'success_count' => $successCount,
                'message' => "成功取消点赞 {$successCount} 个视频"
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 批量删除视频（仅限用户自己的视频）
     *
     * @param int $userId
     * @param array $videoIds
     * @return Response
     */
    private function batchDelete(int $userId, array $videoIds): Response
    {
        Db::startTrans();
        try {
            $successCount = 0;
            $failedIds = [];
            $errors = [];

            foreach ($videoIds as $videoId) {
                // 检查视频是否存在且属于当前用户
                $video = Db::table('videos')
                    ->where('id', $videoId)
                    ->where('user_id', $userId)
                    ->find();

                if (!$video) {
                    $failedIds[] = $videoId;
                    $errors[$videoId] = '视频不存在或无权限删除';
                    continue;
                }

                // 软删除视频（更新状态为deleted）
                Db::table('videos')
                    ->where('id', $videoId)
                    ->update([
                        'status' => 'deleted',
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                $successCount++;
            }

            Db::commit();

            return $this->responseService->success([
                'success' => array_diff($videoIds, $failedIds),
                'failed' => $failedIds,
                'errors' => $errors,
                'success_count' => $successCount,
                'message' => "成功删除 {$successCount} 个视频"
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
}
