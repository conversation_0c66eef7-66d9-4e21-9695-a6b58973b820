<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 视频处理状态服务
 * 
 * 管理视频处理的各个阶段状态：
 * - 上传状态跟踪
 * - 转码进度管理
 * - 审核状态更新
 * - 缩略图生成状态
 */
class VideoProcessingStatusService
{
    /**
     * 处理状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * 处理类型常量
     */
    const TYPE_UPLOAD = 'upload';
    const TYPE_TRANSCODE = 'transcode';
    const TYPE_AUDIT = 'audit';
    const TYPE_THUMBNAIL = 'thumbnail';

    /**
     * 创建处理状态记录
     */
    public function createProcessingStatus(int $videoId, string $processType, array $options = []): int
    {
        $data = [
            'video_id' => $videoId,
            'process_type' => $processType,
            'status' => $options['status'] ?? self::STATUS_PENDING,
            'current_step' => $options['current_step'] ?? null,
            'progress' => $options['progress'] ?? 0,
            'message' => $options['message'] ?? null,
            'error_message' => $options['error_message'] ?? null,
            'started_at' => $options['started_at'] ?? null,
            'completed_at' => $options['completed_at'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $statusId = Db::table('video_processing_status')->insertGetId($data);

        Log::info('创建视频处理状态记录', [
            'status_id' => $statusId,
            'video_id' => $videoId,
            'process_type' => $processType
        ]);

        return $statusId;
    }

    /**
     * 更新处理状态
     */
    public function updateProcessingStatus(int $statusId, array $updates): bool
    {
        $updates['updated_at'] = date('Y-m-d H:i:s');

        // 如果状态变为processing，设置开始时间
        if (isset($updates['status']) && $updates['status'] === self::STATUS_PROCESSING && !isset($updates['started_at'])) {
            $updates['started_at'] = date('Y-m-d H:i:s');
        }

        // 如果状态变为completed或failed，设置完成时间
        if (isset($updates['status']) && in_array($updates['status'], [self::STATUS_COMPLETED, self::STATUS_FAILED]) && !isset($updates['completed_at'])) {
            $updates['completed_at'] = date('Y-m-d H:i:s');
        }

        $result = Db::table('video_processing_status')
            ->where('id', $statusId)
            ->update($updates);

        Log::info('更新视频处理状态', [
            'status_id' => $statusId,
            'updates' => $updates,
            'result' => $result
        ]);

        return $result > 0;
    }

    /**
     * 根据视频ID和处理类型更新状态
     */
    public function updateStatusByVideoAndType(int $videoId, string $processType, array $updates): bool
    {
        $updates['updated_at'] = date('Y-m-d H:i:s');

        // 如果状态变为processing，设置开始时间
        if (isset($updates['status']) && $updates['status'] === self::STATUS_PROCESSING && !isset($updates['started_at'])) {
            $updates['started_at'] = date('Y-m-d H:i:s');
        }

        // 如果状态变为completed或failed，设置完成时间
        if (isset($updates['status']) && in_array($updates['status'], [self::STATUS_COMPLETED, self::STATUS_FAILED]) && !isset($updates['completed_at'])) {
            $updates['completed_at'] = date('Y-m-d H:i:s');
        }

        $result = Db::table('video_processing_status')
            ->where('video_id', $videoId)
            ->where('process_type', $processType)
            ->update($updates);

        Log::info('根据视频ID和类型更新处理状态', [
            'video_id' => $videoId,
            'process_type' => $processType,
            'updates' => $updates,
            'result' => $result
        ]);

        return $result > 0;
    }

    /**
     * 获取视频的所有处理状态
     */
    public function getVideoProcessingStatus(int $videoId): array
    {
        $statuses = Db::table('video_processing_status')
            ->where('video_id', $videoId)
            ->order('created_at', 'asc')
            ->select();

        return $statuses ? $statuses->toArray() : [];
    }

    /**
     * 获取特定类型的处理状态
     */
    public function getProcessingStatusByType(int $videoId, string $processType): ?array
    {
        $status = Db::table('video_processing_status')
            ->where('video_id', $videoId)
            ->where('process_type', $processType)
            ->find();

        return $status ? $status->toArray() : null;
    }

    /**
     * 批量获取视频处理状态
     */
    public function getBatchProcessingStatus(array $videoIds): array
    {
        if (empty($videoIds)) {
            return [];
        }

        $statuses = Db::table('video_processing_status')
            ->whereIn('video_id', $videoIds)
            ->order('video_id', 'asc')
            ->order('created_at', 'asc')
            ->select();

        $result = [];
        foreach ($statuses as $status) {
            $videoId = $status['video_id'];
            if (!isset($result[$videoId])) {
                $result[$videoId] = [];
            }
            $result[$videoId][] = $status;
        }

        return $result;
    }

    /**
     * 获取处理进度摘要
     */
    public function getProcessingSummary(int $videoId): array
    {
        $statuses = $this->getVideoProcessingStatus($videoId);

        $summary = [
            'video_id' => $videoId,
            'overall_status' => 'pending',
            'overall_progress' => 0,
            'steps' => [],
            'current_step' => null,
            'estimated_completion' => null,
            'error_message' => null
        ];

        if (empty($statuses)) {
            return $summary;
        }

        // 分离技术处理步骤和审核步骤
        $technicalSteps = [];
        $auditSteps = [];

        foreach ($statuses as $status) {
            if ($status['process_type'] === 'audit') {
                $auditSteps[] = $status;
            } else {
                $technicalSteps[] = $status;
            }
        }

        // 只基于技术处理步骤计算整体状态
        $totalSteps = count($technicalSteps);
        $completedSteps = 0;
        $hasError = false;
        $currentProcessing = null;

        // 处理所有步骤信息（包括审核）
        foreach ($statuses as $status) {
            $stepInfo = [
                'type' => $status['process_type'],
                'status' => $status['status'],
                'progress' => $status['progress'],
                'current_step' => $status['current_step'],
                'message' => $status['message'],
                'started_at' => $status['started_at'],
                'completed_at' => $status['completed_at']
            ];

            $summary['steps'][] = $stepInfo;
        }

        // 只统计技术处理步骤的状态
        foreach ($technicalSteps as $status) {
            if ($status['status'] === self::STATUS_COMPLETED) {
                $completedSteps++;
            } elseif ($status['status'] === self::STATUS_FAILED) {
                $hasError = true;
                $summary['error_message'] = $status['error_message'];
            } elseif ($status['status'] === self::STATUS_PROCESSING) {
                $currentProcessing = $status;
                $summary['current_step'] = $status['process_type'] . ': ' . ($status['current_step'] ?? '处理中');
            }
        }

        // 计算整体状态和进度（只基于技术处理步骤）
        if ($totalSteps === 0) {
            // 没有技术处理步骤，直接标记为完成
            $summary['overall_status'] = 'completed';
            $summary['overall_progress'] = 100;
        } elseif ($hasError) {
            $summary['overall_status'] = 'failed';
        } elseif ($completedSteps === $totalSteps) {
            $summary['overall_status'] = 'completed';
            $summary['overall_progress'] = 100;
        } elseif ($currentProcessing) {
            $summary['overall_status'] = 'processing';
            $summary['overall_progress'] = intval(($completedSteps / $totalSteps) * 100 + ($currentProcessing['progress'] / $totalSteps));
        } else {
            // 有pending状态的技术处理步骤，显示等待处理
            $summary['overall_status'] = 'pending';
            $summary['overall_progress'] = intval(($completedSteps / $totalSteps) * 100);

            // 找到下一个待处理的技术步骤
            foreach ($technicalSteps as $status) {
                if ($status['status'] === self::STATUS_PENDING) {
                    $summary['current_step'] = $status['process_type'] . ': 等待处理';
                    break;
                }
            }
        }

        return $summary;
    }

    /**
     * 清理过期的处理状态记录
     */
    public function cleanupExpiredStatus(int $daysOld = 30): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        
        $count = Db::table('video_processing_status')
            ->where('status', 'in', [self::STATUS_COMPLETED, self::STATUS_FAILED])
            ->where('completed_at', '<', $cutoffDate)
            ->delete();

        Log::info('清理过期处理状态记录', [
            'cutoff_date' => $cutoffDate,
            'deleted_count' => $count
        ]);

        return $count;
    }
}
