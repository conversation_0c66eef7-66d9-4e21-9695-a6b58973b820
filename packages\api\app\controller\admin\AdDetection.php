<?php
declare(strict_types=1);

namespace app\controller\admin;

use app\BaseController;
use app\service\AdDetectionService;
use app\service\ResponseService;
use app\model\Video;
use think\Request;
use think\Response;
use think\facade\Log;

/**
 * 广告检测管理控制器
 */
class AdDetection extends BaseController
{
    private AdDetectionService $adDetectionService;
    private ResponseService $responseService;

    protected function initialize()
    {
        parent::initialize();
        $this->adDetectionService = new AdDetectionService();
        $this->responseService = new ResponseService();
    }

    /**
     * 检测单个视频广告
     */
    public function detectVideo(): Response
    {
        try {
            $videoId = $this->request->param('video_id');
            $forceRedetect = $this->request->param('force_redetect', false);

            if (!$videoId) {
                return $this->responseService->error('视频ID不能为空');
            }

            $result = $this->adDetectionService->detectVideoAds((int)$videoId, (bool)$forceRedetect);

            if ($result['success']) {
                return $this->responseService->success($result['data'], '广告检测完成');
            } else {
                return $this->responseService->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('广告检测失败', [
                'video_id' => $videoId ?? null,
                'error' => $e->getMessage()
            ]);
            return $this->responseService->error('广告检测失败：' . $e->getMessage());
        }
    }

    /**
     * 批量检测视频广告
     */
    public function batchDetect(): Response
    {
        try {
            $videoIds = $this->request->param('video_ids', []);
            $forceRedetect = $this->request->param('force_redetect', false);

            if (empty($videoIds) || !is_array($videoIds)) {
                return $this->responseService->error('视频ID列表不能为空');
            }

            // 限制批量处理数量
            if (count($videoIds) > 100) {
                return $this->responseService->error('单次最多只能处理100个视频');
            }

            $result = $this->adDetectionService->batchDetectAds($videoIds, (bool)$forceRedetect);

            return $this->responseService->success($result['data'], '批量检测完成');
        } catch (\Exception $e) {
            Log::error('批量广告检测失败', [
                'video_ids' => $videoIds ?? [],
                'error' => $e->getMessage()
            ]);
            return $this->responseService->error('批量检测失败：' . $e->getMessage());
        }
    }

    /**
     * 手动标记视频广告状态
     */
    public function manualMark(): Response
    {
        try {
            $videoId = $this->request->param('video_id');
            $hasAds = $this->request->param('has_ads');
            $adSegments = $this->request->param('ad_segments', []);

            if (!$videoId) {
                return $this->responseService->error('视频ID不能为空');
            }

            if ($hasAds === null) {
                return $this->responseService->error('请指定是否包含广告');
            }

            $result = $this->adDetectionService->manualMarkAds(
                (int)$videoId, 
                (bool)$hasAds, 
                is_array($adSegments) ? $adSegments : []
            );

            if ($result['success']) {
                return $this->responseService->success($result['data'], '标记成功');
            } else {
                return $this->responseService->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('手动标记广告状态失败', [
                'video_id' => $videoId ?? null,
                'error' => $e->getMessage()
            ]);
            return $this->responseService->error('标记失败：' . $e->getMessage());
        }
    }

    /**
     * 获取广告检测统计信息
     */
    public function getStatistics(): Response
    {
        try {
            $stats = [
                'total_videos' => Video::count(),
                'videos_with_ads' => Video::where('has_ads', 1)->count(),
                'videos_without_ads' => Video::where('has_ads', 0)->count(),
                'pending_detection' => Video::where('ad_detection_status', 'pending')->count(),
                'detected_videos' => Video::where('ad_detection_status', 'detected')->count(),
                'manually_marked' => Video::where('ad_detection_status', 'manual')->count(),
                'collected_videos' => Video::where('source_type', 'collect')->count(),
                'high_confidence' => Video::where('ad_confidence_score', '>=', 0.8)->count(),
                'medium_confidence' => Video::whereBetween('ad_confidence_score', [0.5, 0.8])->count(),
                'low_confidence' => Video::whereBetween('ad_confidence_score', [0.3, 0.5])->count(),
            ];

            // 计算百分比
            $total = $stats['total_videos'];
            if ($total > 0) {
                $stats['ad_detection_rate'] = round(($stats['videos_with_ads'] + $stats['videos_without_ads']) / $total * 100, 2);
                $stats['ad_percentage'] = round($stats['videos_with_ads'] / $total * 100, 2);
            } else {
                $stats['ad_detection_rate'] = 0;
                $stats['ad_percentage'] = 0;
            }

            return $this->responseService->success($stats, '获取统计信息成功');
        } catch (\Exception $e) {
            Log::error('获取广告检测统计失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('获取统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取待检测的视频列表
     */
    public function getPendingVideos(): Response
    {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $sourceType = $this->request->param('source_type', '');

            $query = Video::where('ad_detection_status', 'pending')
                ->order('created_at', 'desc');

            if ($sourceType) {
                $query->where('source_type', $sourceType);
            }

            $videos = $query->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

            return $this->responseService->success($videos, '获取待检测视频列表成功');
        } catch (\Exception $e) {
            Log::error('获取待检测视频列表失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('获取列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取有广告的视频列表
     */
    public function getAdsVideos(): Response
    {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $confidenceLevel = $this->request->param('confidence_level', '');

            $query = Video::where('has_ads', 1)
                ->order('ad_confidence_score', 'desc');

            // 按置信度筛选
            if ($confidenceLevel) {
                switch ($confidenceLevel) {
                    case 'high':
                        $query->where('ad_confidence_score', '>=', 0.8);
                        break;
                    case 'medium':
                        $query->whereBetween('ad_confidence_score', [0.5, 0.8]);
                        break;
                    case 'low':
                        $query->whereBetween('ad_confidence_score', [0.3, 0.5]);
                        break;
                }
            }

            $videos = $query->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

            return $this->responseService->success($videos, '获取广告视频列表成功');
        } catch (\Exception $e) {
            Log::error('获取广告视频列表失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('获取列表失败：' . $e->getMessage());
        }
    }

    /**
     * 自动检测所有待检测的视频
     */
    public function autoDetectPending(): Response
    {
        try {
            $limit = $this->request->param('limit', 50); // 限制单次处理数量

            $pendingVideos = Video::where('ad_detection_status', 'pending')
                ->limit($limit)
                ->column('id');

            if (empty($pendingVideos)) {
                return $this->responseService->success([], '没有待检测的视频');
            }

            $result = $this->adDetectionService->batchDetectAds($pendingVideos, false);

            return $this->responseService->success($result['data'], "已处理 {$limit} 个视频的广告检测");
        } catch (\Exception $e) {
            Log::error('自动检测待检测视频失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('自动检测失败：' . $e->getMessage());
        }
    }

    /**
     * 重置视频广告检测状态
     */
    public function resetDetection(): Response
    {
        try {
            $videoId = $this->request->param('video_id');

            if (!$videoId) {
                return $this->responseService->error('视频ID不能为空');
            }

            $video = Video::find($videoId);
            if (!$video) {
                return $this->responseService->error('视频不存在');
            }

            $video->has_ads = null;
            $video->ad_segments = null;
            $video->ad_detection_status = 'pending';
            $video->ad_detection_method = null;
            $video->ad_keywords_matched = null;
            $video->ad_confidence_score = null;
            $video->ad_last_detected_at = null;
            $video->save();

            return $this->responseService->success(['video_id' => $videoId], '重置成功');
        } catch (\Exception $e) {
            Log::error('重置广告检测状态失败', [
                'video_id' => $videoId ?? null,
                'error' => $e->getMessage()
            ]);
            return $this->responseService->error('重置失败：' . $e->getMessage());
        }
    }
}
