<?php
/**
 * API密钥配置文件
 * 统一管理所有API密钥，替代CORS认证
 */

return [
    // 是否启用API密钥认证
    'enabled' => env('API_KEY_AUTH_ENABLED', true),
    
    // 默认速率限制（每小时）
    'default_rate_limit' => env('API_DEFAULT_RATE_LIMIT', 1000),
    
    // 密钥配置
    'keys' => [
        // 管理后台密钥
        'admin_frontend' => [
            'key' => env('API_KEY_ADMIN', 'ShiPinAdmin2024ProductionKey32Bytes!@#$%^&*()_+'),
            'name' => '管理后台',
            'permissions' => ['admin.*', 'video.*', 'user.*', 'collect.*', 'system.*'],
            'rate_limit' => 2000,
            'allowed_ips' => [], // 空数组表示不限制IP
            'expires_at' => null // null表示永不过期
        ],

        // 用户前端客户端标识（用于识别请求来源，不用于认证）
        'user_frontend' => [
            'key' => env('API_KEY_USER', ''),
            'name' => '用户前端',
            'permissions' => ['public.*'], // 仅用于公开接口
            'rate_limit' => 1000,
            'allowed_ips' => [],
            'expires_at' => null,
            'auth_required' => false // 标识此密钥不用于认证
        ],

        // 移动端密钥
        'mobile_app' => [
            'key' => env('API_KEY_MOBILE', 'ShiPinMobile2024ProductionKey32Bytes!@#$%^&*()_+'),
            'name' => '移动端应用',
            'permissions' => ['video.list', 'video.detail', 'video.search', 'user.*'],
            'rate_limit' => 500,
            'allowed_ips' => [],
            'expires_at' => null
        ],

        // 采集系统密钥
        'collect_system' => [
            'key' => env('API_KEY_COLLECT', 'ShiPinCollect2024ProductionKey32Bytes!@#$%^&*()_+'),
            'name' => '采集系统',
            'permissions' => ['collect.*', 'video.create', 'video.update', 'category.*'],
            'rate_limit' => 5000,
            'allowed_ips' => ['127.0.0.1', '************'], // 限制服务器IP
            'expires_at' => null
        ],

        // 第三方API密钥
        'third_party' => [
            'key' => env('API_KEY_THIRD_PARTY', 'ShiPinThirdParty2024ProductionKey32Bytes!@#$%^&*()_+'),
            'name' => '第三方接口',
            'permissions' => ['video.list', 'video.detail', 'video.search'],
            'rate_limit' => 200,
            'allowed_ips' => [],
            'expires_at' => '2025-12-31 23:59:59'
        ],

        // 开发测试密钥（仅开发环境）
        'development' => [
            'key' => env('API_KEY_DEV', 'ShiPinDev2024ProductionKey32Bytes!@#$%^&*()_+'),
            'name' => '开发测试',
            'permissions' => ['*'], // 开发环境全权限
            'rate_limit' => 10000,
            'allowed_ips' => [],
            'expires_at' => null,
            'enabled' => env('APP_DEBUG', false) // 仅在调试模式下启用
        ]
    ],
    
    // 不需要API密钥的公开路由
    'public_routes' => [
        '/health',
        '/simple-test',
        '/api/public/*',
        '/storage/*', // 静态文件
        '/uploads/*', // 上传文件
        '/hls/*'      // HLS视频流
    ],
    
    // 日志配置
    'logging' => [
        'enabled' => env('API_KEY_LOGGING', true),
        'log_failed_attempts' => true,
        'log_rate_limit_exceeded' => true,
        'log_successful_requests' => env('APP_DEBUG', false) // 仅调试模式记录成功请求
    ],
    
    // 缓存配置
    'cache' => [
        'rate_limit_prefix' => 'api_rate_limit:',
        'key_validation_prefix' => 'api_key_valid:',
        'cache_ttl' => 3600 // 1小时
    ]
];
