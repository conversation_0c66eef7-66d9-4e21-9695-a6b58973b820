<template>
  <div class="tiktok-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <!-- 汉堡菜单按钮 -->
      <button class="hamburger-btn" @click="toggleMenu">
        <svg viewBox="0 0 24 24" fill="white" width="24" height="24">
          <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
        </svg>
      </button>

      <!-- 导航标签 -->
      <div class="nav-tabs">
        <span
          class="nav-tab"
          :class="{ active: activeTab === 'recommend' }"
          @click="switchTab('recommend')"
        >
          推荐
        </span>
        <span
          class="nav-tab"
          :class="{ active: activeTab === 'latest' }"
          @click="switchTab('latest')"
        >
          最新
        </span>
        <span
          v-for="category in categories"
          :key="category.id"
          class="nav-tab"
          :class="{ active: activeTab === `category_${category.id}` }"
          @click="switchTab(`category_${category.id}`)"
        >
          {{ category.name }}
        </span>
      </div>

      <!-- 搜索按钮 -->
      <button class="search-btn" @click="openSearch">
        <svg viewBox="0 0 24 24" fill="white" width="24" height="24">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      </button>
    </div>

    <!-- 视频列表 -->
    <div
      class="video-list"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @wheel="handleWheel"
    >
      <div
        v-for="(video, index) in videos"
        :key="video.id"
        class="video-item"
        :class="{ 'active': index === currentIndex }"
        :style="{ transform: `translateY(${(index - currentIndex) * 100}vh)` }"
      >
        <!-- 简化的视频播放器 -->
        <VideoPlayer
          :ref="el => setVideoRef(el, index)"
          :src="video.videoUrl"
          :autoplay="index === currentIndex"
          :muted="true"
          :player-id="`player-${video.id}`"
          class="main-video"
          @play="onVideoPlay(index)"
          @pause="onVideoPause(index)"
          @error="onVideoError(index)"
          @timeupdate="onTimeUpdate(index, $event)"
          @canplay="onVideoCanPlay(index)"
          @loadstart="onVideoLoaded(index)"
          @ready="onVideoLoaded(index)"
          @click="handleVideoClick"
          @dblclick="handleDoubleClick"
        />

        <!-- 播放按钮 -->
        <div v-if="!isPlaying && currentIndex === index" class="play-btn" @click="togglePlay">
          <svg viewBox="0 0 24 24" fill="white" width="80" height="80">
            <path d="M8 5v14l11-7z"/>
          </svg>
        </div>

        <!-- 加载状态 -->
        <div v-if="video.isLoading" class="loading">
          <div class="spinner"></div>
          <p>加载中...</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="video.hasError" class="error">
          <p>视频加载失败</p>
          <button @click="retryVideo(index)">重试</button>
        </div>

        <!-- 右侧操作栏 -->
        <div class="right-sidebar">
          <!-- 用户头像 -->
          <div class="user-avatar">
            <img
              :src="getAvatarUrl(video.author.avatar)"
              :alt="video.author.name"
              @error="handleAvatarError"
            >
            <div class="follow-btn" v-if="!video.author.isFollowing" @click="followUser(index)">+</div>
          </div>

          <!-- 点赞 -->
          <div class="action-item" @click="toggleLike(index)">
            <div class="action-icon" :class="{ liked: video.isLiked }">
              <svg viewBox="0 0 24 24" fill="currentColor" width="32" height="32">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
            </div>
            <span class="action-count">{{ formatCount(video.likes) }}</span>
          </div>

          <!-- 评论 -->
          <div class="action-item" @click="showComments(index)">
            <div class="action-icon">
              <svg viewBox="0 0 24 24" fill="currentColor" width="32" height="32">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
              </svg>
            </div>
            <span class="action-count">{{ formatCount(video.comments) }}</span>
          </div>

          <!-- 收藏 -->
          <div class="action-item" @click="toggleFavorite(index)">
            <div class="action-icon" :class="{ favorited: video.isFavorited }">
              <svg viewBox="0 0 24 24" fill="currentColor" width="32" height="32">
                <path d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
              </svg>
            </div>
            <span class="action-count">收藏</span>
          </div>

          <!-- 分享 -->
          <div class="action-item" @click="shareVideo(index)">
            <div class="action-icon">
              <svg viewBox="0 0 24 24" fill="currentColor" width="32" height="32">
                <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
              </svg>
            </div>
            <span class="action-count">{{ formatCount(video.shares) }}</span>
          </div>


        </div>

        <!-- 底部信息栏 - 确保不被底部导航栏遮挡 -->
        <div class="bottom-info">
          <div class="user-info">
            <span class="username">@{{ video.author.name }}</span>
            <span class="description">{{ video.description }}</span>
          </div>


        </div>
      </div>
    </div>

    <!-- 播放进度条 - 位于底部导航栏上方 -->
    <div
      class="progress-container"
      :class="{ 'progress-visible': showProgressBar }"
      @click="handleProgressClick"
      @touchstart="handleProgressTouchStart"
      @touchmove="handleProgressTouchMove"
      @touchend="handleProgressTouchEnd"
      @mousedown="handleProgressMouseDown"
      @mousemove="handleProgressMouseMove"
      @mouseup="handleProgressMouseUp"
    >
      <div
        class="progress-track"
        :style="{ width: '100%' }"
      ></div>
      <div
        class="progress-bar"
        :style="{ width: `${currentProgress}%` }"
      ></div>
      <div
        v-if="showProgressBar"
        class="progress-thumb"
        :style="{ left: `${currentProgress}%` }"
      ></div>
    </div>

    <!-- 底部导航栏占位 - 模拟真实的底部导航栏空间 -->
    <div class="bottom-nav-placeholder">
      <!-- 这里可以放置实际的底部导航内容 -->
      <div class="nav-item">首页</div>
      <div class="nav-item">发现</div>
      <div class="nav-item">+</div>
      <div class="nav-item">消息</div>
      <div class="nav-item">我</div>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info" v-if="showDebug">
      <p>当前视频: {{ currentIndex }}</p>
      <p>总视频数: {{ videos.length }}</p>
      <p>播放状态: {{ isPlaying ? '播放中' : '暂停' }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { videoApi } from '@/api/video'
import { ElMessage, ElMessageBox } from 'element-plus'
import VideoPlayer from '@/components/VideoPlayer.vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const currentIndex = ref(0)
const isPlaying = ref(false)
const videos = ref<any[]>([])
const playerRefs = ref<{ [key: number]: any }>({})
const showDebug = ref(false)
const categories = ref<any[]>([])
const activeTab = ref('recommend')
const currentProgress = ref(0) // 播放进度百分比
const showProgressBar = ref(false) // 是否显示可拖拽的进度条
const isDraggingProgress = ref(false) // 是否正在拖拽进度条

// 触摸相关
const touchStartY = ref(0)
const isDragging = ref(false)

// 双指缩放相关
const isZooming = ref(false)
const initialDistance = ref(0)
const currentScale = ref(1)
const maxScale = 3
const minScale = 1

// 进度条自动隐藏定时器
let progressBarTimer: NodeJS.Timeout | null = null

// 设置播放器引用
const setVideoRef = (el: any, index: number) => {
  if (el) {
    playerRefs.value[index] = el
    console.log(`🎬 设置播放器引用 ${index}`)
  }
}

// 加载分类数据
const loadCategories = async () => {
  try {
    console.log('🔄 开始加载分类数据...')
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    const response = await fetch(`${apiBaseUrl}/api/v1/videos/short-categories`, {
      method: 'GET',
      headers: {
        // 移除Content-Type以避免CORS预检请求
        'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+'
      }
    })

    console.log('📡 分类API响应状态:', response.status)

    if (response.ok) {
      const data = await response.json()
      console.log('📊 分类API响应数据:', data)

      if (data.success && data.data) {
        categories.value = data.data
        console.log('✅ 成功加载分类:', categories.value.length, '个分类')
      } else {
        console.warn('⚠️ 分类API返回数据格式异常:', data)
        throw new Error('分类数据格式异常')
      }
    } else {
      console.error('❌ 分类API请求失败:', response.status, response.statusText)
      throw new Error(`API请求失败: ${response.status}`)
    }
  } catch (error) {
    console.error('❌ 加载分类失败:', error)
    // 使用默认分类
    categories.value = [
      { id: 1, name: '搞笑' },
      { id: 2, name: '美食' },
      { id: 3, name: '音乐' },
      { id: 4, name: '舞蹈' }
    ]
    console.log('🔄 使用默认分类数据')
  }
}

// 切换标签
const switchTab = async (tab: string) => {
  activeTab.value = tab
  console.log('🔄 切换到标签:', tab)

  // 根据标签加载不同的视频
  if (tab === 'recommend') {
    await loadVideos()
  } else if (tab === 'latest') {
    await loadVideos('latest')
  } else if (tab.startsWith('category_')) {
    const categoryId = tab.replace('category_', '')
    await loadVideos('category', categoryId)
  }
}

// 加载视频数据
const loadVideos = async (type: string = 'recommend', categoryId?: string) => {
  try {
    console.log('🎬 开始加载短视频数据...', { type, categoryId })

    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    let url = `${apiBaseUrl}/api/v1/videos/short-videos`
    const params = new URLSearchParams()

    if (type === 'category' && categoryId) {
      params.append('category_id', categoryId)
    }

    if (params.toString()) {
      url += '?' + params.toString()
    }

    const response = await fetch(url, {
      headers: {
        'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+'
      }
    })

    console.log('📡 API响应状态:', response.status)

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }

    const data = await response.json()
    console.log('📦 API响应数据:', data)

    if (data.success && data.data && Array.isArray(data.data)) {
      videos.value = data.data.map((video: any, index: number) => {
        // 生成视频URL - 短视频优先使用HLS切片播放以获得更好性能
        let videoUrl = ''
        if (video.hls_url && video.has_hls) {
          // 优先使用HLS格式，提供更快更丝滑的播放体验
          const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
          if (video.hls_url.startsWith('http')) {
            videoUrl = video.hls_url
          } else {
            // 处理相对路径的HLS URL
            const path = video.hls_url.startsWith('/') ? video.hls_url : `/${video.hls_url}`
            videoUrl = `${apiBaseUrl}${path}`
          }
          console.log(`📺 视频 ${video.id} 使用HLS格式:`, videoUrl)
        } else if (video.file_path) {
          // 备用MP4格式（仅在没有HLS时使用）
          const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
          const path = video.file_path.startsWith('/') ? video.file_path : `/${video.file_path}`
          videoUrl = `${apiBaseUrl}${path}`
          console.log(`📺 视频 ${video.id} 备用MP4格式:`, videoUrl)
        }

        return {
          id: video.id,
          videoUrl: videoUrl,
          coverUrl: video.cover_image ? `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'}${video.cover_image.startsWith('/') ? video.cover_image : `/${video.cover_image}`}` : '/default-cover.svg',
          description: video.description || video.title || `精彩短视频 ${index + 1}`,
          author: {
            id: video.user_id || 1,
            name: video.nickname || video.username || `用户${video.user_id || 1}`,
            avatar: video.user_avatar ? `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'}${video.user_avatar}` : '/images/default-avatar.svg',
            isFollowing: false
          },
          likes: video.like_count || 0,
          comments: video.comment_count || 0,
          shares: video.share_count || 0,
          isLiked: video.is_liked || false,
          isFavorited: video.is_favorited || false,
          isLoading: false,
          hasError: false,
          hasHls: video.has_hls || false,
          // 防止重复点击的状态
          isLiking: false,
          isFavoriting: false
        }
      })

      console.log(`✅ 成功加载 ${videos.value.length} 个视频`)
    } else {
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 加载视频失败:', error)

    // 创建测试数据
    videos.value = [
      {
        id: 1,
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
        coverUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
        description: '精彩短视频 - 大自然的美丽瞬间 🌸',
        author: { id: 1, name: '自然摄影师', avatar: 'https://via.placeholder.com/50', isFollowing: false },
        likes: 12340, comments: 567, shares: 123, isLiked: false, isFavorited: false, isLoading: false, hasError: false, isLiking: false, isFavoriting: false
      },
      {
        id: 2,
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
        coverUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ElephantsDream.jpg',
        description: '创意动画短片 - 梦境奇遇记 ✨',
        author: { id: 2, name: '动画创作者', avatar: 'https://via.placeholder.com/50', isFollowing: false },
        likes: 23450, comments: 789, shares: 234, isLiked: false, isFavorited: false, isLoading: false, hasError: false, isLiking: false, isFavoriting: false
      },
      {
        id: 3,
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
        coverUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerBlazes.jpg',
        description: '震撼视觉效果 - 火焰与激情 🔥',
        author: { id: 3, name: '特效大师', avatar: 'https://via.placeholder.com/50', isFollowing: false },
        likes: 34560, comments: 890, shares: 345, isLiked: false, isFavorited: false, isLoading: false, hasError: false, isLiking: false, isFavoriting: false
      }
    ]

    console.log('🎯 使用测试数据:', videos.value.length, '个视频')
  }

  // Video.js会自动初始化第一个视频
  if (videos.value.length > 0) {
    await nextTick()
    // 延迟自动播放，确保播放器准备就绪
    setTimeout(() => {
      autoPlayCurrentVideo()
    }, 1000)
  }
}

// Video.js会自动处理视频初始化，包括HLS流

// 处理视频点击 - 显示进度条或播放/暂停
const handleVideoClick = (event: MouseEvent) => {
  // 如果点击的是进度条区域，不处理
  if ((event.target as HTMLElement).closest('.progress-container')) {
    return
  }

  // 显示进度条
  showProgressBar.value = true

  // 清除之前的定时器
  if (progressBarTimer) {
    clearTimeout(progressBarTimer)
  }

  // 3秒后自动隐藏进度条
  progressBarTimer = setTimeout(() => {
    if (!isDraggingProgress.value) {
      showProgressBar.value = false
    }
  }, 3000)

  // 播放/暂停控制
  togglePlay()
}

// 处理双击点赞
const handleDoubleClick = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()

  // 双击点赞当前视频
  toggleLike(currentIndex.value)

  // 显示点赞动画效果
  showLikeAnimation()
}

// 显示点赞动画
const showLikeAnimation = () => {
  // 这里可以添加点赞动画效果
  console.log('❤️ 双击点赞动画')
}

// 视频事件处理
const onVideoPlay = (index: number) => {
  if (index === currentIndex.value) {
    isPlaying.value = true
    console.log(`▶️ 视频 ${index} 开始播放`)
  }
}

const onVideoPause = (index: number) => {
  if (index === currentIndex.value) {
    isPlaying.value = false
    console.log(`⏸️ 视频 ${index} 暂停`)
  }
}

// 播放/暂停控制
const togglePlay = async () => {
  const player = playerRefs.value[currentIndex.value]
  if (!player) return

  try {
    if (isPlaying.value) {
      player.pause()
      console.log(`⏸️ 暂停视频 ${currentIndex.value}`)
    } else {
      await player.play()
      console.log(`▶️ 播放视频 ${currentIndex.value}`)
    }
  } catch (error) {
    console.error(`❌ 播放控制失败:`, error)
  }
}

// 播放器事件处理
const onPlayerReady = (index: number, player: any) => {
  console.log(`🎬 播放器 ${index} 准备就绪`)
  // 如果是当前视频，自动播放
  if (index === currentIndex.value) {
    setTimeout(() => {
      autoPlayCurrentVideo()
    }, 500)
  }
}

// 切换到指定视频
const switchToVideo = async (targetIndex: number) => {
  if (targetIndex < 0 || targetIndex >= videos.value.length) return

  console.log(`🔄 切换到视频 ${targetIndex}`)

  // 暂停当前播放器
  const currentPlayer = playerRefs.value[currentIndex.value]
  if (currentPlayer) {
    currentPlayer.pause()
    isPlaying.value = false
  }

  // 更新索引
  currentIndex.value = targetIndex

  // 等待DOM更新
  await nextTick()

  // 延迟自动播放，确保播放器准备就绪
  setTimeout(async () => {
    await autoPlayCurrentVideo()
  }, 500)
}

// 自动播放当前视频
const autoPlayCurrentVideo = async () => {
  const player = playerRefs.value[currentIndex.value]
  if (!player) return

  try {
    await player.play()
    console.log(`▶️ 自动播放视频 ${currentIndex.value}`)
  } catch (error) {
    console.error(`❌ 自动播放失败:`, error)
    // 如果自动播放失败，可能是浏览器策略限制
    isPlaying.value = false
  }
}

// 触摸事件处理
const handleTouchStart = (e: TouchEvent) => {
  if (e.touches.length === 2) {
    // 双指触摸，开始缩放
    isZooming.value = true
    initialDistance.value = getDistance(e.touches[0], e.touches[1])
    e.preventDefault()
  } else if (e.touches.length === 1 && !isZooming.value) {
    // 单指触摸，准备滑动
    touchStartY.value = e.touches[0].clientY
    isDragging.value = true
  }
}

const handleTouchMove = (e: TouchEvent) => {
  if (e.touches.length === 2 && isZooming.value) {
    // 双指缩放
    e.preventDefault()
    const currentDistance = getDistance(e.touches[0], e.touches[1])
    const scale = Math.max(minScale, Math.min(maxScale, (currentDistance / initialDistance.value) * currentScale.value))

    // 应用缩放变换
    const videoContainer = e.currentTarget as HTMLElement
    const videoElement = videoContainer.querySelector('.main-video') as HTMLElement
    if (videoElement) {
      videoElement.style.transform = `scale(${scale})`
      videoElement.style.transformOrigin = 'center center'
    }
  } else if (e.touches.length === 1 && isDragging.value && !isZooming.value) {
    // 单指滑动
    e.preventDefault()
  }
}

const handleTouchEnd = (e: TouchEvent) => {
  if (isZooming.value) {
    // 结束缩放，恢复原样
    isZooming.value = false
    const videoContainer = e.currentTarget as HTMLElement
    const videoElement = videoContainer.querySelector('.main-video') as HTMLElement
    if (videoElement) {
      videoElement.style.transform = 'scale(1)'
      videoElement.style.transition = 'transform 0.3s ease'
      setTimeout(() => {
        videoElement.style.transition = ''
      }, 300)
    }
    currentScale.value = 1
    return
  }

  if (!isDragging.value) return

  const endY = e.changedTouches[0].clientY
  const deltaY = endY - touchStartY.value

  // 判断滑动方向
  if (Math.abs(deltaY) > 50) {
    if (deltaY > 0 && currentIndex.value > 0) {
      // 向下滑动，切换到上一个视频
      switchToVideo(currentIndex.value - 1)
    } else if (deltaY < 0 && currentIndex.value < videos.value.length - 1) {
      // 向上滑动，切换到下一个视频
      switchToVideo(currentIndex.value + 1)
    }
  }

  isDragging.value = false
}

// 计算两点间距离
const getDistance = (touch1: Touch, touch2: Touch): number => {
  const dx = touch1.clientX - touch2.clientX
  const dy = touch1.clientY - touch2.clientY
  return Math.sqrt(dx * dx + dy * dy)
}

// 获取头像URL
const getAvatarUrl = (avatarPath: string | null | undefined): string => {
  if (!avatarPath) {
    return '/images/default-avatar.svg'
  }

  if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
    return avatarPath
  }

  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
  const normalizedPath = avatarPath.startsWith('/') ? avatarPath : `/${avatarPath}`
  return `${apiBaseUrl}${normalizedPath}`
}

// 头像加载错误处理
const handleAvatarError = (event: Event) => {
  const target = event.target as HTMLImageElement
  // 防止无限循环：如果已经是默认头像了，就不再重新设置
  if (target.src.includes('default-avatar.svg')) {
    console.warn('默认头像加载失败，停止重试以防止循环')
    return
  }
  target.src = '/images/default-avatar.svg'
}

// 鼠标滚轮事件
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()

  if (e.deltaY > 0 && currentIndex.value < videos.value.length - 1) {
    switchToVideo(currentIndex.value + 1)
  } else if (e.deltaY < 0 && currentIndex.value > 0) {
    switchToVideo(currentIndex.value - 1)
  }
}

// 新增方法
const toggleMenu = () => {
  console.log('🍔 打开汉堡菜单')
  // 这里可以实现侧边菜单功能
}

const openSearch = () => {
  console.log('🔍 打开搜索')
  // 这里可以实现搜索功能
}

// 播放进度更新
const onTimeUpdate = (index: number, currentTime: number) => {
  if (index === currentIndex.value && !isDraggingProgress.value) {
    const player = playerRefs.value[index]
    if (player) {
      const duration = player.getDuration()
      if (duration > 0) {
        currentProgress.value = (currentTime / duration) * 100
      }
    }
  }
}

// 进度条点击处理
const handleProgressClick = (event: MouseEvent) => {
  event.stopPropagation()
  const player = playerRefs.value[currentIndex.value]
  if (!player) return

  const duration = player.getDuration()
  if (!duration) return

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = (clickX / rect.width) * 100
  const newTime = (percentage / 100) * duration

  player.seek(newTime)
  currentProgress.value = percentage
}

// 进度条触摸开始
const handleProgressTouchStart = (event: TouchEvent) => {
  event.stopPropagation()
  isDraggingProgress.value = true
  showProgressBar.value = true

  if (progressBarTimer) {
    clearTimeout(progressBarTimer)
  }
}

// 进度条触摸移动
const handleProgressTouchMove = (event: TouchEvent) => {
  if (!isDraggingProgress.value) return
  event.preventDefault()
  event.stopPropagation()

  const video = videoRefs.value[currentIndex.value]
  if (!video || !video.duration) return

  const touch = event.touches[0]
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const touchX = touch.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (touchX / rect.width) * 100))

  video.currentTime = (percentage / 100) * video.duration
  currentProgress.value = percentage
}

// 进度条触摸结束
const handleProgressTouchEnd = (event: TouchEvent) => {
  event.stopPropagation()
  isDraggingProgress.value = false

  // 3秒后隐藏进度条
  progressBarTimer = setTimeout(() => {
    showProgressBar.value = false
  }, 3000)
}

// 进度条鼠标按下
const handleProgressMouseDown = (event: MouseEvent) => {
  event.stopPropagation()
  isDraggingProgress.value = true
  showProgressBar.value = true

  if (progressBarTimer) {
    clearTimeout(progressBarTimer)
  }
}

// 进度条鼠标移动
const handleProgressMouseMove = (event: MouseEvent) => {
  if (!isDraggingProgress.value) return
  event.preventDefault()
  event.stopPropagation()

  const video = videoRefs.value[currentIndex.value]
  if (!video || !video.duration) return

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (mouseX / rect.width) * 100))

  video.currentTime = (percentage / 100) * video.duration
  currentProgress.value = percentage
}

// 进度条鼠标释放
const handleProgressMouseUp = (event: MouseEvent) => {
  event.stopPropagation()
  isDraggingProgress.value = false

  // 3秒后隐藏进度条
  progressBarTimer = setTimeout(() => {
    showProgressBar.value = false
  }, 3000)
}

// 视频事件处理
const onVideoLoaded = (index: number) => {
  videos.value[index].isLoading = false
  console.log(`✅ 视频 ${index} 加载完成`)
}

const onVideoCanPlay = (index: number) => {
  console.log(`✅ 视频 ${index} 可以播放`)
  // 视频可以播放时清除错误状态
  if (videos.value[index]) {
    videos.value[index].hasError = false
    videos.value[index].isLoading = false
  }
}

const onVideoError = (index: number) => {
  const video = videoRefs.value[index]
  const videoData = videos.value[index]

  // 只有在视频有有效src且当前正在播放时才报告错误
  if (video && video.src && video.src !== '' && index === currentIndex.value) {
    console.error(`❌ 视频 ${index} 加载错误:`, video.src)
    videoData.hasError = true
    videoData.isLoading = false
  } else {
    // 忽略无src或非当前视频的错误（这些是预期的）
    console.log(`🔇 忽略视频 ${index} 的预期错误（无src或非当前视频）`)
  }
}

// 操作方法
const retryVideo = (index: number) => {
  console.log(`🔄 重试视频 ${index}`)
  videos.value[index].hasError = false
  videos.value[index].isLoading = true

  // Video.js会自动重试，这里只需要重置状态
  if (index === currentIndex.value) {
    // 如果是当前视频，尝试重新播放
    setTimeout(() => {
      autoPlayCurrentVideo()
    }, 1000)
  }
}

const toggleLike = async (index: number) => {
  const video = videos.value[index]

  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再点赞')
    router.push('/login')
    return
  }

  // 防止重复点击
  if (video.isLiking) return
  video.isLiking = true

  try {
    const wasLiked = video.isLiked
    const originalLikes = video.likes

    // 乐观更新UI
    video.isLiked = !wasLiked
    video.likes += video.isLiked ? 1 : -1

    // 调用API
    if (video.isLiked) {
      const response = await videoApi.like(video.id)
      if (response.code === 200) {
        video.likes = response.data.likeCount || video.likes
        ElMessage.success('点赞成功')
      } else {
        throw new Error(response.message || '点赞失败')
      }
    } else {
      const response = await videoApi.unlike(video.id)
      if (response.code === 200) {
        video.likes = response.data.likeCount || video.likes
        ElMessage.success('取消点赞成功')
      } else {
        throw new Error(response.message || '取消点赞失败')
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    // 回滚UI状态
    video.isLiked = !video.isLiked
    video.likes += video.isLiked ? 1 : -1
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    video.isLiking = false
  }
}

const followUser = (index: number) => {
  const video = videos.value[index]
  video.author.isFollowing = true
  console.log('关注用户:', video.author.name)
}

const showComments = async (index: number) => {
  const video = videos.value[index]

  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再查看评论')
    router.push('/login')
    return
  }

  try {
    // 这里可以打开评论弹窗或跳转到评论页面
    // 暂时使用简单的提示框演示
    const { value: comment } = await ElMessageBox.prompt('请输入评论内容', '发表评论', {
      confirmButtonText: '发表',
      cancelButtonText: '取消',
      inputPlaceholder: '说点什么...',
      inputType: 'textarea',
      inputValidator: (value) => {
        if (!value || value.trim().length === 0) {
          return '评论内容不能为空'
        }
        if (value.length > 500) {
          return '评论内容不能超过500字'
        }
        return true
      }
    })

    if (comment && comment.trim()) {
      // 这里应该调用评论API
      // const response = await commentApi.create(video.id, comment.trim())
      ElMessage.success('评论发表成功')
      console.log('发表评论:', comment.trim(), '视频ID:', video.id)
    }
  } catch (error) {
    // 用户取消或其他错误
    if (error !== 'cancel') {
      console.error('评论操作失败:', error)
      ElMessage.error('评论发表失败')
    }
  }
}

const toggleFavorite = async (index: number) => {
  const video = videos.value[index]

  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再收藏')
    router.push('/login')
    return
  }

  // 防止重复点击
  if (video.isFavoriting) return
  video.isFavoriting = true

  try {
    const wasFavorited = video.isFavorited

    // 乐观更新UI
    video.isFavorited = !wasFavorited

    // 调用API
    if (video.isFavorited) {
      const response = await videoApi.favorite(video.id)
      if (response.code === 200) {
        ElMessage.success('收藏成功')
      } else {
        throw new Error(response.message || '收藏失败')
      }
    } else {
      const response = await videoApi.unfavorite(video.id)
      if (response.code === 200) {
        ElMessage.success('取消收藏成功')
      } else {
        throw new Error(response.message || '取消收藏失败')
      }
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    // 回滚UI状态
    video.isFavorited = !video.isFavorited
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    video.isFavoriting = false
  }
}

const shareVideo = async (index: number) => {
  const video = videos.value[index]

  try {
    // 调用后端API记录分享统计
    await videoApi.share(video.id)

    // 生成分享链接
    const shareUrl = `${window.location.origin}/video/${video.id}`

    // 检查是否支持Web Share API
    if (navigator.share) {
      await navigator.share({
        title: video.title,
        text: video.description,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      fallbackShare(video, shareUrl)
    }
  } catch (error) {
    console.error('分享失败:', error)
    // 即使API调用失败，也允许用户分享
    const shareUrl = `${window.location.origin}/video/${video.id}`
    fallbackShare(video, shareUrl)
  }
}

const fallbackShare = (video: any, shareUrl: string) => {
  // 复制链接到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard.writeText(shareUrl).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败')
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = shareUrl
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('链接已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}

const formatCount = (count: number): string => {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M'
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K'
  }
  return count.toString()
}

// 生命周期
onMounted(async () => {
  console.log('🎬 TikTok风格组件已挂载')
  await loadCategories()
  await loadVideos()
})

onUnmounted(() => {
  // 清理所有HLS实例
  Object.values(hlsInstances.value).forEach(hls => {
    if (hls) {
      hls.destroy()
    }
  })

  // 清理所有视频
  Object.values(videoRefs.value).forEach(video => {
    if (video) {
      video.pause()
      video.src = ''
    }
  })
})
</script>

<style scoped>
.tiktok-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.video-list {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.video-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.main-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
}

.play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.play-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%) scale(1.1);
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  text-align: center;
  z-index: 10;
}

.error button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #ff2d55;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
}

/* 顶部导航 - 纯文字设计 */
.top-nav {
  position: absolute;
  top: 60px; /* 向下移动60px */
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: transparent; /* 去掉背景色 */
  z-index: 30;
}

/* 汉堡菜单按钮 */
.hamburger-btn {
  background: transparent;
  border: none;
  color: white;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.hamburger-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.nav-tabs {
  display: flex;
  gap: 24px;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  flex: 1;
  padding: 0 8px;
  background: transparent; /* 去掉背景色 */
}

.nav-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.nav-tab {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  padding: 8px 0;
  white-space: nowrap;
  flex-shrink: 0;
}

.nav-tab.active {
  color: white;
}

.nav-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: white;
  border-radius: 1px;
}

/* 搜索按钮 */
.search-btn {
  background: transparent;
  border: none;
  color: white;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.search-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 右侧操作栏 */
.right-sidebar {
  position: absolute;
  right: 12px;
  bottom: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  z-index: 20;
}

.user-avatar {
  position: relative;
  width: 60px; /* 头像稍微小点，保持协调 */
  height: 60px;
}

.user-avatar img {
  width: 60px !important; /* 头像稍微小点，保持协调 */
  height: 60px !important;
  border-radius: 50% !important;
  border: 2px solid white;
  object-fit: cover;
  display: block;
  background-color: #f0f0f0;
}

.follow-btn {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: #ff2d55;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.action-icon {
  width: 75px; /* 其他按钮稍微大点，保持协调 */
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
}

.action-icon svg {
  width: 45px; /* 图标也相应增大 */
  height: 45px;
}

.action-icon:hover {
  transform: scale(1.1);
}

.action-icon.liked {
  color: #ff2d55;
  animation: heartBeat 0.6s ease-in-out;
}

.action-icon.favorited {
  color: #ffd700;
  animation: starGlow 0.6s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes starGlow {
  0% { transform: scale(1); filter: brightness(1); }
  50% { transform: scale(1.2); filter: brightness(1.5); }
  100% { transform: scale(1); filter: brightness(1); }
}



@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.action-count {
  color: white;
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 播放进度条 - 位于底部导航栏上方 */
.progress-container {
  position: absolute;
  bottom: 70px; /* 稍微往上移动一点 */
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 25;
  cursor: pointer;
  transition: height 0.3s ease;
}

/* 显示可拖拽进度条时增加高度 */
.progress-container.progress-visible {
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: white;
  transition: width 0.1s ease;
  border-radius: 3px;
}

/* 进度条拖拽圆点 */
.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.progress-thumb:hover {
  transform: translate(-50%, -50%) scale(1.2);
}

/* 底部导航栏占位 */
.bottom-nav-placeholder {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 30;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item {
  color: white;
  font-size: 12px;
  text-align: center;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.nav-item:hover {
  opacity: 1;
}

/* 底部信息栏 - 移动到更靠下的位置 */
.bottom-info {
  position: absolute;
  left: 16px;
  right: 120px; /* 增加右边距，为右侧按钮留出更多空间 */
  bottom: 90px; /* 移动到第二个红框位置，距离进度条20px */
  color: white;
  z-index: 20;
  max-width: calc(100vw - 140px); /* 限制最大宽度，防止文字延伸出页面 */
  display: flex;
  flex-direction: column;
  gap: 8px; /* 用户名和描述之间的间距 */
}

.user-info {
  margin-bottom: 12px;
}

.username {
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  order: 1; /* 确保用户名在上方 */
}

.description {
  font-size: 14px;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 限制最多显示3行 */
  -webkit-box-orient: vertical;
  max-width: 100%;
  word-wrap: break-word;
  order: 2; /* 确保描述在下方 */
  margin-bottom: 0; /* 确保底部距离进度条20px */
}



.debug-info {
  position: absolute;
  top: 70px;
  left: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 20;
}

.debug-info p {
  margin: 2px 0;
}

/* ================================
   响应式设计优化
   ================================ */

/* 超小屏幕 (手机竖屏) - 320px ~ 575px */
@media (max-width: 575.98px) {
  .right-sidebar {
    right: 6px;
    bottom: 130px; /* 调整以适应底部导航栏 */
    gap: 12px;
  }

  .user-avatar {
    width: 50px; /* 手机端头像稍小 */
    height: 50px;
  }

  .user-avatar img {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
  }

  .action-icon {
    width: 65px; /* 手机端其他按钮稍大 */
    height: 65px;
  }

  .action-icon svg {
    width: 38px; /* 相应增大图标 */
    height: 38px;
  }

  .action-count {
    font-size: 11px;
  }

  .bottom-info {
    left: 10px;
    right: 95px; /* 为更大的按钮留出更多空间 */
    bottom: 120px; /* 调整到合适位置 */
  }

  .username {
    font-size: 14px;
  }

  .description {
    font-size: 12px;
    line-height: 1.3;
  }



  .debug-info {
    top: 60px;
    left: 10px;
    font-size: 10px;
    padding: 6px 8px;
  }

  /* 手机端进度条调整 */
  .progress-container {
    bottom: 60px;
  }

  /* 手机端底部导航栏 */
  .bottom-nav-placeholder {
    height: 60px;
  }
}

/* 小屏幕 (手机横屏) - 576px ~ 767px */
@media (min-width: 576px) and (max-width: 767.98px) {
  .right-sidebar {
    right: 8px;
    bottom: 75px;
    gap: 14px;
  }

  .user-avatar {
    width: 55px; /* 小屏幕头像稍小 */
    height: 55px;
  }

  .user-avatar img {
    width: 55px !important;
    height: 55px !important;
    border-radius: 50% !important;
  }

  .action-icon {
    width: 70px; /* 小屏幕其他按钮稍大 */
    height: 70px;
  }

  .action-icon svg {
    width: 42px; /* 相应增大图标 */
    height: 42px;
  }

  .bottom-info {
    left: 12px;
    right: 65px;
    bottom: 55px;
  }

  .username {
    font-size: 15px;
  }

  .description {
    font-size: 13px;
  }


}

/* 中等屏幕 (平板竖屏) - 768px ~ 991px */
@media (min-width: 768px) and (max-width: 991.98px) {
  .right-sidebar {
    right: 10px;
    bottom: 80px;
    gap: 16px;
  }

  .user-avatar {
    width: 58px; /* 中等屏幕头像稍小 */
    height: 58px;
  }

  .user-avatar img {
    width: 58px !important;
    height: 58px !important;
    border-radius: 50% !important;
  }

  .action-icon {
    width: 73px; /* 中等屏幕其他按钮稍大 */
    height: 73px;
  }

  .action-icon svg {
    width: 44px; /* 相应增大图标 */
    height: 44px;
  }

  .bottom-info {
    left: 16px;
    right: 75px;
    bottom: 65px;
  }

  .username {
    font-size: 16px;
  }

  .description {
    font-size: 14px;
  }


}

/* 大屏幕 (平板横屏/桌面) - 992px+ */
@media (min-width: 992px) {
  .right-sidebar {
    right: 12px;
    bottom: 85px;
    gap: 18px;
  }

  .user-avatar {
    width: 65px; /* 桌面端头像适中 */
    height: 65px;
  }

  .user-avatar img {
    width: 65px !important;
    height: 65px !important;
    border-radius: 50% !important;
  }

  .action-icon {
    width: 80px; /* 桌面端其他按钮最大 */
    height: 80px;
  }

  .action-icon svg {
    width: 48px; /* 相应增大图标 */
    height: 48px;
  }

  .bottom-info {
    left: 20px;
    right: 85px;
    bottom: 80px;
  }

  .username {
    font-size: 17px;
  }

  .description {
    font-size: 15px;
  }


}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .action-icon:hover {
    transform: none;
  }

  .action-icon:active {
    transform: scale(0.9);
  }

  .user-avatar:active {
    transform: scale(0.95);
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .right-sidebar {
    bottom: 20px;
    gap: 10px;
  }

  .bottom-info {
    bottom: 20px;
  }

  .debug-info {
    top: 10px;
  }
}

/* 防止选中文本 */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
</style>
