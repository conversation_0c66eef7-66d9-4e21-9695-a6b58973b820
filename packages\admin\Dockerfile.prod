# 生产模式 - 使用Node.js Express提供静态文件
FROM node:18-alpine

# 设置时区
ENV TZ=Asia/Shanghai
RUN apk update && apk add --no-cache tzdata \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && apk del tzdata

# 设置工作目录
WORKDIR /app

# 安装express
RUN npm install express

# 复制静态文件服务器脚本
COPY serve-static.js /app/

# 复制构建好的静态文件
COPY dist/ /app/dist/

# 暴露端口
EXPOSE 3001

# 启动静态文件服务器
CMD ["node", "serve-static.js"]
