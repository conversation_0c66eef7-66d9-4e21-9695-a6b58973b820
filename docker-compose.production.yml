# ==========================================
# 🚀 生产环境 Docker Compose 配置
# ==========================================
# 专为生产环境优化的容器配置
# 包含性能优化、安全加固、监控等

version: '3.8'

services:
  # ==========================================
  # 🗄️ MySQL 数据库服务
  # ==========================================
  mysql:
    image: mysql:8.0
    container_name: shipin_mysql_prod
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASS}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "127.0.0.1:${MYSQL_PORT:-3306}:3306"  # 仅本地访问
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./packages/api/database/init:/docker-entrypoint-initdb.d:ro
      - ./config/mysql/prod.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - ./backups/mysql:/backups
    networks:
      - shipin_network
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=1G
      --innodb-log-file-size=256M
      --max-connections=200
      --query-cache-size=64M
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================
  # 🔴 Redis 缓存服务
  # ==========================================
  redis:
    image: redis:7-alpine
    container_name: shipin_redis_prod
    restart: always
    ports:
      - "127.0.0.1:${REDIS_PORT:-6379}:6379"  # 仅本地访问
    volumes:
      - redis_prod_data:/data
      - ./config/redis/prod.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - shipin_network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================
  # 🚀 API 后端服务
  # ==========================================
  api:
    build:
      context: ./packages/api
      dockerfile: Dockerfile.prod
      target: production
    image: shipin/api:${APP_VERSION:-latest}
    container_name: shipin_api_prod
    restart: always
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASS=${DB_PASS}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET}
      - API_KEY_ADMIN=${API_KEY_ADMIN}
      - API_KEY_USER=${API_KEY_USER}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
    ports:
      - "127.0.0.1:${API_PORT:-3000}:3000"  # 仅本地访问
    volumes:
      - ./storage/logs:/var/www/html/runtime/logs
      - ./storage/uploads:/var/www/html/public/uploads:ro
      - ./storage/videos:/var/www/html/public/videos:ro
      - ./backups/api:/backups
    networks:
      - shipin_network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # ==========================================
  # 🌐 Nginx 反向代理
  # ==========================================
  nginx:
    image: nginx:alpine
    container_name: shipin_nginx_prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/prod.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/sites:/etc/nginx/conf.d:ro
      - ./storage/ssl:/etc/nginx/ssl:ro
      - ./packages/frontend/dist:/var/www/frontend:ro
      - ./packages/admin/dist:/var/www/admin:ro
      - ./storage/uploads:/var/www/uploads:ro
      - ./storage/videos:/var/www/videos:ro
      - ./storage/logs/nginx:/var/log/nginx
    networks:
      - shipin_network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  # ==========================================
  # 📊 监控服务
  # ==========================================
  
  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: shipin_prometheus_prod
    restart: always
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - shipin_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: shipin_grafana_prod
    restart: always
    ports:
      - "127.0.0.1:3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - shipin_network
    depends_on:
      - prometheus

  # ==========================================
  # 🔄 备份服务
  # ==========================================
  backup:
    build:
      context: ./scripts/backup
      dockerfile: Dockerfile
    container_name: shipin_backup_prod
    restart: unless-stopped
    environment:
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}  # 每天凌晨2点
      - DB_HOST=mysql
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASS=${DB_PASS}
      - BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
    volumes:
      - ./backups:/backups
      - mysql_prod_data:/var/lib/mysql:ro
      - redis_prod_data:/data:ro
    networks:
      - shipin_network
    depends_on:
      - mysql
      - redis

# ==========================================
# 📦 数据卷配置
# ==========================================
volumes:
  mysql_prod_data:
    driver: local
    name: shipin_mysql_prod_data
  redis_prod_data:
    driver: local
    name: shipin_redis_prod_data
  prometheus_data:
    driver: local
    name: shipin_prometheus_data
  grafana_data:
    driver: local
    name: shipin_grafana_data

# ==========================================
# 🌐 网络配置
# ==========================================
networks:
  shipin_network:
    driver: bridge
    name: shipin_prod_network
    ipam:
      config:
        - subnet: **********/16
