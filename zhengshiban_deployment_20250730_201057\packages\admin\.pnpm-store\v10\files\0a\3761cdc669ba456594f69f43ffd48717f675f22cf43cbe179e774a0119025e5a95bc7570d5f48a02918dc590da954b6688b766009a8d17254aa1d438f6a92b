export { ConfigEnv, UserConfig, mergeConfig } from 'vite';
export { ax as UserConfigExport, aw as UserConfigFn, au as UserConfigFnObject, av as UserConfigFnPromise, az as UserProjectConfigExport, ay as UserProjectConfigFn, a1 as UserWorkspaceConfig, aD as configDefaults, aG as coverageConfigDefaults, aF as defaultExclude, aE as defaultInclude, aA as defineConfig, aB as defineProject, aC as defineWorkspace, aH as extraInlineDeps } from './reporters-w_64AS5f.js';
import '@vitest/runner';
import 'vite-node';
import '@vitest/snapshot';
import '@vitest/expect';
import '@vitest/runner/utils';
import '@vitest/utils';
import 'tinybench';
import 'node:stream';
import 'vite-node/client';
import '@vitest/snapshot/manager';
import 'vite-node/server';
import 'node:worker_threads';
import 'node:fs';
import 'chai';
