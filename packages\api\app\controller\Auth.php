<?php

namespace app\controller;

use think\App;
use app\BaseController;
use app\model\User;
use think\Request;
use think\Response;
use think\facade\Validate;
use think\facade\Db;
use app\service\JwtService;
use OpenApi\Attributes as OA;

/**
 * 用户认证控制器 - JWT版本
 *
 * 功能：
 * 1. 用户登录/注册
 * 2. JWT令牌管理
 * 3. 多设备登录支持
 * 4. 令牌刷新和撤销
 */
class Auth extends BaseController
{
    /**
     * JWT服务
     */
    private JwtService $jwtService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->jwtService = new JwtService();
    }
    /**
     * 用户登录
     *
     * @OA\Post(
     *     path="/api/v1/auth/login",
     *     tags={"认证"},
     *     summary="用户登录",
     *     description="用户通过用户名/邮箱和密码进行登录，支持多设备登录",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"username", "password"},
     *             @OA\Property(property="username", type="string", description="用户名或邮箱", example="testuser"),
     *             @OA\Property(property="password", type="string", description="密码", example="password123"),
     *             @OA\Property(property="remember", type="boolean", description="记住登录状态", example=false)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="登录成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="登录成功"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="access_token", type="string", description="访问令牌"),
     *                 @OA\Property(property="refresh_token", type="string", description="刷新令牌"),
     *                 @OA\Property(property="expires_in", type="integer", description="令牌过期时间(秒)", example=3600),
     *                 @OA\Property(
     *                     property="user",
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="username", type="string", example="testuser"),
     *                     @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="level", type="integer", example=1),
     *                     @OA\Property(property="avatar", type="string", example="/uploads/avatars/default.jpg")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="请求参数错误",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="用户名不能为空"),
     *             @OA\Property(property="data", type="null")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="认证失败",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="用户名或密码错误"),
     *             @OA\Property(property="data", type="null")
     *         )
     *     )
     * )
     */
    public function login(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证输入
            $validate = Validate::rule([
                'username' => 'require',
                'password' => 'require'
            ])->message([
                'username.require' => '用户名不能为空',
                'password.require' => '密码不能为空'
            ]);
            
            if (!$validate->check($data)) {
                return json([
                    'success' => false,
                    'message' => $validate->getError(),
                    'data' => null
                ]);
            }
            
            // 查找用户
            $user = User::findByUsername($data['username']);
            if (!$user) {
                return json([
                    'success' => false,
                    'message' => '用户名或密码错误',
                    'data' => null
                ]);
            }
            
            // 验证密码
            if (!$user->verifyPassword($data['password'])) {
                return json([
                    'success' => false,
                    'message' => '用户名或密码错误',
                    'data' => null
                ]);
            }
            
            // 检查用户状态
            if (!$user->isActive()) {
                return json([
                    'success' => false,
                    'message' => '账户已被禁用',
                    'data' => null
                ]);
            }
            
            // 获取设备ID
            $deviceId = $request->header('X-Device-ID') ?? $request->post('device_id') ?? null;

            // 生成JWT令牌
            $userData = [
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email ?? '',
                'level' => $user->level ?? 1
            ];

            $accessToken = $this->jwtService->generateAccessToken($userData, $deviceId);
            $refreshToken = $this->jwtService->generateRefreshToken($userData, $deviceId);

            return json([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'user' => $user->getBasicInfo(),
                    'access_token' => $accessToken,
                    'refresh_token' => $refreshToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('jwt.ttl', 7200)
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '登录失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 用户注册
     */
    public function register(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证输入
            $validate = Validate::rule([
                'username' => 'require|length:3,20|alphaNum',
                'email' => 'require|email',
                'password' => 'require|length:6,20',
                'confirmPassword' => 'require|confirm:password'
            ])->message([
                'username.require' => '用户名不能为空',
                'username.length' => '用户名长度为3-20个字符',
                'username.alphaNum' => '用户名只能包含字母和数字',
                'email.require' => '邮箱不能为空',
                'email.email' => '邮箱格式不正确',
                'password.require' => '密码不能为空',
                'password.length' => '密码长度为6-20个字符',
                'confirmPassword.require' => '确认密码不能为空',
                'confirmPassword.confirm' => '两次密码输入不一致'
            ]);
            
            if (!$validate->check($data)) {
                return json([
                    'success' => false,
                    'message' => $validate->getError(),
                    'data' => null
                ]);
            }
            
            // 检查用户名是否存在
            if (User::usernameExists($data['username'])) {
                error_log("注册失败 - 用户名已存在: " . $data['username']);
                return json([
                    'success' => false,
                    'message' => '用户名已存在',
                    'data' => null
                ]);
            }

            // 检查邮箱是否存在
            if (User::emailExists($data['email'])) {
                error_log("注册失败 - 邮箱已存在: " . $data['email']);
                return json([
                    'success' => false,
                    'message' => '邮箱已存在',
                    'data' => null
                ]);
            }

            error_log("开始创建用户: " . $data['username'] . " (" . $data['email'] . ")");

            // 创建用户
            $user = User::createUser([
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => $data['password'],
                'nickname' => $data['nickname'] ?? $data['username']
            ]);

            if (!$user) {
                error_log("用户创建失败: " . $data['username'] . " - createUser返回false");
                return json([
                    'success' => false,
                    'message' => '注册失败，请稍后重试',
                    'data' => null
                ]);
            }

            error_log("用户注册成功: " . $data['username'] . " (ID: " . $user->id . ")");
            
            return json([
                'success' => true,
                'message' => '注册成功',
                'data' => [
                    'user' => $user->getBasicInfo()
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '注册失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    

    
    /**
     * 获取用户信息
     */
    public function profile(Request $request): Response
    {
        try {
            // 验证用户认证
            $authHeader = $request->header('Authorization');
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return json([
                    'success' => false,
                    'message' => '未提供认证信息',
                    'data' => null
                ], 401);
            }

            $token = substr($authHeader, 7);
            if (empty($token)) {
                return json([
                    'success' => false,
                    'message' => '无效的认证令牌',
                    'data' => null
                ], 401);
            }

            // 验证JWT token并获取用户ID
            try {
                $payload = JWT::decode($token, new Key(config('jwt.secret'), 'HS256'));
                $userId = $payload->user_id ?? null;

                if (!$userId) {
                    return json([
                        'success' => false,
                        'message' => '无效的用户令牌',
                        'data' => null
                    ], 401);
                }
            } catch (\Exception $e) {
                return json([
                    'success' => false,
                    'message' => '令牌验证失败：' . $e->getMessage(),
                    'data' => null
                ], 401);
            }

            // 测试数据库连接
            try {
                $dbTest = Db::query('SELECT 1 as test');
                if (empty($dbTest)) {
                    throw new \Exception('数据库连接测试失败');
                }
            } catch (\Exception $e) {
                return json([
                    'success' => false,
                    'message' => '数据库连接失败：' . $e->getMessage(),
                    'data' => null
                ]);
            }

            // 获取用户详细信息
            $user = null;
            try {
                $user = Db::table('users')
                    ->field([
                        'id', 'username', 'nickname', 'email', 'avatar', 'vip_expire_time', 'created_at'
                    ])
                    ->where('id', $userId)
                    ->where('status', 'active')
                    ->find();
            } catch (\Exception $e) {
                return json([
                    'success' => false,
                    'message' => '查询用户信息失败：' . $e->getMessage(),
                    'data' => null
                ]);
            }

            if (!$user) {
                return json([
                    'success' => false,
                    'message' => '用户不存在或已被禁用',
                    'data' => null
                ], 404);
            }

            // 添加数据库中不存在但前端需要的字段
            $user['level'] = 1;
            $user['points'] = 100;
            $user['is_vip'] = !empty($user['vip_expire_time']) && strtotime($user['vip_expire_time']) > time();
            $user['vip_level'] = $user['is_vip'] ? 1 : 0;

            // 获取用户统计
            $stats = [
                'upload_count' => 0,
                'total_views' => 0,
                'total_likes' => 0,
                'favorite_count' => 0
            ];

            try {
                $stats['upload_count'] = Db::table('videos')->where('user_id', $userId)->count();
                $stats['total_views'] = Db::table('videos')->where('user_id', $userId)->sum('view_count') ?: 0;
                $stats['total_likes'] = Db::table('video_likes')->where('user_id', $userId)->count();
                $stats['favorite_count'] = Db::table('video_collections')->where('user_id', $userId)->count();
            } catch (\Exception $e) {
                // 数据库查询失败，使用默认统计
            }

            $user['stats'] = $stats;

            // 返回用户信息
            return json([
                'success' => true,
                'message' => '获取成功',
                'data' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'nickname' => $user['nickname'] ?: $user['username'],
                    'email' => $user['email'],
                    'avatar' => $user['avatar'],
                    'level' => $user['level'],
                    'points' => $user['points'],
                    'is_vip' => $user['is_vip'],
                    'vip_level' => $user['vip_level'],
                    'vip_expire_time' => $user['vip_expire_time'],
                    'created_at' => $user['created_at'],
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取用户信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 找回密码 - 发送重置邮件
     */
    public function forgotPassword(Request $request): Response
    {
        try {
            $data = $request->post();

            // 验证输入
            $validate = Validate::rule([
                'email' => 'require|email'
            ])->message([
                'email.require' => '邮箱不能为空',
                'email.email' => '邮箱格式不正确'
            ]);

            if (!$validate->check($data)) {
                return json([
                    'success' => false,
                    'message' => $validate->getError(),
                    'data' => null
                ]);
            }

            // 查找用户
            $user = User::findByEmail($data['email']);
            if (!$user) {
                // 为了安全，即使邮箱不存在也返回成功
                return json([
                    'success' => true,
                    'message' => '如果该邮箱存在，重置密码邮件已发送',
                    'data' => null
                ]);
            }

            // 生成重置token（简化版本，实际项目中应该保存到数据库）
            $resetToken = 'reset_' . $user->id . '_' . time() . '_' . md5($user->email);

            // 这里应该发送邮件，暂时只返回成功消息
            // TODO: 集成邮件服务

            return json([
                'success' => true,
                'message' => '重置密码邮件已发送，请查收',
                'data' => [
                    'reset_token' => $resetToken // 仅用于测试，实际项目中不应该返回
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '发送重置邮件失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 重置密码
     */
    public function resetPassword(Request $request): Response
    {
        try {
            $data = $request->post();

            // 验证输入
            $validate = Validate::rule([
                'token' => 'require',
                'password' => 'require|length:6,20',
                'confirmPassword' => 'require|confirm:password'
            ])->message([
                'token.require' => '重置令牌不能为空',
                'password.require' => '新密码不能为空',
                'password.length' => '密码长度为6-20个字符',
                'confirmPassword.require' => '确认密码不能为空',
                'confirmPassword.confirm' => '两次密码输入不一致'
            ]);

            if (!$validate->check($data)) {
                return json([
                    'success' => false,
                    'message' => $validate->getError(),
                    'data' => null
                ]);
            }

            // 简化的token验证（实际项目中应该从数据库验证）
            if (!preg_match('/^reset_(\d+)_(\d+)_([a-f0-9]{32})$/', $data['token'], $matches)) {
                return json([
                    'success' => false,
                    'message' => '无效的重置令牌',
                    'data' => null
                ]);
            }

            $userId = $matches[1];
            $timestamp = $matches[2];

            // 检查token是否过期（1小时）
            if (time() - $timestamp > 3600) {
                return json([
                    'success' => false,
                    'message' => '重置令牌已过期',
                    'data' => null
                ]);
            }

            // 查找用户
            $user = User::where('id', $userId)->find();
            if (!$user) {
                return json([
                    'success' => false,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }

            // 更新密码
            $user->password = $data['password']; // 会自动加密
            if ($user->save()) {
                return json([
                    'success' => true,
                    'message' => '密码重置成功',
                    'data' => null
                ]);
            } else {
                return json([
                    'success' => false,
                    'message' => '密码重置失败',
                    'data' => null
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '重置密码失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 刷新访问令牌
     */
    public function refresh(Request $request): Response
    {
        try {
            $refreshToken = $request->post('refresh_token');

            if (empty($refreshToken)) {
                return json([
                    'success' => false,
                    'message' => '刷新令牌不能为空',
                    'data' => null
                ]);
            }

            // 刷新令牌
            $tokens = $this->jwtService->refreshAccessToken($refreshToken);

            return json([
                'success' => true,
                'message' => '令牌刷新成功',
                'data' => [
                    'access_token' => $tokens['access_token'],
                    'refresh_token' => $tokens['refresh_token'],
                    'token_type' => 'Bearer',
                    'expires_in' => $tokens['expires_in']
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '令牌刷新失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 用户登出
     */
    public function logout(Request $request): Response
    {
        try {
            $token = $this->extractToken($request);

            if (!empty($token)) {
                // 撤销当前令牌
                $this->jwtService->revokeToken($token);
            }

            return json([
                'success' => true,
                'message' => '登出成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '登出失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 登出所有设备
     */
    public function logoutAll(Request $request): Response
    {
        try {
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return json([
                    'success' => false,
                    'message' => '用户信息不存在',
                    'data' => null
                ]);
            }

            $userId = $userInfo['id'];
            $currentDeviceId = $userInfo['device_id'] ?? null;

            // 撤销用户的所有令牌（除了当前设备）
            $this->jwtService->revokeUserTokens($userId, $currentDeviceId);

            return json([
                'success' => true,
                'message' => '已登出所有其他设备',
                'data' => null
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '操作失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取用户信息
     */
    public function me(Request $request): Response
    {
        try {
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return json([
                    'success' => false,
                    'message' => '用户信息不存在',
                    'data' => null
                ]);
            }

            // 从数据库获取最新用户信息
            $user = User::find($userInfo['id']);
            if (!$user) {
                return json([
                    'success' => false,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }

            return json([
                'success' => true,
                'message' => '获取用户信息成功',
                'data' => [
                    'user' => $user->getBasicInfo(),
                    'device_id' => $userInfo['device_id'] ?? null
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取用户信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 提取token
     */
    private function extractToken(Request $request): ?string
    {
        $authHeader = $request->header('Authorization');
        if (!empty($authHeader)) {
            if (strpos($authHeader, 'Bearer ') === 0) {
                return substr($authHeader, 7);
            }
            return $authHeader;
        }

        return $request->param('token') ?? $request->post('token');
    }
}
