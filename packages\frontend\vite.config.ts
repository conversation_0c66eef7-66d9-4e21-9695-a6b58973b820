import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  const isDev = command === 'serve'
  const isProd = mode === 'production'

  return {
    plugins: [
      vue({
        // Vue编译优化
        template: {
          compilerOptions: {
            // 生产环境移除注释
            comments: !isProd
          }
        }
      })
    ],

    server: {
      host: '0.0.0.0',
      port: parseInt(env.VITE_DEV_SERVER_PORT) || 3002,
      hmr: {
        port: parseInt(env.VITE_HMR_PORT) || 3002
      },
      // 开发服务器优化
      fs: {
        strict: false
      }
    },

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },

    // 构建优化
    build: {
      outDir: 'dist',
      sourcemap: isDev || env.VITE_SOURCE_MAP === 'true',
      minify: isProd ? 'terser' : false,
      target: 'es2015',
      cssTarget: 'chrome80',

      // 生产环境优化
      ...(isProd && {
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug']
          },
          mangle: {
            safari10: true
          }
        }
      }),

      rollupOptions: {
        output: {
          // 高性能代码分割策略
          manualChunks: {
            // 核心框架
            'vue-core': ['vue', '@vue/runtime-dom'],
            'vue-router': ['vue-router'],
            'pinia': ['pinia'],

            // 视频播放器（按需加载）
            'video-player': ['hls.js'],

            // UI组件库
            'element-plus': ['element-plus'],

            // 工具库
            'utils': ['axios'],

            // 页面级别分割
            'pages-home': ['./src/views/HomePage.vue'],
            'pages-video': [
              './src/views/VideoPlayer.vue',
              './src/views/ShortVideosSimple.vue',
              './src/views/LongVideos.vue'
            ],
            'pages-user': [
              './src/views/LoginPage.vue',
              './src/views/RegisterPage.vue',
              './src/views/ProfilePage.vue'
            ]
          },

          // 文件命名策略
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
            if (facadeModuleId) {
              if (facadeModuleId.includes('/views/')) {
                return 'js/pages/[name]-[hash].js'
              }
              if (facadeModuleId.includes('/components/')) {
                return 'js/components/[name]-[hash].js'
              }
            }
            return 'js/[name]-[hash].js'
          },

          entryFileNames: 'js/[name]-[hash].js',

          assetFileNames: (assetInfo) => {
            if (!assetInfo.name) return 'assets/[name]-[hash].[ext]'
            const info = assetInfo.name.split('.')
            const ext = info[info.length - 1]

            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              return `media/[name]-[hash].${ext}`
            }
            if (/\.(png|jpe?g|gif|svg|webp|avif)(\?.*)?$/i.test(assetInfo.name)) {
              return `images/[name]-[hash].${ext}`
            }
            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              return `fonts/[name]-[hash].${ext}`
            }
            return `assets/[name]-[hash].${ext}`
          }
        }
      },

      // 构建性能优化
      chunkSizeWarningLimit: 1000,
      cssCodeSplit: true,
      manifest: true
    },

    // 开发优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'axios',
        'element-plus'
      ],
      exclude: [
        'hls.js' // 视频播放器按需加载
      ]
    },

    // 环境变量
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: isDev || env.VITE_ENABLE_DEVTOOLS === 'true'
    },

    envPrefix: 'VITE_'
  }
})
