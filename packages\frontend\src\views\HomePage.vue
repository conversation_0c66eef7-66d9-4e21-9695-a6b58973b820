<template>
  <div class="home-page">
    <!-- 动态背景 -->
    <div class="background-container">
      <div class="gradient-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
      </div>
      <div class="grid-pattern"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 品牌标识区域 -->
      <div class="brand-section fade-in">
        <div class="brand-logo">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,12L11,7.5V16.5Z"/>
            </svg>
          </div>
        </div>
        <h1 class="main-title text-gradient">51吃瓜网</h1>
        <p class="subtitle">专业视频平台 · 精彩内容聚集地</p>
        <div class="brand-stats">
          <div class="stat-item">
            <span class="stat-number">10K+</span>
            <span class="stat-label">精选视频</span>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <span class="stat-number">24/7</span>
            <span class="stat-label">在线服务</span>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <span class="stat-number">99%</span>
            <span class="stat-label">用户满意</span>
          </div>
        </div>
      </div>

      <!-- 功能导航区域 -->
      <div class="navigation-section slide-in-left">
        <div class="section-header">
          <h2 class="section-title">探索精彩内容</h2>
          <p class="section-subtitle">选择您感兴趣的内容类型</p>
        </div>

        <div class="nav-grid">
          <!-- 内部页面导航 -->
          <div
            class="nav-card glass-card scale-in"
            style="animation-delay: 0ms"
            @click="goToShortVideos"
            role="button"
            tabindex="0"
            @keydown.enter="goToShortVideos"
          >
            <div class="nav-card-header">
              <div class="nav-icon icon-video">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                </svg>
              </div>
              <div class="nav-badge">热门</div>
            </div>
            <div class="nav-card-content">
              <h3 class="nav-title">短视频</h3>
              <p class="nav-description">精彩短视频内容，快速浏览</p>
            </div>
            <div class="nav-card-footer">
              <span class="nav-action">立即观看</span>
              <div class="nav-arrow">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                </svg>
              </div>
            </div>
          </div>

          <div
            class="nav-card glass-card scale-in"
            style="animation-delay: 200ms"
            @click="goToLongVideos"
            role="button"
            tabindex="0"
            @keydown.enter="goToLongVideos"
          >
            <div class="nav-card-header">
              <div class="nav-icon icon-movie">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18,4L20,8H17L15,4H13L15,8H12L10,4H8L10,8H7L5,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V4H18Z"/>
                </svg>
              </div>
            </div>
            <div class="nav-card-content">
              <h3 class="nav-title">长视频</h3>
              <p class="nav-description">高质量长视频内容，深度观看</p>
            </div>
            <div class="nav-card-footer">
              <span class="nav-action">立即观看</span>
              <div class="nav-arrow">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 外部链接区域 -->
        <div v-if="hasExternalLinks" class="external-links-section">
          <div class="section-divider">
            <span class="divider-text">更多精彩内容</span>
          </div>
          <div class="external-links">
            <div
              v-for="(url, title) in externalLinks"
              :key="title"
              class="external-link glass-card"
              @click="goToUrl(title)"
              role="button"
              tabindex="0"
              @keydown.enter="goToUrl(title)"
            >
              <div class="external-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
                </svg>
              </div>
              <span class="external-title">{{ title }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 特色功能区域 -->
      <div class="features-section scale-in">
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,12L11,7.5V16.5Z"/>
              </svg>
            </div>
            <h4 class="feature-title">高清画质</h4>
            <p class="feature-desc">4K超清体验</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,12L11,7.5V16.5Z"/>
              </svg>
            </div>
            <h4 class="feature-title">流畅播放</h4>
            <p class="feature-desc">智能缓存技术</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,12L11,7.5V16.5Z"/>
              </svg>
            </div>
            <h4 class="feature-title">多端同步</h4>
            <p class="feature-desc">随时随地观看</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 装饰性元素 -->
    <div class="decorative-elements">
      <div class="floating-particles">
        <div class="particle" v-for="i in 6" :key="i"></div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer">
      <p class="notice">发送至我们的邮箱，任何举报51吃瓜的网站</p>
      <p class="email">《举报》邮箱：<span class="email-link"><EMAIL></span></p>
      
      <!-- 社交图标 -->
      <div class="social-icons">
        <div class="social-icon">📱</div>
        <div class="social-icon">💻</div>
        <div class="social-icon">📺</div>
      </div>
      
      <div class="links">
        <span>首页导航</span>
        <span>举报中心</span>
        <span>学习心得</span>
        <span>电脑版网站</span>
        <span>学习论坛</span>
      </div>
      
      <p class="copyright">本站内容来源于互联网，本站不承担任何法律责任如有侵权请联系站长删除</p>
      <p class="copyright">© 2023 51吃瓜 - 人工智能https://51chigua.me All Rights Reserved</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { advancedPerformanceMonitor } from '@/utils/advanced-performance'

const router = useRouter()

// 外部链接配置 - 从环境变量或API获取
const externalLinks = ref({
  '51吃瓜 线路一': import.meta.env.VITE_EXTERNAL_LINK_1 || '#',
  '51吃瓜 线路二': import.meta.env.VITE_EXTERNAL_LINK_2 || '#',
  '51吃瓜 线路三': import.meta.env.VITE_EXTERNAL_LINK_3 || '#'
})

// 检查是否有有效的外部链接
const hasExternalLinks = computed(() => {
  return Object.values(externalLinks.value).some(url => url !== '#')
})

// 导航函数
const goToUrl = (title: string) => {
  advancedPerformanceMonitor.startMeasure('external-link-navigation')
  
  const url = externalLinks.value[title]
  if (url && url !== '#') {
    window.open(url, '_blank', 'noopener,noreferrer')

    // 记录用户行为
    advancedPerformanceMonitor.recordCustomMetric('external-link-click', 1, 'count')
  } else {
    console.log(`链接未配置: ${title}`)
    // 可以显示提示信息给用户
  }

  advancedPerformanceMonitor.endMeasure('external-link-navigation')
}

const goToShortVideos = () => {
  advancedPerformanceMonitor.startMeasure('internal-navigation')
  router.push('/short-videos')
  advancedPerformanceMonitor.endMeasure('internal-navigation')
}

const goToLongVideos = () => {
  advancedPerformanceMonitor.startMeasure('internal-navigation')
  router.push('/long-videos')
  advancedPerformanceMonitor.endMeasure('internal-navigation')
}

// 页面加载完成后的处理
onMounted(() => {
  // 记录首页访问
  advancedPerformanceMonitor.recordCustomMetric('homepage-visit', 1, 'count')

  // 预加载关键资源
  const criticalRoutes = ['/short-videos', '/long-videos']
  criticalRoutes.forEach(route => {
    router.resolve(route) // 预解析路由
  })
})
</script>

<style scoped>
@import '@/styles/design-system.css';

.home-page {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ==========================================
   🌌 动态背景系统
   ========================================== */

.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.gradient-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, #667eea 0%, transparent 70%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #764ba2 0%, transparent 70%);
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, #f093fb 0%, transparent 70%);
  bottom: 20%;
  left: 50%;
  animation-delay: 4s;
}

.grid-pattern {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* ==========================================
   📱 主要内容区域
   ========================================== */

.main-content {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  gap: var(--space-12);
}

/* ==========================================
   🎨 品牌标识区域
   ========================================== */

.brand-section {
  text-align: center;
  max-width: 600px;
}

.brand-logo {
  margin-bottom: var(--space-6);
}

.logo-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-4);
  filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.5));
}

.main-title {
  font-size: clamp(2.5rem, 8vw, 4rem);
  font-weight: var(--font-black);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-4);
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  font-weight: var(--font-medium);
}

.brand-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--space-1);
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: var(--glass-border);
}

/* ==========================================
   🧭 导航区域
   ========================================== */

.navigation-section {
  width: 100%;
  max-width: 800px;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-tertiary);
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.nav-card {
  padding: var(--space-6);
  cursor: pointer;
  transition: all var(--duration-300) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0;
  transition: all var(--duration-500) var(--ease-out);
  z-index: -1;
}

.nav-card:hover::before {
  left: 0;
  opacity: 0.1;
}

.nav-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-primary);
}

.nav-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.nav-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  transition: all var(--duration-300) var(--ease-out);
}

.nav-icon svg {
  width: 24px;
  height: 24px;
  color: var(--text-primary);
}

.icon-video .nav-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-movie .nav-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.nav-badge {
  background: var(--gradient-secondary);
  color: var(--text-primary);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.nav-card-content {
  margin-bottom: var(--space-4);
}

.nav-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.nav-description {
  font-size: var(--text-base);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
}

.nav-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-action {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.nav-arrow {
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
  transition: all var(--duration-200) var(--ease-out);
}

.nav-card:hover .nav-arrow {
  transform: translateX(4px);
  color: var(--text-primary);
}

/* ==========================================
   🔗 外部链接区域
   ========================================== */

.external-links-section {
  margin-top: var(--space-8);
}

.section-divider {
  text-align: center;
  margin-bottom: var(--space-6);
  position: relative;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--glass-border);
}

.divider-text {
  background: var(--bg-primary);
  color: var(--text-tertiary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  padding: 0 var(--space-4);
  position: relative;
}

.external-links {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  justify-content: center;
}

.external-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: all var(--duration-300) var(--ease-out);
  border-radius: var(--radius-xl);
}

.external-link:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.external-icon {
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
}

.external-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

/* ==========================================
   ✨ 特色功能区域
   ========================================== */

.features-section {
  width: 100%;
  max-width: 600px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-6);
}

.feature-item {
  text-align: center;
  padding: var(--space-4);
}

.feature-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--space-3);
  border-radius: var(--radius-xl);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-300) var(--ease-out);
}

.feature-icon svg {
  width: 28px;
  height: 28px;
  color: var(--text-primary);
}

.feature-item:hover .feature-icon {
  background: var(--glass-bg-hover);
  transform: scale(1.1);
}

.feature-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.feature-desc {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

/* ==========================================
   📱 响应式设计
   ========================================== */

@media (max-width: 768px) {
  .main-content {
    padding: var(--space-6) var(--space-4);
    gap: var(--space-8);
  }

  .brand-stats {
    gap: var(--space-4);
  }

  .nav-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .external-links {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--space-4) var(--space-3);
    gap: var(--space-6);
  }

  .brand-stats {
    flex-direction: column;
    gap: var(--space-3);
  }

  .stat-divider {
    width: 40px;
    height: 1px;
  }

  .nav-card {
    padding: var(--space-4);
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }
}

/* ==========================================
   🎬 动画增强
   ========================================== */

@media (prefers-reduced-motion: no-preference) {
  .fade-in {
    animation: fadeIn 0.8s var(--ease-out);
  }

  .slide-in-left {
    animation: slideInLeft 0.8s var(--ease-out) 0.2s both;
  }

  .scale-in {
    animation: scaleIn 0.6s var(--ease-bounce) 0.4s both;
  }
}

@media (prefers-reduced-motion: reduce) {
  .orb,
  .grid-pattern,
  .particle {
    animation: none;
  }

  .nav-card:hover {
    transform: none;
  }
}
</style>
