<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\Response;
use think\facade\Log;
use think\facade\Cache;
use app\service\VideoEncryptionService;
use app\service\DatabaseManagerService;

/**
 * 视频加密控制器
 * 
 * 提供视频加密相关的API接口，包括：
 * - 密钥获取接口（供HLS播放器使用）
 * - 加密状态查询
 * - 密钥轮换管理
 * - 加密统计信息
 */
class VideoEncryptionController extends BaseController
{
    /**
     * 视频加密服务
     */
    private VideoEncryptionService $encryptionService;

    /**
     * 数据库管理服务
     */
    private DatabaseManagerService $dbManager;

    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->encryptionService = new VideoEncryptionService();
        $this->dbManager = new DatabaseManagerService();
    }

    /**
     * 返回成功响应
     */
    protected function success($data = null, string $message = 'success', int $code = 200): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 返回错误响应
     */
    protected function error(string $message = 'error', int $code = 400, $data = null): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 获取用户ID（模拟方法）
     */
    protected function getUserId(): int
    {
        return 1; // 临时返回固定值
    }

    /**
     * 获取视频解密密钥
     * 
     * HLS播放器会调用此接口获取解密密钥
     * 需要验证请求的合法性，防止密钥泄露
     * 
     * @param Request $request
     * @param string $keyId 密钥ID
     * @return Response
     */
    public function getKey(Request $request, string $keyId): Response
    {
        try {
            // 记录密钥请求日志（包含IP和User-Agent用于安全审计）
            $clientIp = $request->ip();
            $userAgent = $request->header('User-Agent', '');
            
            Log::info('收到密钥获取请求', [
                'key_id' => $keyId,
                'client_ip' => $clientIp,
                'user_agent' => $userAgent,
                'referer' => $request->header('Referer', '')
            ]);

            // 验证请求来源（可选的安全检查）
            if (!$this->validateKeyRequest($request, $keyId)) {
                Log::warning('密钥请求验证失败', [
                    'key_id' => $keyId,
                    'client_ip' => $clientIp
                ]);
                
                return $this->error('访问被拒绝', 403);
            }

            // 获取加密密钥信息
            $keyInfo = $this->encryptionService->getEncryptionKey($keyId);
            
            if (!$keyInfo) {
                Log::warning('请求的密钥不存在', [
                    'key_id' => $keyId,
                    'client_ip' => $clientIp
                ]);
                
                return $this->error('密钥不存在', 404);
            }

            // 检查密钥是否过期
            if (strtotime($keyInfo['expires_at']) < time()) {
                Log::warning('请求的密钥已过期', [
                    'key_id' => $keyId,
                    'expires_at' => $keyInfo['expires_at'],
                    'client_ip' => $clientIp
                ]);
                
                return $this->error('密钥已过期', 410);
            }

            // 记录密钥获取日志
            $this->logKeyAccess($keyInfo, $clientIp, $userAgent, 'success');

            // 解码密钥并返回原始二进制数据
            $rawKey = base64_decode($keyInfo['encryption_key']);
            
            // 设置响应头
            $response = response($rawKey);
            $response->header([
                'Content-Type' => 'application/octet-stream',
                'Content-Length' => strlen($rawKey),
                'Cache-Control' => 'private, max-age=3600', // 允许缓存1小时
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, OPTIONS',
                'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-API-Key, Cache-Control, Pragma, Expires, If-Modified-Since, If-None-Match, Accept'
            ]);

            Log::info('密钥获取成功', [
                'key_id' => $keyId,
                'video_id' => $keyInfo['video_id'],
                'client_ip' => $clientIp
            ]);

            return $response;

        } catch (\Exception $e) {
            Log::error('密钥获取失败', [
                'key_id' => $keyId,
                'client_ip' => $request->ip(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 记录失败日志
            $this->logKeyAccess(['key_id' => $keyId], $request->ip(), $request->header('User-Agent', ''), 'failed', $e->getMessage());

            return $this->error('密钥获取失败', 500);
        }
    }

    /**
     * 获取视频的加密状态
     * 
     * 查询指定视频的加密状态和密钥信息
     * 
     * @param Request $request
     * @param int $videoId 视频ID
     * @return Response
     */
    public function getEncryptionStatus(Request $request, int $videoId): Response
    {
        try {
            // 查询视频的加密密钥信息
            $keyInfo = $this->dbManager->readTable('video_encryption_keys', 'video')
                ->where('video_id', $videoId)
                ->where('is_active', 1)
                ->order('created_at', 'desc')
                ->find();

            if (!$keyInfo) {
                return $this->success([
                    'video_id' => $videoId,
                    'encrypted' => false,
                    'message' => '视频未加密'
                ]);
            }

            // 检查密钥是否过期
            $isExpired = strtotime($keyInfo['expires_at']) < time();

            $status = [
                'video_id' => $videoId,
                'encrypted' => true,
                'key_id' => $keyInfo['key_id'],
                'method' => $keyInfo['method'],
                'created_at' => $keyInfo['created_at'],
                'expires_at' => $keyInfo['expires_at'],
                'is_expired' => $isExpired,
                'status' => $isExpired ? 'expired' : 'active'
            ];

            return $this->success($status);

        } catch (\Exception $e) {
            Log::error('获取加密状态失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取加密状态失败');
        }
    }

    /**
     * 手动轮换视频密钥
     * 
     * 管理员可以手动触发密钥轮换
     * 
     * @param Request $request
     * @return Response
     */
    public function rotateKeys(Request $request): Response
    {
        try {
            // 执行密钥轮换
            $result = $this->encryptionService->rotateExpiredKeys();

            Log::info('手动密钥轮换完成', [
                'admin_id' => $this->getUserId(),
                'result' => $result
            ]);

            return $this->success($result, '密钥轮换完成');

        } catch (\Exception $e) {
            Log::error('手动密钥轮换失败', [
                'admin_id' => $this->getUserId(),
                'error' => $e->getMessage()
            ]);

            return $this->error('密钥轮换失败');
        }
    }

    /**
     * 获取加密统计信息
     * 
     * 提供加密相关的统计数据
     * 
     * @param Request $request
     * @return Response
     */
    public function getEncryptionStats(Request $request): Response
    {
        try {
            // 统计活跃密钥数量
            $activeKeys = $this->dbManager->readTable('video_encryption_keys', 'video')
                ->where('is_active', 1)
                ->where('expires_at', '>', date('Y-m-d H:i:s'))
                ->count();

            // 统计过期密钥数量
            $expiredKeys = $this->dbManager->readTable('video_encryption_keys', 'video')
                ->where('is_active', 1)
                ->where('expires_at', '<=', date('Y-m-d H:i:s'))
                ->count();

            // 统计加密视频数量
            $encryptedVideos = $this->dbManager->readTable('video_encryption_keys', 'video')
                ->where('is_active', 1)
                ->group('video_id')
                ->count();

            // 统计今日密钥访问次数
            $todayAccess = $this->dbManager->readTable('video_encryption_logs', 'log')
                ->where('operation', 'key_access')
                ->where('created_at', '>=', date('Y-m-d 00:00:00'))
                ->count();

            // 统计最近7天的密钥轮换次数
            $recentRotations = $this->dbManager->readTable('video_encryption_logs', 'log')
                ->where('operation', 'key_rotate')
                ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-7 days')))
                ->count();

            $stats = [
                'active_keys' => $activeKeys,
                'expired_keys' => $expiredKeys,
                'encrypted_videos' => $encryptedVideos,
                'today_key_access' => $todayAccess,
                'recent_rotations' => $recentRotations,
                'encryption_enabled' => config('video.encryption.enabled', false),
                'key_rotation_interval' => config('video.encryption.key_rotation_interval', 86400)
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取加密统计失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('获取统计信息失败');
        }
    }

    /**
     * 验证密钥请求的合法性
     * 
     * 可以根据需要添加更多的安全检查
     * 
     * @param Request $request
     * @param string $keyId
     * @return bool
     */
    private function validateKeyRequest(Request $request, string $keyId): bool
    {
        // 检查请求频率限制 - 放宽限制，允许更多请求
        $clientIp = $request->ip();
        $rateLimitKey = 'key_request_rate_limit_' . $clientIp;
        $requestCount = Cache::get($rateLimitKey, 0);

        if ($requestCount > 500) { // 每分钟最多500次请求（放宽限制）
            return false;
        }

        Cache::set($rateLimitKey, $requestCount + 1, 60);

        // 对于本地开发环境，放宽验证
        if (strpos($clientIp, '127.0.0.1') !== false || strpos($clientIp, '::1') !== false || $clientIp === 'localhost') {
            return true;
        }

        // 可以添加更多验证逻辑：
        // - 检查Referer头
        // - 验证User-Agent
        // - 检查IP白名单
        // - 验证时间戳防重放攻击

        return true;
    }

    /**
     * 记录密钥访问日志
     * 
     * @param array $keyInfo 密钥信息
     * @param string $clientIp 客户端IP
     * @param string $userAgent 用户代理
     * @param string $status 访问状态
     * @param string $errorMessage 错误信息
     * @return void
     */
    private function logKeyAccess(array $keyInfo, string $clientIp, string $userAgent, string $status, string $errorMessage = ''): void
    {
        try {
            $logData = [
                'video_id' => $keyInfo['video_id'] ?? 0,
                'key_id' => $keyInfo['key_id'] ?? '',
                'operation' => 'key_access',
                'status' => $status,
                'error_message' => $errorMessage,
                'ip_address' => $clientIp,
                'user_agent' => $userAgent,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->dbManager->writeTable('video_encryption_logs', 'log')->insert($logData);

        } catch (\Exception $e) {
            Log::error('记录密钥访问日志失败', [
                'error' => $e->getMessage(),
                'log_data' => $logData ?? []
            ]);
        }
    }
}
