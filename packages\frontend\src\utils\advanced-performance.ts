/**
 * 🚀 高级性能监控工具
 * 
 * 提供全面的性能监控、资源优化、用户体验指标等功能
 * 
 * <AUTHOR> Video Platform Team
 * @version 2.0.0
 */

import { config } from '@/config/env'

// 高级性能指标接口
interface AdvancedPerformanceMetrics {
  // 核心Web指标
  pageLoadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
  
  // 自定义指标
  timeToInteractive: number
  totalBlockingTime: number
  speedIndex: number
  
  // 资源指标
  resourceLoadTime: Record<string, number>
  resourceSizes: Record<string, number>
  criticalResourceCount: number
  
  // 网络指标
  connectionType: string
  effectiveType: string
  downlink: number
  rtt: number
  
  // 内存指标
  memoryUsage?: MemoryInfo
  
  // 用户体验指标
  userInteractions: number
  errorCount: number
  
  // 视频特定指标
  videoLoadTime: number
  videoBufferHealth: number
  videoQualitySwitches: number
}

// 性能阈值配置
const PERFORMANCE_THRESHOLDS = {
  FCP: 1800,  // 首次内容绘制 < 1.8s
  LCP: 2500,  // 最大内容绘制 < 2.5s
  FID: 100,   // 首次输入延迟 < 100ms
  CLS: 0.1,   // 累积布局偏移 < 0.1
  TTI: 3800,  // 可交互时间 < 3.8s
  TBT: 200    // 总阻塞时间 < 200ms
}

// 高性能监控类
class AdvancedPerformanceMonitor {
  private metrics: Partial<AdvancedPerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []
  private startTime: number = performance.now()
  private isEnabled: boolean = config.performanceMonitor
  private reportQueue: any[] = []
  private reportTimer: number | null = null

  constructor() {
    if (this.isEnabled) {
      this.init()
    }
  }

  /**
   * 初始化性能监控
   */
  private init(): void {
    // 基础性能监控
    this.observeNavigation()
    this.observePaint()
    this.observeLayoutShift()
    this.observeResources()
    this.observeLongTasks()
    
    // 网络信息监控
    this.observeNetworkInfo()
    
    // 用户交互监控
    this.observeUserInteractions()
    
    // 错误监控
    this.observeErrors()
    
    // 视频性能监控
    this.observeVideoPerformance()
    
    // 定期报告
    this.setupPeriodicReporting()
    
    // 页面卸载时收集最终指标
    window.addEventListener('beforeunload', () => {
      this.collectFinalMetrics()
    })
    
    // 页面可见性变化监控
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.flushReports()
      }
    })
  }

  /**
   * 监控导航性能
   */
  private observeNavigation(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            this.metrics.pageLoadTime = navEntry.loadEventEnd - navEntry.loadEventStart
            
            // 计算更多指标
            this.calculateAdvancedMetrics(navEntry)
          }
        })
      })
      
      observer.observe({ entryTypes: ['navigation'] })
      this.observers.push(observer)
    }
  }

  /**
   * 计算高级性能指标
   */
  private calculateAdvancedMetrics(navEntry: PerformanceNavigationTiming): void {
    // 计算可交互时间 (简化版)
    this.metrics.timeToInteractive = navEntry.domInteractive - navEntry.navigationStart
    
    // 记录关键时间点
    const timings = {
      dns: navEntry.domainLookupEnd - navEntry.domainLookupStart,
      tcp: navEntry.connectEnd - navEntry.connectStart,
      ssl: navEntry.secureConnectionStart > 0 ? navEntry.connectEnd - navEntry.secureConnectionStart : 0,
      ttfb: navEntry.responseStart - navEntry.navigationStart,
      download: navEntry.responseEnd - navEntry.responseStart,
      domParse: navEntry.domInteractive - navEntry.responseEnd,
      domReady: navEntry.domContentLoadedEventEnd - navEntry.navigationStart,
      loadComplete: navEntry.loadEventEnd - navEntry.navigationStart
    }
    
    this.queueReport('navigation-timing', timings)
  }

  /**
   * 监控绘制性能
   */
  private observePaint(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime
            this.checkThreshold('FCP', entry.startTime)
          }
        })
      })
      
      observer.observe({ entryTypes: ['paint'] })
      this.observers.push(observer)
    }
  }

  /**
   * 监控布局偏移
   */
  private observeLayoutShift(): void {
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
            clsValue += (entry as any).value
            this.metrics.cumulativeLayoutShift = clsValue
            this.checkThreshold('CLS', clsValue)
          }
        })
      })
      
      observer.observe({ entryTypes: ['layout-shift'] })
      this.observers.push(observer)
    }
  }

  /**
   * 监控资源加载
   */
  private observeResources(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming
            if (!this.metrics.resourceLoadTime) {
              this.metrics.resourceLoadTime = {}
            }
            this.metrics.resourceLoadTime[resourceEntry.name] = resourceEntry.duration
            
            // 记录资源大小
            if (!this.metrics.resourceSizes) {
              this.metrics.resourceSizes = {}
            }
            this.metrics.resourceSizes[resourceEntry.name] = resourceEntry.transferSize || 0
          }
        })
      })
      
      observer.observe({ entryTypes: ['resource'] })
      this.observers.push(observer)
    }
  }

  /**
   * 监控长任务
   */
  private observeLongTasks(): void {
    if ('PerformanceObserver' in window) {
      let totalBlockingTime = 0
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'longtask') {
            const blockingTime = Math.max(0, entry.duration - 50)
            totalBlockingTime += blockingTime
            this.metrics.totalBlockingTime = totalBlockingTime
            
            // 记录长任务详情
            this.queueReport('long-task', {
              duration: entry.duration,
              startTime: entry.startTime,
              blockingTime
            })
          }
        })
      })
      
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.push(observer)
    }
  }

  /**
   * 监控网络信息
   */
  private observeNetworkInfo(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      this.metrics.connectionType = connection.type || 'unknown'
      this.metrics.effectiveType = connection.effectiveType || 'unknown'
      this.metrics.downlink = connection.downlink || 0
      this.metrics.rtt = connection.rtt || 0
      
      // 监听网络变化
      connection.addEventListener('change', () => {
        this.queueReport('network-change', {
          type: connection.type,
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        })
      })
    }
  }

  /**
   * 监控用户交互
   */
  private observeUserInteractions(): void {
    let interactionCount = 0
    
    const events = ['click', 'keydown', 'scroll', 'touchstart']
    events.forEach(eventType => {
      document.addEventListener(eventType, () => {
        interactionCount++
        this.metrics.userInteractions = interactionCount
      }, { passive: true })
    })
    
    // 监控首次输入延迟
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'first-input') {
            this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime
            this.checkThreshold('FID', this.metrics.firstInputDelay)
          }
        })
      })
      
      observer.observe({ entryTypes: ['first-input'] })
      this.observers.push(observer)
    }
  }

  /**
   * 监控错误
   */
  private observeErrors(): void {
    let errorCount = 0
    
    window.addEventListener('error', (event) => {
      errorCount++
      this.metrics.errorCount = errorCount
      
      this.queueReport('javascript-error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      })
    })
    
    window.addEventListener('unhandledrejection', (event) => {
      errorCount++
      this.metrics.errorCount = errorCount
      
      this.queueReport('promise-rejection', {
        reason: event.reason,
        stack: event.reason?.stack
      })
    })
  }

  /**
   * 监控视频性能
   */
  private observeVideoPerformance(): void {
    // 监控视频元素
    const observeVideoElement = (video: HTMLVideoElement) => {
      const startTime = performance.now()
      
      video.addEventListener('loadstart', () => {
        this.queueReport('video-load-start', { src: video.src })
      })
      
      video.addEventListener('canplay', () => {
        this.metrics.videoLoadTime = performance.now() - startTime
        this.queueReport('video-can-play', { 
          loadTime: this.metrics.videoLoadTime,
          src: video.src 
        })
      })
      
      video.addEventListener('waiting', () => {
        this.queueReport('video-buffering', { src: video.src })
      })
      
      video.addEventListener('playing', () => {
        this.queueReport('video-playing', { src: video.src })
      })
    }
    
    // 观察现有视频元素
    document.querySelectorAll('video').forEach(observeVideoElement)
    
    // 观察新添加的视频元素
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            if (element.tagName === 'VIDEO') {
              observeVideoElement(element as HTMLVideoElement)
            }
            element.querySelectorAll('video').forEach(observeVideoElement)
          }
        })
      })
    })
    
    observer.observe(document.body, { childList: true, subtree: true })
  }

  /**
   * 检查性能阈值
   */
  private checkThreshold(metric: keyof typeof PERFORMANCE_THRESHOLDS, value: number): void {
    const threshold = PERFORMANCE_THRESHOLDS[metric]
    if (value > threshold) {
      this.queueReport('performance-threshold-exceeded', {
        metric,
        value,
        threshold,
        severity: value > threshold * 1.5 ? 'high' : 'medium'
      })
    }
  }

  /**
   * 队列报告
   */
  private queueReport(type: string, data: any): void {
    this.reportQueue.push({
      type,
      data,
      timestamp: Date.now(),
      url: window.location.href
    })
    
    // 队列满时立即发送
    if (this.reportQueue.length >= 10) {
      this.flushReports()
    }
  }

  /**
   * 设置定期报告
   */
  private setupPeriodicReporting(): void {
    this.reportTimer = window.setInterval(() => {
      if (this.reportQueue.length > 0) {
        this.flushReports()
      }
    }, 30000) // 每30秒发送一次
  }

  /**
   * 刷新报告队列
   */
  private flushReports(): void {
    if (this.reportQueue.length === 0) return
    
    const reports = [...this.reportQueue]
    this.reportQueue = []
    
    this.sendReports(reports)
  }

  /**
   * 发送报告到服务器
   */
  private sendReports(reports: any[]): void {
    if (!navigator.sendBeacon && !fetch) return
    
    const payload = {
      reports,
      session: this.getSessionInfo(),
      metrics: this.getMetrics()
    }
    
    const data = JSON.stringify(payload)
    
    if (navigator.sendBeacon) {
      navigator.sendBeacon('/api/performance/reports', data)
    } else {
      fetch('/api/performance/reports', {
        method: 'POST',
        body: data,
        headers: { 'Content-Type': 'application/json' },
        keepalive: true
      }).catch(() => {
        // 静默处理错误
      })
    }
  }

  /**
   * 获取会话信息
   */
  private getSessionInfo() {
    return {
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      connection: this.getConnectionInfo(),
      timestamp: Date.now()
    }
  }

  /**
   * 获取连接信息
   */
  private getConnectionInfo() {
    if ('connection' in navigator) {
      const conn = (navigator as any).connection
      return {
        type: conn.type,
        effectiveType: conn.effectiveType,
        downlink: conn.downlink,
        rtt: conn.rtt
      }
    }
    return null
  }

  /**
   * 收集最终指标
   */
  private collectFinalMetrics(): void {
    // 收集内存使用情况
    if ('memory' in performance) {
      this.metrics.memoryUsage = (performance as any).memory
    }
    
    // 发送最终报告
    this.flushReports()
  }

  /**
   * 获取当前性能指标
   */
  getMetrics(): Partial<AdvancedPerformanceMetrics> {
    return { ...this.metrics }
  }

  /**
   * 手动记录自定义指标
   */
  recordCustomMetric(name: string, value: number, unit = 'ms'): void {
    this.queueReport('custom-metric', { name, value, unit })
  }

  /**
   * 开始性能测量
   */
  startMeasure(name: string): void {
    performance.mark(`${name}-start`)
  }

  /**
   * 结束性能测量
   */
  endMeasure(name: string): number {
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)
    
    const measure = performance.getEntriesByName(name, 'measure')[0]
    const duration = measure.duration
    
    this.recordCustomMetric(name, duration)
    
    // 清理标记
    performance.clearMarks(`${name}-start`)
    performance.clearMarks(`${name}-end`)
    performance.clearMeasures(name)
    
    return duration
  }

  /**
   * 清理监控器
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    
    if (this.reportTimer) {
      clearInterval(this.reportTimer)
      this.reportTimer = null
    }
    
    this.flushReports()
  }
}

// 创建全局高级性能监控实例
export const advancedPerformanceMonitor = new AdvancedPerformanceMonitor()

// 导出性能监控类和工具函数
export default AdvancedPerformanceMonitor

// 便捷的性能测量装饰器
export function measurePerformance(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value
  
  descriptor.value = function (...args: any[]) {
    const measureName = `${target.constructor.name}.${propertyName}`
    advancedPerformanceMonitor.startMeasure(measureName)
    
    try {
      const result = method.apply(this, args)
      
      if (result instanceof Promise) {
        return result.finally(() => {
          advancedPerformanceMonitor.endMeasure(measureName)
        })
      } else {
        advancedPerformanceMonitor.endMeasure(measureName)
        return result
      }
    } catch (error) {
      advancedPerformanceMonitor.endMeasure(measureName)
      throw error
    }
  }
  
  return descriptor
}
