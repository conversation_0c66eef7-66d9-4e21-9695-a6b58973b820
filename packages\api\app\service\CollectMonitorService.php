<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 采集监控服务
 * 
 * 提供采集任务监控、失败重试、统计报表等功能
 */
class CollectMonitorService
{
    /**
     * 获取采集统计数据
     */
    public function getCollectStatistics(string $period = 'today'): array
    {
        $dateCondition = $this->getDateCondition($period);
        
        // 基础统计
        $stats = [
            'tasks' => $this->getTaskStatistics($dateCondition),
            'videos' => $this->getVideoStatistics($dateCondition),
            'sources' => $this->getSourceStatistics(),
            'errors' => $this->getErrorStatistics($dateCondition)
        ];
        
        return $stats;
    }
    
    /**
     * 获取任务统计
     */
    private function getTaskStatistics(string $dateCondition): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_tasks,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_tasks,
                    COUNT(CASE WHEN status = 'running' THEN 1 END) as running_tasks,
                    SUM(collected_count) as total_collected,
                    SUM(success_count) as total_success,
                    SUM(failed_count) as total_failed
                FROM collect_tasks 
                WHERE {$dateCondition}";
                
        return Db::query($sql)[0] ?? [];
    }
    
    /**
     * 获取视频统计
     */
    private function getVideoStatistics(string $dateCondition): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_videos,
                    COUNT(CASE WHEN source_type = 'collect' THEN 1 END) as collected_videos,
                    AVG(view_count) as avg_views,
                    SUM(view_count) as total_views
                FROM videos 
                WHERE {$dateCondition}";
                
        return Db::query($sql)[0] ?? [];
    }
    
    /**
     * 获取采集源统计
     */
    private function getSourceStatistics(): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_sources,
                    COUNT(CASE WHEN status = 1 THEN 1 END) as active_sources,
                    AVG(success_count) as avg_success_rate,
                    SUM(total_collected) as total_collected
                FROM collect_sources";
                
        return Db::query($sql)[0] ?? [];
    }
    
    /**
     * 获取错误统计
     */
    private function getErrorStatistics(string $dateCondition): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_errors,
                    COUNT(DISTINCT source_id) as error_sources,
                    COUNT(DISTINCT task_id) as error_tasks
                FROM collect_logs 
                WHERE operation_type = 'error' AND {$dateCondition}";
                
        return Db::query($sql)[0] ?? [];
    }
    
    /**
     * 获取日期条件
     */
    private function getDateCondition(string $period): string
    {
        switch ($period) {
            case 'today':
                return "DATE(created_at) = CURDATE()";
            case 'week':
                return "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            case 'month':
                return "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            default:
                return "1=1";
        }
    }
    
    /**
     * 检查失败任务并重试
     */
    public function retryFailedTasks(int $maxRetries = 3): array
    {
        $failedTasks = Db::table('collect_tasks')
            ->where('status', 'failed')
            ->where('run_count', '<', $maxRetries)
            ->select();
            
        $retryResults = [];
        
        foreach ($failedTasks as $task) {
            try {
                // 重置任务状态
                Db::table('collect_tasks')
                    ->where('id', $task['id'])
                    ->update([
                        'status' => 'pending',
                        'run_count' => $task['run_count'] + 1,
                        'error_message' => null,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    
                $retryResults[] = [
                    'task_id' => $task['id'],
                    'task_name' => $task['task_name'],
                    'retry_count' => $task['run_count'] + 1,
                    'status' => 'scheduled'
                ];
                
                Log::info('任务重试调度', [
                    'task_id' => $task['id'],
                    'retry_count' => $task['run_count'] + 1
                ]);
                
            } catch (\Exception $e) {
                $retryResults[] = [
                    'task_id' => $task['id'],
                    'task_name' => $task['task_name'],
                    'status' => 'retry_failed',
                    'error' => $e->getMessage()
                ];
                
                Log::error('任务重试失败', [
                    'task_id' => $task['id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $retryResults;
    }
    
    /**
     * 清理过期任务和日志
     */
    public function cleanupOldData(int $daysToKeep = 30): array
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        $results = [];
        
        try {
            // 清理完成的任务（保留失败的任务用于分析）
            $deletedTasks = Db::table('collect_tasks')
                ->where('status', 'completed')
                ->where('completed_at', '<', $cutoffDate)
                ->delete();
                
            $results['deleted_tasks'] = $deletedTasks;
            
            // 清理旧日志（保留错误日志）
            $deletedLogs = Db::table('collect_logs')
                ->where('operation_type', 'not in', ['error', 'failed'])
                ->where('created_at', '<', $cutoffDate)
                ->delete();
                
            $results['deleted_logs'] = $deletedLogs;
            
            Log::info('数据清理完成', $results);
            
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
            Log::error('数据清理失败', ['error' => $e->getMessage()]);
        }
        
        return $results;
    }
    
    /**
     * 获取采集源健康状态
     */
    public function getSourceHealthStatus(): array
    {
        $sources = Db::table('collect_sources')
            ->where('status', 1)
            ->select();
            
        $healthStatus = [];
        
        foreach ($sources as $source) {
            $recentErrors = Db::table('collect_logs')
                ->where('source_id', $source['id'])
                ->where('operation_type', 'error')
                ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                ->count();
                
            $successRate = $source['total_collected'] > 0 
                ? round(($source['success_count'] / $source['total_collected']) * 100, 2)
                : 0;
                
            $healthStatus[] = [
                'source_id' => $source['id'],
                'source_name' => $source['name'],
                'success_rate' => $successRate,
                'recent_errors' => $recentErrors,
                'last_collect_time' => $source['last_collect_time'],
                'health_score' => $this->calculateHealthScore($successRate, $recentErrors),
                'status' => $this->getHealthStatusText($successRate, $recentErrors)
            ];
        }
        
        return $healthStatus;
    }
    
    /**
     * 计算健康分数
     */
    private function calculateHealthScore(float $successRate, int $recentErrors): int
    {
        $score = $successRate;
        
        // 根据最近错误数量扣分
        if ($recentErrors > 10) {
            $score -= 30;
        } elseif ($recentErrors > 5) {
            $score -= 20;
        } elseif ($recentErrors > 0) {
            $score -= 10;
        }
        
        return max(0, min(100, (int)$score));
    }
    
    /**
     * 获取健康状态文本
     */
    private function getHealthStatusText(float $successRate, int $recentErrors): string
    {
        if ($successRate >= 90 && $recentErrors <= 2) {
            return 'healthy';
        } elseif ($successRate >= 70 && $recentErrors <= 5) {
            return 'warning';
        } else {
            return 'critical';
        }
    }
    
    /**
     * 生成采集报表
     */
    public function generateCollectReport(string $period = 'week'): array
    {
        $stats = $this->getCollectStatistics($period);
        $sourceHealth = $this->getSourceHealthStatus();
        
        // 获取热门分类
        $topCategories = $this->getTopCategories($period);
        
        // 获取采集趋势
        $trends = $this->getCollectTrends($period);
        
        return [
            'period' => $period,
            'generated_at' => date('Y-m-d H:i:s'),
            'statistics' => $stats,
            'source_health' => $sourceHealth,
            'top_categories' => $topCategories,
            'trends' => $trends,
            'recommendations' => $this->generateRecommendations($stats, $sourceHealth)
        ];
    }
    
    /**
     * 获取热门分类
     */
    private function getTopCategories(string $period): array
    {
        $dateCondition = $this->getDateCondition($period);
        
        $sql = "SELECT 
                    c.id, c.name, 
                    COUNT(v.id) as video_count,
                    SUM(v.view_count) as total_views
                FROM categories c
                LEFT JOIN videos v ON c.id = v.category_id 
                WHERE v.source_type = 'collect' AND {$dateCondition}
                GROUP BY c.id, c.name
                ORDER BY video_count DESC
                LIMIT 10";
                
        return Db::query($sql);
    }
    
    /**
     * 获取采集趋势
     */
    private function getCollectTrends(string $period): array
    {
        $dateCondition = $this->getDateCondition($period);
        
        $sql = "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as videos_collected,
                    SUM(view_count) as total_views
                FROM videos 
                WHERE source_type = 'collect' AND {$dateCondition}
                GROUP BY DATE(created_at)
                ORDER BY date DESC
                LIMIT 30";
                
        return Db::query($sql);
    }
    
    /**
     * 生成建议
     */
    private function generateRecommendations(array $stats, array $sourceHealth): array
    {
        $recommendations = [];
        
        // 检查失败率
        $taskStats = $stats['tasks'] ?? [];
        if (isset($taskStats['total_failed']) && isset($taskStats['total_collected'])) {
            $failureRate = $taskStats['total_collected'] > 0 
                ? ($taskStats['total_failed'] / $taskStats['total_collected']) * 100 
                : 0;
                
            if ($failureRate > 20) {
                $recommendations[] = [
                    'type' => 'warning',
                    'message' => "采集失败率较高 ({$failureRate}%)，建议检查采集源配置和网络连接"
                ];
            }
        }
        
        // 检查采集源健康状态
        $criticalSources = array_filter($sourceHealth, function($source) {
            return $source['status'] === 'critical';
        });
        
        if (count($criticalSources) > 0) {
            $recommendations[] = [
                'type' => 'critical',
                'message' => "有 " . count($criticalSources) . " 个采集源状态异常，需要立即处理"
            ];
        }
        
        return $recommendations;
    }

    /**
     * 自动健康检查服务
     * 定期检查所有采集源的健康状态
     */
    public function autoHealthCheck(): array
    {
        Log::info('开始自动健康检查');
        
        $results = [
            'checked_sources' => 0,
            'healthy_sources' => 0,
            'warning_sources' => 0,
            'critical_sources' => 0,
            'auto_recovered' => 0,
            'details' => []
        ];
        
        try {
            // 获取所有启用的采集源
            $sources = Db::table('collect_sources')
                ->where('status', 1)
                ->select();
            
            $results['checked_sources'] = count($sources);
            
            foreach ($sources as $source) {
                $healthResult = $this->checkSingleSourceHealth($source);
                $results['details'][] = $healthResult;
                
                // 统计健康状态
                switch ($healthResult['status']) {
                    case 'healthy':
                        $results['healthy_sources']++;
                        break;
                    case 'warning':
                        $results['warning_sources']++;
                        // 尝试自动恢复
                        if ($this->attemptAutoRecovery($source, $healthResult)) {
                            $results['auto_recovered']++;
                        }
                        break;
                    case 'critical':
                        $results['critical_sources']++;
                        // 记录严重问题
                        $this->logCriticalIssue($source, $healthResult);
                        // 尝试自动恢复
                        if ($this->attemptAutoRecovery($source, $healthResult)) {
                            $results['auto_recovered']++;
                        }
                        break;
                }
                
                // 更新采集源健康状态
                $this->updateSourceHealthStatus($source['id'], $healthResult);
            }
            
            Log::info('自动健康检查完成', $results);
            
        } catch (\Exception $e) {
            Log::error('自动健康检查失败', ['error' => $e->getMessage()]);
            $results['error'] = $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * 检查单个采集源健康状态
     */
    private function checkSingleSourceHealth(array $source): array
    {
        $healthData = [
            'source_id' => $source['id'],
            'source_name' => $source['name'],
            'api_url' => $source['api_url'],
            'check_time' => date('Y-m-d H:i:s'),
            'tests' => [],
            'status' => 'unknown',
            'issues' => []
        ];
        
        try {
            // 1. 连接测试
            $connectTest = $this->testConnection($source);
            $healthData['tests']['connection'] = $connectTest;
            
            if (!$connectTest['success']) {
                $healthData['issues'][] = '连接失败: ' . $connectTest['error'];
            }
            
            // 2. API响应测试
            $apiTest = $this->testApiResponse($source);
            $healthData['tests']['api_response'] = $apiTest;
            
            if (!$apiTest['success']) {
                $healthData['issues'][] = 'API响应异常: ' . $apiTest['error'];
            }
            
            // 3. 数据格式测试
            $formatTest = $this->testDataFormat($source);
            $healthData['tests']['data_format'] = $formatTest;
            
            if (!$formatTest['success']) {
                $healthData['issues'][] = '数据格式错误: ' . $formatTest['error'];
            }
            
            // 4. 检查最近错误率
            $errorRate = $this->getRecentErrorRate($source['id']);
            $healthData['tests']['error_rate'] = [
                'success' => $errorRate < 30,
                'error_rate' => $errorRate,
                'threshold' => 30
            ];
            
            if ($errorRate >= 30) {
                $healthData['issues'][] = "最近错误率过高: {$errorRate}%";
            }
            
            // 5. 检查最后采集时间
            $lastCollectCheck = $this->checkLastCollectTime($source);
            $healthData['tests']['last_collect'] = $lastCollectCheck;
            
            if (!$lastCollectCheck['success']) {
                $healthData['issues'][] = $lastCollectCheck['message'];
            }
            
            // 计算整体健康状态
            $healthData['status'] = $this->calculateOverallHealth($healthData['tests']);
            
        } catch (\Exception $e) {
            $healthData['status'] = 'critical';
            $healthData['issues'][] = '健康检查异常: ' . $e->getMessage();
            Log::error('采集源健康检查失败', [
                'source_id' => $source['id'],
                'error' => $e->getMessage()
            ]);
        }
        
        return $healthData;
    }
    
    /**
     * 测试连接
     */
    private function testConnection(array $source): array
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $source['api_url']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $startTime = microtime(true);
            $result = curl_exec($ch);
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $success = $httpCode >= 200 && $httpCode < 400 && empty($error);
            
            return [
                'success' => $success,
                'http_code' => $httpCode,
                'response_time' => round($responseTime, 2),
                'error' => $error ?: null
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 测试API响应
     */
    private function testApiResponse(array $source): array
    {
        try {
            $testUrl = $source['api_url'];
            if (strpos($testUrl, '?') === false) {
                $testUrl .= '?';
            } else {
                $testUrl .= '&';
            }
            $testUrl .= 'ac=list&pg=1';
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $testUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $success = !empty($response) && $httpCode == 200 && empty($error);
            
            return [
                'success' => $success,
                'response_length' => strlen($response),
                'http_code' => $httpCode,
                'error' => $error ?: null
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 测试数据格式
     */
    private function testDataFormat(array $source): array
    {
        try {
            $testUrl = $source['api_url'];
            if (strpos($testUrl, '?') === false) {
                $testUrl .= '?';
            } else {
                $testUrl .= '&';
            }
            $testUrl .= 'ac=list&pg=1';
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $testUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            curl_close($ch);
            
            if (empty($response)) {
                return ['success' => false, 'error' => '响应为空'];
            }
            
            if ($source['api_type'] === 'json') {
                $data = json_decode($response, true);
                $hasValidStructure = isset($data['list']) && is_array($data['list']);
                
                return [
                    'success' => $hasValidStructure,
                    'format' => 'json',
                    'has_list' => isset($data['list']),
                    'list_count' => isset($data['list']) ? count($data['list']) : 0,
                    'error' => $hasValidStructure ? null : 'JSON格式不正确或缺少list字段'
                ];
            } else {
                // XML格式检查
                $hasValidStructure = strpos($response, '<rss') !== false || 
                                   strpos($response, '<list>') !== false ||
                                   strpos($response, '<?xml') !== false;
                
                return [
                    'success' => $hasValidStructure,
                    'format' => 'xml',
                    'has_xml_structure' => $hasValidStructure,
                    'error' => $hasValidStructure ? null : 'XML格式不正确'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取最近错误率
     */
    private function getRecentErrorRate(int $sourceId): float
    {
        try {
            $totalLogs = Db::table('collect_logs')
                ->where('source_id', $sourceId)
                ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                ->count();
            
            if ($totalLogs == 0) {
                return 0;
            }
            
            $errorLogs = Db::table('collect_logs')
                ->where('source_id', $sourceId)
                ->where('operation_type', 'in', ['error', 'failed'])
                ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                ->count();
            
            return round(($errorLogs / $totalLogs) * 100, 2);
            
        } catch (\Exception $e) {
            Log::error('获取错误率失败', ['source_id' => $sourceId, 'error' => $e->getMessage()]);
            return 0;
        }
    }
    
    /**
     * 检查最后采集时间
     */
    private function checkLastCollectTime(array $source): array
    {
        $lastCollectTime = $source['last_collect_time'];
        
        if (empty($lastCollectTime)) {
            return [
                'success' => false,
                'message' => '从未进行过采集',
                'last_collect_time' => null
            ];
        }
        
        $lastTime = strtotime($lastCollectTime);
        $hoursSinceLastCollect = (time() - $lastTime) / 3600;
        
        // 如果超过24小时没有采集，认为异常
        if ($hoursSinceLastCollect > 24) {
            return [
                'success' => false,
                'message' => "超过24小时未采集 (上次: {$lastCollectTime})",
                'last_collect_time' => $lastCollectTime,
                'hours_since' => round($hoursSinceLastCollect, 1)
            ];
        }
        
        return [
            'success' => true,
            'message' => '采集时间正常',
            'last_collect_time' => $lastCollectTime,
            'hours_since' => round($hoursSinceLastCollect, 1)
        ];
    }
    
    /**
     * 计算整体健康状态
     */
    private function calculateOverallHealth(array $tests): string
    {
        $totalTests = count($tests);
        $passedTests = 0;
        
        foreach ($tests as $test) {
            if ($test['success']) {
                $passedTests++;
            }
        }
        
        $passRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        
        if ($passRate >= 80) {
            return 'healthy';
        } elseif ($passRate >= 60) {
            return 'warning';
        } else {
            return 'critical';
        }
    }
    
    /**
     * 尝试自动恢复
     */
    private function attemptAutoRecovery(array $source, array $healthResult): bool
    {
        try {
            Log::info('尝试自动恢复采集源', [
                'source_id' => $source['id'],
                'source_name' => $source['name'],
                'issues' => $healthResult['issues']
            ]);
            
            $recovered = false;
            
            // 1. 如果是连接问题，尝试重置连接
            if (isset($healthResult['tests']['connection']) && !$healthResult['tests']['connection']['success']) {
                $recovered = $this->resetConnection($source);
            }
            
            // 2. 如果是API响应问题，尝试清理缓存
            if (isset($healthResult['tests']['api_response']) && !$healthResult['tests']['api_response']['success']) {
                $this->clearSourceCache($source['id']);
                $recovered = true;
            }
            
            // 3. 如果错误率过高，暂停一段时间后重新启用
            if (isset($healthResult['tests']['error_rate']) && !$healthResult['tests']['error_rate']['success']) {
                $this->temporaryDisableSource($source['id']);
                $recovered = true;
            }
            
            if ($recovered) {
                // 记录恢复日志
                Db::table('collect_logs')->insert([
                    'source_id' => $source['id'],
                    'operation_type' => 'auto_recovery',
                    'operation_result' => 'success',
                    'details' => json_encode([
                        'issues' => $healthResult['issues'],
                        'recovery_actions' => '自动恢复尝试'
                    ]),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                Log::info('自动恢复成功', ['source_id' => $source['id']]);
            }
            
            return $recovered;
            
        } catch (\Exception $e) {
            Log::error('自动恢复失败', [
                'source_id' => $source['id'],
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 重置连接
     */
    private function resetConnection(array $source): bool
    {
        // 这里可以实现连接重置逻辑
        // 例如：清理DNS缓存、重置curl句柄等
        return true;
    }
    
    /**
     * 清理采集源缓存
     */
    private function clearSourceCache(int $sourceId): void
    {
        Cache::delete("collect_source_{$sourceId}");
        Cache::delete("collect_categories_{$sourceId}");
    }
    
    /**
     * 临时禁用采集源
     */
    private function temporaryDisableSource(int $sourceId): void
    {
        // 设置临时禁用标记，30分钟后自动恢复
        Cache::set("source_temp_disabled_{$sourceId}", time(), 1800);
    }
    
    /**
     * 记录严重问题
     */
    private function logCriticalIssue(array $source, array $healthResult): void
    {
        Db::table('collect_logs')->insert([
            'source_id' => $source['id'],
            'operation_type' => 'critical_issue',
            'operation_result' => 'failed',
            'details' => json_encode([
                'issues' => $healthResult['issues'],
                'tests' => $healthResult['tests'],
                'check_time' => $healthResult['check_time']
            ]),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 更新采集源健康状态
     */
    private function updateSourceHealthStatus(int $sourceId, array $healthResult): void
    {
        try {
            // 更新采集源表的健康状态字段
            Db::table('collect_sources')
                ->where('id', $sourceId)
                ->update([
                    'health_status' => $healthResult['status'],
                    'last_health_check' => $healthResult['check_time'],
                    'health_issues' => json_encode($healthResult['issues']),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                
        } catch (\Exception $e) {
            Log::error('更新健康状态失败', [
                'source_id' => $sourceId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取采集趋势数据（用于图表）
     */
    public function getCollectTrendsData(int $days = 7): array
    {
        try {
            $trends = [];
            
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                
                // 获取当天的采集统计
                $dayStats = Db::table('collect_tasks')
                    ->where('DATE(created_at)', $date)
                    ->field([
                        'COUNT(*) as total_tasks',
                        'COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_tasks',
                        'COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_tasks',
                        'SUM(success_count) as total_success',
                        'SUM(failed_count) as total_failed'
                    ])
                    ->find();
                
                $trends[] = [
                    'date' => $date,
                    'date_label' => date('m-d', strtotime($date)),
                    'total_tasks' => (int)($dayStats['total_tasks'] ?? 0),
                    'completed_tasks' => (int)($dayStats['completed_tasks'] ?? 0),
                    'failed_tasks' => (int)($dayStats['failed_tasks'] ?? 0),
                    'total_success' => (int)($dayStats['total_success'] ?? 0),
                    'total_failed' => (int)($dayStats['total_failed'] ?? 0),
                    'success_rate' => $dayStats['total_success'] > 0 ? 
                        round(($dayStats['total_success'] / ($dayStats['total_success'] + $dayStats['total_failed'])) * 100, 2) : 0
                ];
            }
            
            return $trends;
            
        } catch (\Exception $e) {
            Log::error('获取采集趋势数据失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 获取采集源性能分布数据
     */
    public function getSourcePerformanceData(): array
    {
        try {
            $sources = Db::table('collect_sources')
                ->where('status', 1)
                ->field(['id', 'name', 'health_status', 'total_collected', 'success_count'])
                ->select();
            
            $performance = [
                'healthy' => 0,
                'warning' => 0,
                'critical' => 0,
                'unknown' => 0
            ];
            
            $sourceDetails = [];
            
            foreach ($sources as $source) {
                $status = $source['health_status'] ?? 'unknown';
                $performance[$status]++;
                
                $successRate = $source['total_collected'] > 0 ? 
                    round(($source['success_count'] / $source['total_collected']) * 100, 2) : 0;
                
                $sourceDetails[] = [
                    'id' => $source['id'],
                    'name' => $source['name'],
                    'status' => $status,
                    'success_rate' => $successRate,
                    'total_collected' => (int)$source['total_collected']
                ];
            }
            
            return [
                'distribution' => $performance,
                'source_details' => $sourceDetails
            ];
            
        } catch (\Exception $e) {
            Log::error('获取采集源性能数据失败', ['error' => $e->getMessage()]);
            return [
                'distribution' => ['healthy' => 0, 'warning' => 0, 'critical' => 0, 'unknown' => 0],
                'source_details' => []
            ];
        }
    }
}
