<?php

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Db;
use think\facade\Log;
use app\service\VideoService;
use app\service\ResponseService;

/**
 * 用户控制器
 */
class User
{
    protected $videoService;
    protected $responseService;

    public function __construct()
    {
        $this->videoService = new VideoService();
        $this->responseService = new ResponseService();
    }

    /**
     * 获取用户个人信息
     */
    public function profile(Request $request): Response
    {
        try {
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('用户未登录', 401);
            }

            // 获取用户详细信息
            $user = Db::table('users')
                ->field('id, username, nickname, email, avatar, phone, gender, birthday, bio, created_at, last_login_time')
                ->where('id', $userInfo['id'])
                ->where('status', 'active')
                ->find();

            if (!$user) {
                return $this->responseService->error('用户不存在', 404);
            }

            return $this->responseService->success($user, '获取用户信息成功');

        } catch (\Exception $e) {
            Log::error('获取用户信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->responseService->handleException($e, '获取用户信息失败');
        }
    }

    /**
     * 获取用户的视频列表（个人中心使用）
     */
    public function videos(Request $request): Response
    {
        try {
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('用户未登录', 401);
            }

            $params = $request->get();
            $page = max(1, $params['page'] ?? 1);
            $limit = min($params['limit'] ?? 20, 50);
            $offset = ($page - 1) * $limit;

            // 构建查询条件
            $where = [];
            $where[] = ['v.user_id', '=', $userInfo['id']];

            // 状态筛选
            if (!empty($params['status'])) {
                $where[] = ['v.status', '=', $params['status']];
            }

            // 审核状态筛选
            if (!empty($params['audit_status'])) {
                $where[] = ['v.audit_status', '=', $params['audit_status']];
            }

            // 视频类型筛选
            if (!empty($params['video_type'])) {
                $where[] = ['v.video_type', '=', $params['video_type']];
            }

            // 获取视频列表
            $videos = Db::table('videos')
                ->alias('v')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field([
                    'v.id', 'v.title', 'v.description', 'v.cover_image', 'v.duration',
                    'v.view_count', 'v.like_count', 'v.comment_count', 'v.share_count',
                    'v.is_vip', 'v.status', 'v.audit_status', 'v.video_type',
                    'v.file_path', 'v.hls_url', 'v.created_at', 'v.updated_at',
                    'c.name as category_name'
                ])
                ->where($where)
                ->order('v.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 获取总数
            $total = Db::table('videos')->alias('v')->where($where)->count();

            // 获取各状态的统计数据
            $statusCounts = $this->getUserVideoStatusCounts($userInfo['id']);

            // 处理视频数据
            foreach ($videos as &$video) {
                // 检查缩略图文件是否存在，如果不存在则尝试重新生成
                if ($video['cover_image']) {
                    $thumbnailPath = root_path() . 'public' . $video['cover_image'];
                    if (!file_exists($thumbnailPath)) {
                        // 缩略图文件不存在，尝试重新生成
                        $this->regenerateThumbnail($video);
                    }
                }

                // 处理封面图片URL - 确保返回完整URL
                if ($video['cover_image']) {
                    if (!str_starts_with($video['cover_image'], 'http')) {
                        // 构建完整的URL
                        $video['cover_image'] = 'http://localhost:3000' . '/' . ltrim($video['cover_image'], '/');
                    }
                } else {
                    // 如果没有缩略图，尝试重新生成
                    $this->regenerateThumbnail($video);
                    // 重新生成后再次处理URL
                    if ($video['cover_image'] && !str_starts_with($video['cover_image'], 'http')) {
                        $video['cover_image'] = 'http://localhost:3000' . '/' . ltrim($video['cover_image'], '/');
                    }
                }

                // 处理播放URL
                if ($video['hls_url']) {
                    $video['play_url'] = $video['hls_url'];
                } elseif ($video['file_path']) {
                    $video['play_url'] = '/uploads/' . ltrim($video['file_path'], '/');
                } else {
                    $video['play_url'] = null;
                }

                // 格式化时间
                $video['created_at'] = date('Y-m-d H:i:s', strtotime($video['created_at']));
                $video['updated_at'] = date('Y-m-d H:i:s', strtotime($video['updated_at']));
            }

            return $this->responseService->success([
                'videos' => $videos,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'statusCounts' => $statusCounts
            ], '获取用户视频列表成功');

        } catch (\Exception $e) {
            Log::error('获取用户视频列表失败', [
                'user_id' => $userInfo['id'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->responseService->handleException($e, '获取用户视频列表失败');
        }
    }

    /**
     * 获取用户视频各状态的统计数据
     */
    private function getUserVideoStatusCounts(int $userId): array
    {
        $counts = [
            'all' => 0,
            'uploading' => 0,
            'processing' => 0,
            'pending' => 0,
            'approved' => 0,
            'rejected' => 0
        ];

        try {
            // 获取总数
            $counts['all'] = Db::table('videos')
                ->where('user_id', $userId)
                ->count();

            // 获取各审核状态的数量
            $auditCounts = Db::table('videos')
                ->field('audit_status, count(*) as count')
                ->where('user_id', $userId)
                ->group('audit_status')
                ->select()
                ->toArray();

            foreach ($auditCounts as $item) {
                if (isset($counts[$item['audit_status']])) {
                    $counts[$item['audit_status']] = $item['count'];
                }
            }

            // 获取处理中的视频数量（技术处理未完成）
            $processingCount = Db::table('videos')
                ->alias('v')
                ->leftJoin('video_processing_status vps', 'v.id = vps.video_id')
                ->where('v.user_id', $userId)
                ->where('v.status', 'published')
                ->where('vps.process_type', '<>', 'audit')
                ->where('vps.status', 'in', ['pending', 'processing'])
                ->count();

            $counts['processing'] = $processingCount;

        } catch (\Exception $e) {
            Log::error('获取用户视频状态统计失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }

        return $counts;
    }

    /**
     * 获取用户统计信息
     */
    public function stats(Request $request): Response
    {
        try {
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('用户未登录', 401);
            }

            $userId = $userInfo['id'];

            // 获取用户统计数据
            $stats = [
                'video_count' => 0,
                'total_views' => 0,
                'total_likes' => 0,
                'total_comments' => 0,
                'total_shares' => 0,
                'follower_count' => 0,
                'following_count' => 0
            ];

            // 视频统计
            $videoStats = Db::table('videos')
                ->field('count(*) as video_count, sum(view_count) as total_views, sum(like_count) as total_likes, sum(comment_count) as total_comments, sum(share_count) as total_shares')
                ->where('user_id', $userId)
                ->where('status', 'published')
                ->find();

            if ($videoStats) {
                $stats['video_count'] = $videoStats['video_count'] ?? 0;
                $stats['total_views'] = $videoStats['total_views'] ?? 0;
                $stats['total_likes'] = $videoStats['total_likes'] ?? 0;
                $stats['total_comments'] = $videoStats['total_comments'] ?? 0;
                $stats['total_shares'] = $videoStats['total_shares'] ?? 0;
            }

            return $this->responseService->success($stats, '获取用户统计信息成功');

        } catch (\Exception $e) {
            Log::error('获取用户统计信息失败', [
                'user_id' => $userInfo['id'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->responseService->handleException($e, '获取用户统计信息失败');
        }
    }

    /**
     * 重新生成视频缩略图
     */
    private function regenerateThumbnail(array &$video): void
    {
        try {
            // 只处理上传的视频
            if ($video['source_type'] !== 'upload' || empty($video['file_path'])) {
                return;
            }

            $videoFilePath = root_path() . 'public' . $video['file_path'];
            if (!file_exists($videoFilePath)) {
                Log::warning('视频文件不存在，无法生成缩略图', [
                    'video_id' => $video['id'],
                    'file_path' => $video['file_path']
                ]);
                return;
            }

            // 使用VideoProcessingService生成缩略图
            $videoProcessingService = new \app\service\VideoProcessingService();
            $processDir = root_path() . 'runtime' . DIRECTORY_SEPARATOR . 'video_processing' . DIRECTORY_SEPARATOR . 'temp_' . time();

            $thumbnailPath = $videoProcessingService->generateThumbnail(
                $videoFilePath,
                $processDir,
                (string)$video['id']
            );

            if ($thumbnailPath) {
                // 更新数据库记录
                Db::table('videos')
                    ->where('id', $video['id'])
                    ->update(['cover_image' => $thumbnailPath]);

                // 更新当前数组中的数据
                $video['cover_image'] = $thumbnailPath;

                Log::info('缩略图重新生成成功', [
                    'video_id' => $video['id'],
                    'thumbnail_path' => $thumbnailPath
                ]);
            }

        } catch (\Exception $e) {
            Log::error('重新生成缩略图失败', [
                'video_id' => $video['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }
}
