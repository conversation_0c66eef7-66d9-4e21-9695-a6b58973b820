import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import config from '@/config/env'
import { authService } from '@/utils/auth'

/**
 * 企业级HTTP请求配置 - [v20250719]
 *
 * 提供统一的HTTP请求封装，包含：
 * - 自动token管理和刷新
 * - 请求/响应拦截
 * - 错误处理和重试
 * - 性能监控
 * - 文件上传下载
 *
 * <AUTHOR> Video Platform Team
 * @version 2.0
 */

// ================================
// 配置常量
// ================================

const BASE_URL = config.apiBaseUrl
const TIMEOUT = config.apiTimeout
const MAX_RETRIES = config.apiRetryCount

// ================================
// 接口定义
// ================================

interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean
  skipErrorHandler?: boolean
  retries?: number
  retryDelay?: number
}

// 扩展Axios配置类型
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime: number
    }
    skipAuth?: boolean
    _retryCount?: number
    retries?: number
    retryDelay?: number
  }
}

// 扩展Window类型
declare global {
  interface Window {
    performanceMonitor?: {
      recordApiResponse: (url: string, responseTime: number, isError: boolean) => void
    }
  }
}

interface ApiError {
  code: number
  message: string
  details?: any
}

// ================================
// 创建axios实例
// ================================

const createAxiosInstance = (): AxiosInstance => {
  return axios.create({
    baseURL: BASE_URL,
    timeout: TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  })
}

// ================================
// HTTP请求类
// ================================

class HttpRequest {
  private instance: AxiosInstance

  constructor() {
    this.instance = createAxiosInstance()
    this.setupInterceptors()

    // 调试环境变量
    console.log('🔧 HttpRequest初始化:', {
      VITE_API_KEY: import.meta.env.VITE_API_KEY,
      VITE_API_KEY_USER: import.meta.env.VITE_API_KEY_USER,
      VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
      BASE_URL,
      TIMEOUT
    })
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const startTime = performance.now()
        config.metadata = { startTime }

        // 添加JWT认证
        if (!config.skipAuth) {
          // 使用JWT token认证
          const token = authService.getAccessToken()
          if (token) {
            config.headers['Authorization'] = `Bearer ${token}`
          }

          // 添加客户端标识（用于后端识别请求来源）
          const clientKey = import.meta.env.VITE_API_KEY_USER
          if (clientKey) {
            config.headers['X-Client-Key'] = clientKey
          }

          // 调试日志
          if (config.debug !== false) {
            console.log('🔑 API请求配置:', {
              url: config.url,
              method: config.method,
              hasToken: !!token,
              clientKey: clientKey ? clientKey.substring(0, 10) + '...' : 'null'
            })
          }
        }

        // 添加请求ID
        config.headers['X-Request-ID'] = this.generateRequestId()

        // 添加客户端信息
        config.headers['X-Client-Version'] = '2.0.0'
        config.headers['X-Client-Platform'] = this.getClientPlatform()

        // 添加时间戳防止缓存
        if (config.method === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now()
          }
        }

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        // 记录响应时间
        const endTime = performance.now()
        const startTime = response.config.metadata?.startTime || endTime
        const responseTime = endTime - startTime

        // 更新性能指标
        this.updatePerformanceMetrics(responseTime)

        // 集成性能监控
        if (typeof window !== 'undefined' && window.performanceMonitor) {
          const url = response.config.url || 'unknown'
          window.performanceMonitor.recordApiResponse(url, responseTime, false)
        }

        // 统一处理响应格式
        const { data } = response

        // 调试信息
        if (response.config.url?.includes('/api/categories') || response.config.url?.includes('/api/videos')) {
          console.log('🔍 API原始响应:', { url: response.config.url, data })
        }

        // 兼容两种API响应格式
        // 格式1: { success: true, data: {...} }
        // 格式2: { code: 200, message: "success", data: {...} }
        if (data.success !== false || data.code === 200) {
          // 统一返回格式为 { success: true, data: {...} }
          if (data.code === 200) {
            const result = {
              success: true,
              data: data.data,
              message: data.message,
              timestamp: data.timestamp
            }

            // 调试信息
            if (response.config.url?.includes('/api/categories') || response.config.url?.includes('/api/videos')) {
              console.log('🔄 响应格式转换:', result)
            }

            return result
          }
          return data
        }

        // 处理业务错误
        const errorMessage = data.message || data.msg || '请求失败'
        throw new Error(errorMessage)
      },
      async (error) => {
        const originalRequest = error.config

        // API密钥认证模式下，不需要token刷新机制
        // 如果收到401错误，直接返回错误，不尝试刷新token
        if (error.response?.status === 401) {
          console.error('API认证失败，请检查API密钥配置')
          return Promise.reject(error)
        }

        // 处理网络错误重试 - 暂时禁用重试以避免无限循环
        // if (this.shouldRetry(error) && !originalRequest._retryCount) {
        //   originalRequest._retryCount = 0
        // }

        // if (originalRequest._retryCount < (originalRequest.retries || MAX_RETRIES)) {
        //   originalRequest._retryCount++

        //   const delay = originalRequest.retryDelay || Math.pow(2, originalRequest._retryCount) * 1000
        //   await this.delay(delay)

        //   return this.instance(originalRequest)
        // }

        // 记录API错误到性能监控
        if (typeof window !== 'undefined' && window.performanceMonitor) {
          const url = originalRequest.url || 'unknown'
          const endTime = performance.now()
          const startTime = originalRequest.metadata?.startTime || endTime
          const responseTime = endTime - startTime
          window.performanceMonitor.recordApiResponse(url, responseTime, true)
        }

        return Promise.reject(this.normalizeError(error))
      }
    )
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    const response = await this.instance.get(url, config)
    return response as T // 响应拦截器已经处理了数据格式，不需要再取.data
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.instance.post(url, data, config)
    return response as T // 响应拦截器已经处理了数据格式，不需要再取.data
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.instance.put(url, data, config)
    return response as T // 响应拦截器已经处理了数据格式
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    const response = await this.instance.delete(url, config)
    return response as T // 响应拦截器已经处理了数据格式
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.instance.patch(url, data, config)
    return response as T // 响应拦截器已经处理了数据格式
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const uploadConfig: RequestConfig = {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      timeout: 300000, // 5分钟上传超时
    }

    const response = await this.instance.post(url, formData, uploadConfig)
    return response.data
  }

  /**
   * 刷新token
   */
  private async refreshToken(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await this.instance.post('/api/auth/refresh', {
        refresh_token: refreshToken
      }, { skipAuth: true } as RequestConfig)

      const { access_token, refresh_token: newRefreshToken } = response.data.data

      localStorage.setItem('token', access_token)
      localStorage.setItem('refresh_token', newRefreshToken)
    } catch (error) {
      localStorage.removeItem('token')
      localStorage.removeItem('refresh_token')
      throw error
    }
  }

  /**
   * 处理认证错误
   */
  private handleAuthError(): void {
    localStorage.removeItem('token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_info')

    // 重定向到登录页
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: AxiosError): boolean {
    // 网络错误
    if (!error.response) {
      return true
    }

    // 服务器错误（5xx）
    if (error.response.status >= 500) {
      return true
    }

    // 请求超时
    if (error.code === 'ECONNABORTED') {
      return true
    }

    return false
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 标准化错误
   */
  private normalizeError(error: AxiosError): ApiError {
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      return {
        code: status,
        message: (data as any)?.message || error.message,
        details: data
      }
    } else if (error.request) {
      // 网络错误
      return {
        code: 0,
        message: '网络连接失败，请检查网络设置',
        details: error
      }
    } else {
      // 其他错误
      return {
        code: -1,
        message: error.message || '未知错误',
        details: error
      }
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取客户端平台信息
   */
  private getClientPlatform(): string {
    const userAgent = navigator.userAgent

    if (/Android/i.test(userAgent)) {
      return 'android'
    } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
      return 'ios'
    } else if (/Windows/i.test(userAgent)) {
      return 'windows'
    } else if (/Mac/i.test(userAgent)) {
      return 'mac'
    } else if (/Linux/i.test(userAgent)) {
      return 'linux'
    } else {
      return 'unknown'
    }
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(responseTime: number): void {
    try {
      // 这里可以集成性能监控
      console.debug(`API响应时间: ${responseTime}ms`)
    } catch (error) {
      console.warn('Failed to update performance metrics:', error)
    }
  }
}

// ================================
// 导出请求实例
// ================================

export const request = new HttpRequest()

// 兼容旧版本API
export const api = {
  get: <T = any>(url: string, config?: RequestConfig) => request.get<T>(url, config),
  post: <T = any>(url: string, data?: any, config?: RequestConfig) => request.post<T>(url, data, config),
  put: <T = any>(url: string, data?: any, config?: RequestConfig) => request.put<T>(url, data, config),
  delete: <T = any>(url: string, config?: RequestConfig) => request.delete<T>(url, config),
  upload: <T = any>(url: string, file: File, config?: RequestConfig) => request.upload<T>(url, file, config),
}

export default request

// 导出类型
export type { RequestConfig, ApiError }
