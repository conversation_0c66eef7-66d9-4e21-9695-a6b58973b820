<!--
  高性能懒加载图片组件
  
  功能特性：
  - 智能懒加载
  - WebP格式支持
  - 渐进式加载
  - 错误处理
  - 占位符支持
  - 响应式图片
  
  <AUTHOR> Video Platform Team
  @version 2.0.0
-->

<template>
  <div 
    ref="containerRef"
    class="lazy-image-container"
    :class="{
      'lazy-image--loading': isLoading,
      'lazy-image--loaded': isLoaded,
      'lazy-image--error': hasError,
      'lazy-image--rounded': rounded,
      'lazy-image--circle': circle
    }"
    :style="containerStyle"
  >
    <!-- 占位符 -->
    <div 
      v-if="showPlaceholder" 
      class="lazy-image-placeholder"
      :style="placeholderStyle"
    >
      <slot name="placeholder">
        <div class="lazy-image-skeleton">
          <div class="lazy-image-skeleton-shimmer"></div>
        </div>
      </slot>
    </div>

    <!-- 主图片 -->
    <img
      v-show="isLoaded && !hasError"
      ref="imageRef"
      :src="optimizedSrc"
      :alt="alt"
      :loading="nativeLoading ? 'lazy' : 'eager'"
      class="lazy-image-main"
      @load="handleLoad"
      @error="handleError"
    />

    <!-- 错误状态 -->
    <div 
      v-if="hasError" 
      class="lazy-image-error"
    >
      <slot name="error">
        <div class="lazy-image-error-content">
          <svg class="lazy-image-error-icon" viewBox="0 0 24 24">
            <path d="M21,5V6.5L9.5,18A2.5,2.5 0 0,1 7,18A2.5,2.5 0 0,1 7,13A2.5,2.5 0 0,1 9.5,13A2.5,2.5 0 0,1 9.5,15.5L21,4M7.5,2A2.5,2.5 0 0,1 10,4.5A2.5,2.5 0 0,1 7.5,7A2.5,2.5 0 0,1 5,4.5A2.5,2.5 0 0,1 7.5,2Z"/>
          </svg>
          <span class="lazy-image-error-text">图片加载失败</span>
        </div>
      </slot>
    </div>

    <!-- 加载进度 -->
    <div 
      v-if="isLoading && showProgress" 
      class="lazy-image-progress"
    >
      <div class="lazy-image-progress-bar">
        <div 
          class="lazy-image-progress-fill"
          :style="{ width: `${loadProgress}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props定义
interface Props {
  src: string
  alt?: string
  width?: number | string
  height?: number | string
  placeholder?: string
  fallback?: string
  lazy?: boolean
  webp?: boolean
  quality?: number
  rounded?: boolean
  circle?: boolean
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none'
  showProgress?: boolean
  nativeLoading?: boolean
  threshold?: number
  rootMargin?: string
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  lazy: true,
  webp: true,
  quality: 80,
  rounded: false,
  circle: false,
  objectFit: 'cover',
  showProgress: false,
  nativeLoading: false,
  threshold: 0.1,
  rootMargin: '50px'
})

// Emits定义
const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
  intersect: [isIntersecting: boolean]
}>()

// 响应式状态
const containerRef = ref<HTMLElement>()
const imageRef = ref<HTMLImageElement>()
const isLoading = ref(false)
const isLoaded = ref(false)
const hasError = ref(false)
const isIntersecting = ref(false)
const loadProgress = ref(0)

// 计算属性
const showPlaceholder = computed(() => 
  !isLoaded.value && !hasError.value
)

const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  aspectRatio: props.width && props.height ? `${props.width} / ${props.height}` : undefined
}))

const placeholderStyle = computed(() => ({
  backgroundImage: props.placeholder ? `url(${props.placeholder})` : undefined
}))

// WebP支持检测
const supportsWebP = ref(false)

const checkWebPSupport = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image()
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2)
    }
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
  })
}

// 优化后的图片源
const optimizedSrc = computed(() => {
  if (!props.src) return ''
  
  let src = props.src
  
  // WebP格式转换
  if (props.webp && supportsWebP.value && !src.includes('.webp')) {
    // 如果支持WebP且原图不是WebP，尝试WebP版本
    const webpSrc = src.replace(/\.(jpg|jpeg|png)(\?.*)?$/i, '.webp$2')
    if (webpSrc !== src) {
      src = webpSrc
    }
  }
  
  // 添加质量参数（如果支持）
  if (props.quality !== 80 && !src.includes('quality=')) {
    const separator = src.includes('?') ? '&' : '?'
    src += `${separator}quality=${props.quality}`
  }
  
  return src
})

// 交叉观察器
let observer: IntersectionObserver | null = null

const createObserver = () => {
  if (!containerRef.value || !props.lazy) return
  
  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      isIntersecting.value = entry.isIntersecting
      emit('intersect', entry.isIntersecting)
      
      if (entry.isIntersecting && !isLoaded.value && !hasError.value) {
        loadImage()
      }
    },
    {
      threshold: props.threshold,
      rootMargin: props.rootMargin
    }
  )
  
  observer.observe(containerRef.value)
}

// 图片加载
const loadImage = async () => {
  if (isLoading.value || isLoaded.value) return
  
  isLoading.value = true
  loadProgress.value = 0
  
  try {
    // 创建新的图片对象进行预加载
    const img = new Image()
    
    // 模拟加载进度
    if (props.showProgress) {
      const progressInterval = setInterval(() => {
        if (loadProgress.value < 90) {
          loadProgress.value += Math.random() * 20
        }
      }, 100)
      
      img.onload = img.onerror = () => {
        clearInterval(progressInterval)
        loadProgress.value = 100
      }
    }
    
    img.onload = () => {
      nextTick(() => {
        if (imageRef.value) {
          imageRef.value.src = optimizedSrc.value
        }
      })
    }
    
    img.onerror = () => {
      // WebP失败时回退到原格式
      if (props.webp && optimizedSrc.value.includes('.webp')) {
        const fallbackSrc = props.src
        if (imageRef.value) {
          imageRef.value.src = fallbackSrc
        }
      } else {
        handleError(new Event('error'))
      }
    }
    
    img.src = optimizedSrc.value
    
  } catch (error) {
    handleError(new Event('error'))
  }
}

// 处理加载成功
const handleLoad = (event: Event) => {
  isLoading.value = false
  isLoaded.value = true
  hasError.value = false
  loadProgress.value = 100
  emit('load', event)
}

// 处理加载错误
const handleError = (event: Event) => {
  isLoading.value = false
  isLoaded.value = false
  hasError.value = true
  
  // 尝试fallback图片
  if (props.fallback && imageRef.value && imageRef.value.src !== props.fallback) {
    imageRef.value.src = props.fallback
    return
  }
  
  emit('error', event)
}

// 监听src变化
watch(() => props.src, () => {
  if (props.src) {
    isLoaded.value = false
    hasError.value = false
    
    if (!props.lazy || isIntersecting.value) {
      loadImage()
    }
  }
}, { immediate: true })

// 生命周期
onMounted(async () => {
  // 检测WebP支持
  supportsWebP.value = await checkWebPSupport()
  
  // 创建观察器
  if (props.lazy) {
    createObserver()
  } else {
    loadImage()
  }
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

.lazy-image--rounded {
  border-radius: 8px;
}

.lazy-image--circle {
  border-radius: 50%;
}

.lazy-image-main {
  width: 100%;
  height: 100%;
  object-fit: v-bind(objectFit);
  transition: opacity 0.3s ease;
}

.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.lazy-image-skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.lazy-image-skeleton-shimmer {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer-wave 2s infinite;
}

.lazy-image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  color: #999;
}

.lazy-image-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.lazy-image-error-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
}

.lazy-image-error-text {
  font-size: 12px;
  text-align: center;
}

.lazy-image-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.1);
}

.lazy-image-progress-bar {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.lazy-image-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes shimmer-wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .lazy-image-error-text {
    font-size: 10px;
  }
  
  .lazy-image-error-icon {
    width: 20px;
    height: 20px;
  }
}
</style>
