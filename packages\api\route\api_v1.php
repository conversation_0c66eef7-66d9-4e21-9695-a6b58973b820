<?php
// +----------------------------------------------------------------------
// | API v1 路由配置 - 保留所有现有功能
// +----------------------------------------------------------------------
// | 统一的API v1版本路由管理，保留原有所有功能
// +----------------------------------------------------------------------

use think\facade\Route;

// =====================================================
// API v1 路由组 - 统一版本管理
// =====================================================

Route::group('api/v1', function () {

    // =====================================================
    // 公开接口（无需认证）
    // =====================================================

    // 健康检查
    Route::get('health', 'Api/health');

    // 网络检测接口
    Route::any('ping', function() {
        return json(['code' => 200, 'message' => 'pong', 'timestamp' => time()]);
    });

    // =====================================================
    // 用户认证相关接口
    // =====================================================

    Route::group('auth', function () {
        Route::post('login', 'Auth/login');
        Route::post('register', 'Auth/register');
        Route::post('forgot-password', 'Auth/forgotPassword');
        Route::post('reset-password', 'Auth/resetPassword');
        Route::post('refresh', 'Auth/refresh');

        // 需要用户认证的接口
        Route::group('', function () {
            Route::post('logout', 'Auth/logout');
            Route::post('logout-all', 'Auth/logoutAll');
            Route::get('me', 'Auth/me');
            Route::get('profile', 'Auth/profile');
            Route::put('profile', 'Auth/updateProfile');
        })->middleware(\app\middleware\UserAuth::class);
    });

    // =====================================================
    // 用户个人中心接口（需要认证）
    // =====================================================

    Route::group('user', function () {
        Route::get('profile', 'User/profile');           // 获取个人信息
        Route::get('videos', 'User/videos');             // 获取我的视频
        Route::get('stats', 'User/stats');               // 获取个人统计
    })->middleware(\app\middleware\UserAuth::class);
    
    // =====================================================
    // 视频相关接口 - 保留所有现有功能
    // =====================================================

    // 视频处理状态接口（不需要认证，用于前端轮询）
    Route::get('videos/processing-status/batch', 'VideoProcessingController/getBatchStatus');
    Route::get('videos/:id/processing-status', 'VideoProcessingController/getStatus');

    // 视频观看记录（支持游客，不需要认证）
    Route::post('videos/view/:id', 'Video/recordView');

    // 短视频专用接口 - 放在videos组外面，避免路由冲突
    Route::get('videos/short-videos', function() {
        try {
            $categoryId = request()->get('category_id');

            $query = \think\facade\Db::table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->field([
                    'v.id', 'v.title', 'v.description', 'v.cover_image', 'v.file_path',
                    'v.duration', 'v.view_count', 'v.like_count', 'v.comment_count', 'v.share_count',
                    'v.video_type', 'v.created_at', 'v.category_id',
                    'u.id as user_id', 'u.username', 'u.nickname', 'u.avatar as user_avatar'
                ])
                ->where('v.status', 'published')
                ->where('v.audit_status', 'approved')
                ->where('v.video_type', 'short');

            // 如果指定了分类，添加分类过滤
            if ($categoryId) {
                $query->where('v.category_id', $categoryId);
            }

            $videos = $query->order('v.created_at desc')
                ->limit(20)
                ->select();

            // 为每个视频添加HLS播放地址
            $videosArray = $videos->toArray();
            foreach ($videosArray as &$video) {
                // 检查是否有HLS文件
                $hlsPath = "/hls/video_{$video['id']}/playlist.m3u8";
                $hlsFullPath = root_path() . "public" . $hlsPath;

                if (file_exists($hlsFullPath)) {
                    // 使用环境变量获取API基础URL
                    $apiBaseUrl = env('API_BASE_URL', 'http://localhost:3000');
                    $video['hls_url'] = $apiBaseUrl . $hlsPath;
                    $video['has_hls'] = true;
                } else {
                    $video['hls_url'] = null;
                    $video['has_hls'] = false;
                }
            }

            return json([
                'success' => true,
                'code' => 200,
                'message' => '获取短视频列表成功',
                'data' => $videosArray,
                'timestamp' => time()
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'code' => 500,
                'message' => '获取短视频列表失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time()
            ]);
        }
    });

    // 获取短视频分类（公开接口，无需认证）
    Route::get('videos/short-categories', function() {
        try {
            $categories = \think\facade\Db::table('video_categories')
                ->field(['id', 'name', 'description'])
                ->where('status', 1)
                ->where('video_type', 'short')
                ->order('sort_order asc, id asc')
                ->select();

            return json([
                'success' => true,
                'code' => 200,
                'message' => '获取分类列表成功',
                'data' => $categories->toArray(),
                'timestamp' => time()
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'code' => 500,
                'message' => '获取分类列表失败: ' . $e->getMessage(),
                'data' => [],
                'timestamp' => time()
            ]);
        }
    });

    Route::group('videos', function () {
        // 公开访问的视频接口
        Route::get('', 'Video/index');                    // 获取视频列表
        Route::get('recommend', 'Video/recommend');       // 推荐视频
        Route::get('search', 'Video/search');             // 搜索视频
        Route::get(':id', 'Video/read');                  // 获取视频详情

        // 需要用户认证的视频接口
        Route::group('', function () {
            Route::post('', 'Video/save');                // 上传视频
            Route::put(':id', 'Video/update');           // 更新视频
            Route::delete(':id', 'Video/delete');        // 删除视频
        })->middleware(\app\middleware\UserAuth::class);
    });

    // 视频播放相关路由（独立出来，支持特殊中间件）
    Route::group('video', function () {
        Route::get(':id/play', 'Video/play')->middleware(\app\middleware\VideoAuth::class);
        Route::get(':id/stream', 'Video/stream')->middleware(\app\middleware\VideoAuth::class);
        Route::get(':id/hls', 'Video/hls')->middleware(\app\middleware\VideoAuth::class);
        Route::get(':id/info', 'Video/info');
    });
    
    // =====================================================
    // 分类相关接口
    // =====================================================
    
    Route::group('categories', function () {
        Route::get('', 'Category/index');                 // 获取分类列表
        Route::get(':id', 'Category/read');               // 获取分类详情
        Route::get('tree', 'Category/tree');              // 获取分类树
    });
    
    // =====================================================
    // 用户相关接口 - 保留所有现有功能
    // =====================================================

    // 用户资源路由 (RESTful) - 保留原有的resource定义
    Route::resource('users', 'User');

    // 用户搜索接口
    Route::get('users/search', 'User/search');

    // 特殊用户接口（保留原有的复杂逻辑）
    Route::get('user/:id/info', function($id) {
        // 保留原有的用户信息获取逻辑
        try {
            $user = \think\facade\Db::table('users')
                ->alias('u')
                ->leftJoin('user_profiles p', 'u.id = p.user_id')
                ->field([
                    'u.id', 'u.username', 'u.email', 'u.status', 'u.created_at',
                    'p.nickname', 'p.avatar', 'p.bio', 'p.gender', 'p.birthday'
                ])
                ->where('u.id', $id)
                ->find();

            if (!$user) {
                return json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }

            // 获取用户统计
            $stats = [
                'upload_count' => \think\facade\Db::table('videos')->where('user_id', $id)->count(),
                'total_views' => \think\facade\Db::table('videos')->where('user_id', $id)->sum('view_count'),
                'total_likes' => \think\facade\Db::table('videos')->where('user_id', $id)->sum('like_count'),
                'favorite_count' => \think\facade\Db::table('video_collections')->where('user_id', $id)->count()
            ];

            $user['stats'] = $stats;

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $user
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取用户信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    });

    // 获取用户视频列表
    Route::get('user/:id/videos', function($id) {
        // 保留原有的用户视频列表逻辑
        try {
            $page = max(1, request()->get('page', 1));
            $limit = min(request()->get('limit', 20), 50);
            $offset = ($page - 1) * $limit;

            $total = \think\facade\Db::table('videos')
                ->where('user_id', $id)
                ->where('status', 'published')
                ->count();

            $videos = \think\facade\Db::table('videos')
                ->alias('v')
                ->leftJoin('categories c', 'v.category_id = c.id')
                ->field([
                    'v.id', 'v.title', 'v.description', 'v.cover_image', 'v.duration',
                    'v.view_count', 'v.like_count', 'v.is_vip', 'v.status', 'v.created_at',
                    'c.name as category_name'
                ])
                ->where('v.user_id', $id)
                ->where('v.status', 'published')
                ->order('v.created_at desc')
                ->limit($offset, $limit)
                ->select();

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'videos' => $videos->toArray(),
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取用户视频失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    // =====================================================
    // 视频评论相关接口 - 保留所有现有功能
    // =====================================================

    Route::group('video-comments', function () {
        Route::get('', 'VideoComment/index');             // 获取评论列表（公开）

        // 需要用户认证的评论接口（暂时移除认证，保持原有配置）
        Route::post('', 'VideoComment/save');             // 发表评论
        Route::delete(':id', 'VideoComment/delete');      // 删除评论
        Route::get('user-comments', 'VideoComment/getUserComments'); // 用户评论列表
    });

    // =====================================================
    // 视频收藏相关接口 - 保留所有现有功能
    // =====================================================

    Route::group('video-collections', function () {
        // 收藏接口（暂时移除认证，保持原有配置）
        Route::post('collect', 'VideoCollection/collect');           // 收藏视频
        Route::delete('uncollect', 'VideoCollection/uncollect');     // 取消收藏
        Route::get('user-collections', 'VideoCollection/getUserCollections');    // 用户收藏列表
        Route::get('video-collections/:videoId', 'VideoCollection/getVideoCollections'); // 视频收藏信息
    });
    
    // =====================================================
    // 视频交互相关接口（需要用户认证）
    // =====================================================

    Route::group('videos', function () {
        // 点赞相关接口
        Route::post(':id/like', 'VideoLike/like');
        Route::delete(':id/like', 'VideoLike/unlike');
        Route::get(':id/like/status', 'VideoLike/checkLike');
        Route::get(':id/likes', 'VideoLike/getLikeList');

        // 收藏相关接口
        Route::post(':id/favorite', 'VideoCollection/collect');
        Route::delete(':id/favorite', 'VideoCollection/uncollect');
        Route::get(':id/favorite/status', 'VideoCollection/checkCollection');
        Route::get(':id/collections', 'VideoCollection/getVideoCollections');

        // 评论相关接口
        Route::get(':id/comments', 'VideoComment/index');
        Route::post(':id/comments', 'VideoComment/save');
        Route::delete(':id/comments/:commentId', 'VideoComment/delete');

        // 分享相关接口
        Route::post(':id/share', function($id) {
            // 简单的分享统计
            try {
                \think\facade\Db::table('videos')
                    ->where('id', $id)
                    ->inc('share_count', 1)
                    ->update();

                return json([
                    'success' => true,
                    'message' => '分享成功',
                    'data' => null
                ]);
            } catch (\Exception $e) {
                return json([
                    'success' => false,
                    'message' => '分享失败: ' . $e->getMessage(),
                    'data' => null
                ]);
            }
        });

        // 批量操作接口
        Route::post('batch', 'VideoBatch/batchOperation');
    })->middleware(\app\middleware\UserAuth::class);

    // =====================================================
    // 用户个人中心相关接口（需要用户认证）
    // =====================================================

    Route::group('users/me', function () {
        // 个人资料
        Route::get('profile', 'UserProfile/getProfile');
        Route::put('profile', 'UserProfile/updateProfile');

        // 观看历史
        Route::get('watch-history', 'UserProfile/getWatchHistory');
        Route::delete('watch-history', 'UserProfile/clearWatchHistory');

        // 用户收藏列表
        Route::get('favorites', 'VideoCollection/getUserCollections');

        // 用户点赞列表
        Route::get('likes', 'VideoLike/getUserLikes');
    })->middleware(\app\middleware\UserAuth::class);

    // =====================================================
    // 视频加密相关接口（公开访问，供播放器使用）
    // =====================================================

    Route::group('video/encryption', function () {
        Route::get('key/:keyId', 'VideoEncryptionController/getKey');
        Route::get('status/:videoId', 'VideoEncryptionController/getEncryptionStatus');
    })->middleware(\app\middleware\Cors::class);

    // =====================================================
    // 播放器相关接口（公开访问）
    // =====================================================

    Route::group('player', function () {
        Route::get('play-url', 'PlayerController/getPlayUrl');
        Route::get('players', 'PlayerController/getPlayers');
        Route::get('stats', 'PlayerController/getPlayStats');
    });

    // =====================================================
    // 上传相关接口 - 保留所有现有功能
    // =====================================================

    Route::group('upload', function () {
        // 基础文件上传接口
        Route::post('video', 'Upload/video');
        Route::post('image', 'Upload/image');
        Route::post('avatar', 'Upload/avatar');
        Route::get('progress/:uploadId', 'Upload/getProgress');

        // 分片上传接口（兼容旧版本）
        Route::post('chunk', 'Upload/chunk');
        Route::post('merge', 'Upload/merge');

        // 新版分片上传接口
        Route::post('chunked/init', 'Upload/initChunkedUpload');
        Route::post('chunked/chunk', 'Upload/uploadChunk');
        Route::post('chunked/merge', 'Upload/mergeChunks');
        Route::post('chunked/complete', 'Upload/completeChunkedUpload');
        Route::get('chunked/progress', 'Upload/getUploadProgress');
    })->middleware([\app\middleware\UserAuth::class]);

    // =====================================================
    // 搜索相关接口
    // =====================================================

    Route::group('search', function () {
        Route::get('videos', 'Video/search');
        Route::get('users', 'User/search');
        Route::get('categories', 'Category/search');
    });

    // =====================================================
    // 监控相关接口
    // =====================================================

    Route::group('monitoring', function () {
        Route::get('health', 'Monitoring/health');
        Route::get('realtime', 'Monitoring/realtime');
        Route::get('metrics', 'Monitoring/metrics');
        Route::get('performance', 'Monitoring/performance');
        Route::get('errors', 'Monitoring/errors');
    });

    // =====================================================
    // Swagger文档接口
    // =====================================================

    Route::group('swagger', function () {
        Route::get('json', 'swagger/SwaggerController/json');
        Route::get('ui', 'swagger/SwaggerController/ui');
    });

})->middleware([\app\middleware\Cors::class, \app\middleware\ApiKeyAuth::class]);
