# 正式版视频平台管理后台 - 生产环境配置
# NODE_ENV 由 Vite 自动设置，不需要在 .env 文件中设置

# API配置 - 生产环境
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=15000
VITE_API_KEY_ADMIN="ShiPinAdmin2024ProductionKey32Bytes!@#$%^&*()_+"

# 应用基础配置
VITE_APP_TITLE=正式版视频平台管理后台
VITE_APP_DESCRIPTION=专业的视频分享平台管理系统
VITE_APP_VERSION=1.0.0

# 服务地址配置 - 生产环境
VITE_ADMIN_BASE_URL=http://localhost:3001
VITE_FRONTEND_BASE_URL=http://localhost:3002

# 生产环境特定配置
VITE_DEBUG=false
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_MOCK=false
VITE_LOG_LEVEL=warn
VITE_CONSOLE_LOG=false

# 路由配置
VITE_ROUTER_MODE=history
VITE_BASE_URL=/

# 上传配置
VITE_UPLOAD_URL=http://localhost:3000/api/upload
VITE_UPLOAD_MAX_SIZE=104857600
VITE_UPLOAD_ALLOWED_TYPES=mp4,avi,mov,wmv,flv,webm,mkv,jpg,jpeg,png,gif,webp

# 主题配置
VITE_THEME_COLOR=#409EFF
VITE_THEME_MODE=light

# 缓存配置
VITE_ENABLE_CACHE=true
VITE_CACHE_PREFIX=zhengshiban_admin_prod_

# 权限配置
VITE_ENABLE_PERMISSION=true
VITE_DEFAULT_ROLE=admin

# 国际化配置
VITE_DEFAULT_LOCALE=zh-CN
VITE_ENABLE_I18N=false

# 性能配置 - 生产环境优化
VITE_ENABLE_GZIP=true
VITE_ENABLE_PWA=false
VITE_CHUNK_SIZE_WARNING_LIMIT=1000

# 调试工具 - 生产环境禁用
VITE_ENABLE_VCONSOLE=false
VITE_ENABLE_ERUDA=false
VITE_ENABLE_ANALYZER=false

# 错误监控
VITE_ENABLE_ERROR_LOG=true
VITE_ENABLE_SENTRY=false

# 安全配置
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=false
