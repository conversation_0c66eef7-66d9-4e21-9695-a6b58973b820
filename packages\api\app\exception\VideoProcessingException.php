<?php

namespace app\exception;

use Exception;

/**
 * 视频处理异常类 - 统一错误处理
 */
class VideoProcessingException extends Exception
{
    /**
     * 处理步骤
     * @var string
     */
    protected $step;
    
    /**
     * 视频ID
     * @var int
     */
    protected $videoId;
    
    /**
     * 错误代码映射
     * @var array
     */
    protected static $errorCodes = [
        'file_not_found' => 1001,
        'invalid_format' => 1002,
        'file_too_large' => 1003,
        'ffmpeg_error' => 1004,
        'database_error' => 1005,
        'permission_error' => 1006,
        'timeout_error' => 1007,
        'encryption_error' => 1008,
        'upload_error' => 1009,
        'unknown_error' => 1999
    ];
    
    /**
     * 构造函数
     * 
     * @param string $message 错误消息
     * @param string $step 处理步骤 (upload, thumbnail, transcode, audit)
     * @param int $videoId 视频ID
     * @param string $errorType 错误类型
     * @param Exception|null $previous 上一个异常
     */
    public function __construct(
        string $message, 
        string $step = 'unknown', 
        int $videoId = 0, 
        string $errorType = 'unknown_error',
        Exception $previous = null
    ) {
        $code = self::$errorCodes[$errorType] ?? self::$errorCodes['unknown_error'];
        parent::__construct($message, $code, $previous);
        
        $this->step = $step;
        $this->videoId = $videoId;
    }
    
    /**
     * 获取处理步骤
     * 
     * @return string
     */
    public function getStep(): string
    {
        return $this->step;
    }
    
    /**
     * 获取视频ID
     * 
     * @return int
     */
    public function getVideoId(): int
    {
        return $this->videoId;
    }
    
    /**
     * 获取错误类型
     * 
     * @return string
     */
    public function getErrorType(): string
    {
        $code = $this->getCode();
        return array_search($code, self::$errorCodes) ?: 'unknown_error';
    }
    
    /**
     * 转换为数组格式
     * 
     * @return array
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'step' => $this->step,
            'video_id' => $this->videoId,
            'error_type' => $this->getErrorType(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString()
        ];
    }
    
    /**
     * 创建文件不存在异常
     * 
     * @param string $filePath 文件路径
     * @param string $step 处理步骤
     * @param int $videoId 视频ID
     * @return static
     */
    public static function fileNotFound(string $filePath, string $step = 'unknown', int $videoId = 0): self
    {
        return new static(
            "文件不存在: {$filePath}",
            $step,
            $videoId,
            'file_not_found'
        );
    }
    
    /**
     * 创建格式不支持异常
     * 
     * @param string $format 文件格式
     * @param string $step 处理步骤
     * @param int $videoId 视频ID
     * @return static
     */
    public static function invalidFormat(string $format, string $step = 'unknown', int $videoId = 0): self
    {
        return new static(
            "不支持的视频格式: {$format}",
            $step,
            $videoId,
            'invalid_format'
        );
    }
    
    /**
     * 创建文件过大异常
     * 
     * @param int $fileSize 文件大小
     * @param int $maxSize 最大允许大小
     * @param string $step 处理步骤
     * @param int $videoId 视频ID
     * @return static
     */
    public static function fileTooLarge(int $fileSize, int $maxSize, string $step = 'unknown', int $videoId = 0): self
    {
        $fileSizeMB = round($fileSize / 1024 / 1024, 2);
        $maxSizeMB = round($maxSize / 1024 / 1024, 2);
        
        return new static(
            "文件过大: {$fileSizeMB}MB，最大允许: {$maxSizeMB}MB",
            $step,
            $videoId,
            'file_too_large'
        );
    }
    
    /**
     * 创建FFmpeg错误异常
     * 
     * @param string $command FFmpeg命令
     * @param string $output 错误输出
     * @param string $step 处理步骤
     * @param int $videoId 视频ID
     * @return static
     */
    public static function ffmpegError(string $command, string $output, string $step = 'unknown', int $videoId = 0): self
    {
        return new static(
            "FFmpeg处理失败: {$output}",
            $step,
            $videoId,
            'ffmpeg_error'
        );
    }
    
    /**
     * 创建数据库错误异常
     * 
     * @param string $operation 数据库操作
     * @param string $error 错误信息
     * @param string $step 处理步骤
     * @param int $videoId 视频ID
     * @return static
     */
    public static function databaseError(string $operation, string $error, string $step = 'unknown', int $videoId = 0): self
    {
        return new static(
            "数据库操作失败 ({$operation}): {$error}",
            $step,
            $videoId,
            'database_error'
        );
    }
}
