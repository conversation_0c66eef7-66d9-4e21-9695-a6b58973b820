#!/usr/bin/env node

/**
 * 环境切换脚本
 * 用于在本地开发环境和远程服务器环境之间快速切换
 * 
 * 使用方法:
 * npm run env:local   - 切换到本地环境
 * npm run env:remote  - 切换到远程环境
 * npm run env:status  - 查看当前环境状态
 */

const fs = require('fs')
const path = require('path')

const ENV_LOCAL_FILE = path.join(__dirname, '../.env.local')
const ENV_BACKUP_FILE = path.join(__dirname, '../.env.local.backup')

// 环境配置模板
const LOCAL_CONFIG = `# 开发环境配置 - 本地
NODE_ENV=development

# 应用配置
VITE_APP_NAME=51吃瓜网 (本地开发版)
VITE_APP_TITLE=51吃瓜网
VITE_APP_ENV=development
VITE_APP_VERSION=2.0.0

# 服务地址配置 - 本地
VITE_API_BASE_URL=http://localhost:3000
VITE_FRONTEND_BASE_URL=http://localhost:3002
VITE_ADMIN_BASE_URL=http://localhost:3001

# WebSocket配置
VITE_WS_URL=ws://localhost:3000/ws

# API配置 - 使用API密钥认证
VITE_API_KEY=ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+
VITE_API_KEY_USER=ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3

# 调试配置 - 开发环境启用
VITE_APP_DEBUG=true
VITE_DEBUG_MODE=true
VITE_CONSOLE_LOG=true
VITE_PERFORMANCE_MONITOR=true
VITE_ENABLE_DEVTOOLS=true

# 开发工具
VITE_DEV_TOOLS=true
VITE_SOURCE_MAP=true

# 缓存配置 - 开发环境短缓存
VITE_CACHE_ENABLED=true
VITE_CACHE_EXPIRE_TIME=60000

# 上传配置
VITE_UPLOAD_MAX_SIZE=500
VITE_UPLOAD_CHUNK_SIZE=2
VITE_UPLOAD_CONCURRENT=3
VITE_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,mp4,avi,mov,wmv

# 视频配置
VITE_VIDEO_AUTO_PLAY=false
VITE_VIDEO_PRELOAD=metadata
VITE_VIDEO_QUALITY_AUTO=true

# 功能开关 - 开发环境
VITE_ENABLE_MOCK=false
VITE_FEATURE_LIVE=true
VITE_FEATURE_VIP=true
VITE_FEATURE_COMMENT=true
VITE_FEATURE_SHARE=true

# 环境标识
VITE_USE_REMOTE=false
`

const REMOTE_CONFIG = `# 开发环境配置 - 远程服务器
NODE_ENV=development

# 应用配置
VITE_APP_NAME=51吃瓜网 (远程开发版)
VITE_APP_TITLE=51吃瓜网
VITE_APP_ENV=development
VITE_APP_VERSION=2.0.0

# 服务地址配置 - 远程服务器
VITE_API_BASE_URL=http://*************:3000
VITE_FRONTEND_BASE_URL=http://*************:3002
VITE_ADMIN_BASE_URL=http://*************:3001

# WebSocket配置
VITE_WS_URL=ws://*************:3000/ws

# API配置 - 使用API密钥认证
VITE_API_KEY=ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+
VITE_API_KEY_USER=ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3

# 调试配置 - 开发环境启用
VITE_APP_DEBUG=true
VITE_DEBUG_MODE=true
VITE_CONSOLE_LOG=true
VITE_PERFORMANCE_MONITOR=true
VITE_ENABLE_DEVTOOLS=true

# 开发工具
VITE_DEV_TOOLS=true
VITE_SOURCE_MAP=true

# 缓存配置 - 开发环境短缓存
VITE_CACHE_ENABLED=true
VITE_CACHE_EXPIRE_TIME=60000

# 上传配置
VITE_UPLOAD_MAX_SIZE=500
VITE_UPLOAD_CHUNK_SIZE=2
VITE_UPLOAD_CONCURRENT=3
VITE_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,mp4,avi,mov,wmv

# 视频配置
VITE_VIDEO_AUTO_PLAY=false
VITE_VIDEO_PRELOAD=metadata
VITE_VIDEO_QUALITY_AUTO=true

# 功能开关 - 开发环境
VITE_ENABLE_MOCK=false
VITE_FEATURE_LIVE=true
VITE_FEATURE_VIP=true
VITE_FEATURE_COMMENT=true
VITE_FEATURE_SHARE=true

# 环境标识
VITE_USE_REMOTE=true
`

function getCurrentEnv() {
  if (!fs.existsSync(ENV_LOCAL_FILE)) {
    return 'unknown'
  }
  
  const content = fs.readFileSync(ENV_LOCAL_FILE, 'utf8')
  if (content.includes('VITE_USE_REMOTE=true')) {
    return 'remote'
  } else if (content.includes('VITE_USE_REMOTE=false')) {
    return 'local'
  } else if (content.includes('*************')) {
    return 'remote'
  } else if (content.includes('localhost')) {
    return 'local'
  }
  
  return 'unknown'
}

function switchToLocal() {
  console.log('🔄 切换到本地开发环境...')
  
  // 备份当前配置
  if (fs.existsSync(ENV_LOCAL_FILE)) {
    fs.copyFileSync(ENV_LOCAL_FILE, ENV_BACKUP_FILE)
    console.log('📦 已备份当前配置')
  }
  
  // 写入本地配置
  fs.writeFileSync(ENV_LOCAL_FILE, LOCAL_CONFIG)
  console.log('✅ 已切换到本地环境')
  console.log('🌐 API地址: http://localhost:3000')
  console.log('💡 请重新构建前端: npm run build')
}

function switchToRemote() {
  console.log('🔄 切换到远程服务器环境...')
  
  // 备份当前配置
  if (fs.existsSync(ENV_LOCAL_FILE)) {
    fs.copyFileSync(ENV_LOCAL_FILE, ENV_BACKUP_FILE)
    console.log('📦 已备份当前配置')
  }
  
  // 写入远程配置
  fs.writeFileSync(ENV_LOCAL_FILE, REMOTE_CONFIG)
  console.log('✅ 已切换到远程环境')
  console.log('🌐 API地址: http://*************:3000')
  console.log('💡 请重新构建前端: npm run build')
}

function showStatus() {
  const currentEnv = getCurrentEnv()
  console.log('📊 当前环境状态:')
  console.log(`🎯 环境: ${currentEnv}`)
  
  if (fs.existsSync(ENV_LOCAL_FILE)) {
    const content = fs.readFileSync(ENV_LOCAL_FILE, 'utf8')
    const apiUrl = content.match(/VITE_API_BASE_URL=(.+)/)?.[1] || '未知'
    console.log(`🌐 API地址: ${apiUrl}`)
  }
  
  if (fs.existsSync(ENV_BACKUP_FILE)) {
    console.log('📦 存在备份配置文件')
  }
}

// 主程序
const command = process.argv[2]

switch (command) {
  case 'local':
    switchToLocal()
    break
  case 'remote':
    switchToRemote()
    break
  case 'status':
    showStatus()
    break
  default:
    console.log('🔧 环境切换工具')
    console.log('')
    console.log('使用方法:')
    console.log('  node switch-env.js local   - 切换到本地环境')
    console.log('  node switch-env.js remote  - 切换到远程环境')
    console.log('  node switch-env.js status  - 查看当前环境状态')
    console.log('')
    console.log('或使用 npm 脚本:')
    console.log('  npm run env:local')
    console.log('  npm run env:remote')
    console.log('  npm run env:status')
    break
}
