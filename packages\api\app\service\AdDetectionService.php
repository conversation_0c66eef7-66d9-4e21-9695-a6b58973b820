<?php
declare(strict_types=1);

namespace app\service;

use app\model\Video;
use app\model\SystemConfig;
use think\facade\Log;

/**
 * 智能广告检测服务
 * 
 * 实现多种广告检测算法：
 * 1. 基于时长的检测（开头17秒广告）
 * 2. 基于关键词的内容检测
 * 3. 基于URL模式的检测
 * 4. 基于视频特征的智能分析
 */
class AdDetectionService
{
    // 广告检测方法常量
    const METHOD_DURATION = 'duration';
    const METHOD_KEYWORDS = 'keywords';
    const METHOD_URL_PATTERN = 'url_pattern';
    const METHOD_SMART_ANALYSIS = 'smart_analysis';
    const METHOD_MANUAL = 'manual';
    
    // 广告检测状态
    const STATUS_PENDING = 'pending';
    const STATUS_DETECTED = 'detected';
    const STATUS_MANUAL = 'manual';
    const STATUS_SKIPPED = 'skipped';
    
    // 默认广告关键词
    private array $defaultAdKeywords = [
        '澳门', '新葡京', '娱乐城', '赌场', '博彩', '彩票',
        '贷款', '投资', '理财', '股票', '期货', '外汇',
        '减肥', '丰胸', '壮阳', '性药', '成人', '色情',
        '游戏', '充值', '代练', '装备', '金币', '钻石'
    ];
    
    // 广告URL模式
    private array $adUrlPatterns = [
        'googleads.g.doubleclick.net',
        'googlesyndication.com',
        'tanx.com',
        'alimama.com',
        'union.baidu.com',
        '/ads/',
        '/ad/',
        'ad.mp4',
        'advertisement',
        'promo.mp4',
        'sponsor.mp4'
    ];

    /**
     * 检测视频是否包含广告
     * 
     * @param int $videoId 视频ID
     * @param bool $forceRedetect 是否强制重新检测
     * @return array 检测结果
     */
    public function detectVideoAds(int $videoId, bool $forceRedetect = false): array
    {
        try {
            $video = Video::find($videoId);
            if (!$video) {
                return $this->errorResult('视频不存在');
            }

            // 如果已经检测过且不强制重新检测，直接返回结果
            if (!$forceRedetect && $video->ad_detection_status !== self::STATUS_PENDING) {
                return $this->successResult([
                    'has_ads' => $video->has_ads,
                    'ad_segments' => $video->ad_segments ? json_decode($video->ad_segments, true) : [],
                    'detection_method' => $video->ad_detection_method,
                    'confidence_score' => $video->ad_confidence_score,
                    'keywords_matched' => $video->ad_keywords_matched,
                    'cached' => true
                ]);
            }

            Log::info("开始检测视频广告", ['video_id' => $videoId, 'title' => $video->title]);

            // 执行多种检测方法
            $detectionResults = [];
            
            // 1. URL模式检测
            $urlResult = $this->detectByUrlPattern($video);
            if ($urlResult['detected']) {
                $detectionResults[] = $urlResult;
            }
            
            // 2. 关键词检测
            $keywordResult = $this->detectByKeywords($video);
            if ($keywordResult['detected']) {
                $detectionResults[] = $keywordResult;
            }
            
            // 3. 时长特征检测
            $durationResult = $this->detectByDuration($video);
            if ($durationResult['detected']) {
                $detectionResults[] = $durationResult;
            }
            
            // 4. 智能分析检测
            $smartResult = $this->detectBySmartAnalysis($video);
            if ($smartResult['detected']) {
                $detectionResults[] = $smartResult;
            }

            // 综合分析结果
            $finalResult = $this->analyzeDetectionResults($detectionResults);
            
            // 更新数据库
            $this->updateVideoAdInfo($video, $finalResult);
            
            Log::info("视频广告检测完成", [
                'video_id' => $videoId,
                'has_ads' => $finalResult['has_ads'],
                'confidence' => $finalResult['confidence_score'],
                'methods' => $finalResult['detection_methods']
            ]);

            return $this->successResult($finalResult);
            
        } catch (\Exception $e) {
            Log::error("视频广告检测失败", [
                'video_id' => $videoId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->errorResult('广告检测失败：' . $e->getMessage());
        }
    }

    /**
     * 基于URL模式检测广告
     */
    private function detectByUrlPattern(Video $video): array
    {
        $result = [
            'detected' => false,
            'method' => self::METHOD_URL_PATTERN,
            'confidence' => 0.0,
            'segments' => [],
            'details' => []
        ];

        // 检查播放URL
        $urlsToCheck = [];
        if ($video->hls_url) {
            $urlsToCheck[] = $video->hls_url;
        }
        if ($video->vod_play_url) {
            $playUrls = json_decode($video->vod_play_url, true);
            if (is_array($playUrls)) {
                $urlsToCheck = array_merge($urlsToCheck, array_values($playUrls));
            }
        }

        foreach ($urlsToCheck as $url) {
            if (!$url) continue;
            
            $lowerUrl = strtolower($url);
            foreach ($this->adUrlPatterns as $pattern) {
                if (strpos($lowerUrl, strtolower($pattern)) !== false) {
                    $result['detected'] = true;
                    $result['confidence'] = 0.95; // URL模式匹配置信度很高
                    $result['details'][] = "URL包含广告模式: {$pattern}";
                    break 2;
                }
            }
        }

        return $result;
    }

    /**
     * 基于关键词检测广告
     */
    private function detectByKeywords(Video $video): array
    {
        $result = [
            'detected' => false,
            'method' => self::METHOD_KEYWORDS,
            'confidence' => 0.0,
            'segments' => [],
            'details' => [],
            'matched_keywords' => []
        ];

        // 获取配置的广告关键词
        $configKeywords = $this->getAdKeywords();
        $allKeywords = array_merge($this->defaultAdKeywords, $configKeywords);

        // 检查标题和描述
        $content = ($video->title ?? '') . ' ' . ($video->description ?? '');
        $content = strtolower($content);

        $matchedKeywords = [];
        foreach ($allKeywords as $keyword) {
            if (strpos($content, strtolower($keyword)) !== false) {
                $matchedKeywords[] = $keyword;
            }
        }

        if (!empty($matchedKeywords)) {
            $result['detected'] = true;
            $result['confidence'] = min(0.8, count($matchedKeywords) * 0.2); // 最高0.8
            $result['matched_keywords'] = $matchedKeywords;
            $result['details'][] = "匹配到广告关键词: " . implode(', ', $matchedKeywords);
        }

        return $result;
    }

    /**
     * 基于时长特征检测广告
     */
    private function detectByDuration(Video $video): array
    {
        $result = [
            'detected' => false,
            'method' => self::METHOD_DURATION,
            'confidence' => 0.0,
            'segments' => [],
            'details' => []
        ];

        $duration = $video->duration ?? 0;
        if ($duration <= 0) {
            return $result;
        }

        // 获取配置的广告时长
        $adDuration = $this->getAdDuration();
        
        // 检测逻辑：
        // 1. 如果是采集视频且时长在合理范围内，可能有开头广告
        // 2. 短视频（<5分钟）且开头可能有17秒左右广告
        // 3. 长视频（>10分钟）可能有中间插播广告
        
        if ($video->source_type === 'collect' || $video->collect_source_id) {
            // 采集视频更可能有广告
            if ($duration > 60 && $duration < 3600) { // 1分钟到1小时
                $result['detected'] = true;
                $result['confidence'] = 0.6;
                $result['segments'][] = [
                    'start' => 0,
                    'end' => $adDuration,
                    'type' => 'opening',
                    'confidence' => 0.6
                ];
                $result['details'][] = "采集视频可能包含开头广告 (0-{$adDuration}秒)";
            }
            
            // 长视频可能有中间广告
            if ($duration > 600) { // 10分钟以上
                $midPoint = intval($duration / 2);
                $result['segments'][] = [
                    'start' => $midPoint - 10,
                    'end' => $midPoint + 10,
                    'type' => 'middle',
                    'confidence' => 0.3
                ];
                $result['details'][] = "长视频可能包含中间广告";
            }
        }

        return $result;
    }

    /**
     * 智能分析检测
     */
    private function detectBySmartAnalysis(Video $video): array
    {
        $result = [
            'detected' => false,
            'method' => self::METHOD_SMART_ANALYSIS,
            'confidence' => 0.0,
            'segments' => [],
            'details' => []
        ];

        // 综合多个因素进行智能分析
        $suspiciousFactors = 0;
        $details = [];

        // 因素1：视频来源
        if ($video->source_type === 'collect' || $video->collect_source_id) {
            $suspiciousFactors += 2;
            $details[] = "采集视频更可能包含广告";
        }

        // 因素2：视频时长特征
        $duration = $video->duration ?? 0;
        if ($duration > 0) {
            // 如果视频很短但不是短视频类型，可能是广告
            if ($duration < 60 && $video->video_type !== 'short') {
                $suspiciousFactors += 3;
                $details[] = "视频时长异常短，可能是广告";
            }
            
            // 如果视频时长刚好是常见广告时长
            if (in_array($duration, [15, 17, 30, 60])) {
                $suspiciousFactors += 1;
                $details[] = "视频时长符合常见广告时长";
            }
        }

        // 因素3：标题特征
        $title = strtolower($video->title ?? '');
        $adTitlePatterns = ['广告', '推广', '赞助', '合作', 'ad', 'ads', 'promo'];
        foreach ($adTitlePatterns as $pattern) {
            if (strpos($title, $pattern) !== false) {
                $suspiciousFactors += 2;
                $details[] = "标题包含广告相关词汇";
                break;
            }
        }

        // 因素4：观看数据异常
        if ($video->view_count === 0 && $video->like_count === 0) {
            $suspiciousFactors += 1;
            $details[] = "缺乏用户互动数据";
        }

        // 综合评分
        if ($suspiciousFactors >= 3) {
            $result['detected'] = true;
            $result['confidence'] = min(0.7, $suspiciousFactors * 0.15);
            $result['details'] = $details;
            
            // 如果检测到可能有广告，添加预估的广告段
            if ($duration > 0) {
                $adDuration = min($this->getAdDuration(), $duration * 0.3);
                $result['segments'][] = [
                    'start' => 0,
                    'end' => $adDuration,
                    'type' => 'estimated',
                    'confidence' => $result['confidence']
                ];
            }
        }

        return $result;
    }

    /**
     * 分析检测结果并得出最终结论
     */
    private function analyzeDetectionResults(array $detectionResults): array
    {
        if (empty($detectionResults)) {
            return [
                'has_ads' => false,
                'confidence_score' => 0.0,
                'ad_segments' => [],
                'detection_methods' => [],
                'keywords_matched' => '',
                'details' => ['未检测到广告特征']
            ];
        }

        // 计算综合置信度
        $totalConfidence = 0;
        $maxConfidence = 0;
        $allSegments = [];
        $allMethods = [];
        $allKeywords = [];
        $allDetails = [];

        foreach ($detectionResults as $result) {
            $confidence = $result['confidence'] ?? 0;
            $totalConfidence += $confidence;
            $maxConfidence = max($maxConfidence, $confidence);
            
            if (!empty($result['segments'])) {
                $allSegments = array_merge($allSegments, $result['segments']);
            }
            
            $allMethods[] = $result['method'];
            
            if (!empty($result['matched_keywords'])) {
                $allKeywords = array_merge($allKeywords, $result['matched_keywords']);
            }
            
            if (!empty($result['details'])) {
                $allDetails = array_merge($allDetails, $result['details']);
            }
        }

        // 使用加权平均和最高置信度的组合
        $finalConfidence = ($totalConfidence / count($detectionResults) + $maxConfidence) / 2;
        
        // 去重并合并广告段
        $mergedSegments = $this->mergeAdSegments($allSegments);
        
        return [
            'has_ads' => $finalConfidence >= 0.3, // 置信度阈值
            'confidence_score' => round($finalConfidence, 2),
            'ad_segments' => $mergedSegments,
            'detection_methods' => array_unique($allMethods),
            'keywords_matched' => implode(',', array_unique($allKeywords)),
            'details' => $allDetails
        ];
    }

    /**
     * 合并重叠的广告段
     */
    private function mergeAdSegments(array $segments): array
    {
        if (empty($segments)) {
            return [];
        }

        // 按开始时间排序
        usort($segments, function($a, $b) {
            return $a['start'] <=> $b['start'];
        });

        $merged = [];
        $current = $segments[0];

        for ($i = 1; $i < count($segments); $i++) {
            $next = $segments[$i];
            
            // 如果重叠或相邻，合并
            if ($next['start'] <= $current['end'] + 5) {
                $current['end'] = max($current['end'], $next['end']);
                $current['confidence'] = max($current['confidence'], $next['confidence']);
            } else {
                $merged[] = $current;
                $current = $next;
            }
        }
        
        $merged[] = $current;
        return $merged;
    }

    /**
     * 更新视频广告信息到数据库
     */
    private function updateVideoAdInfo(Video $video, array $result): void
    {
        $video->has_ads = $result['has_ads'] ? 1 : 0;
        $video->ad_segments = !empty($result['ad_segments']) ? json_encode($result['ad_segments']) : null;
        $video->ad_detection_status = self::STATUS_DETECTED;
        $video->ad_detection_method = implode(',', $result['detection_methods']);
        $video->ad_keywords_matched = $result['keywords_matched'];
        $video->ad_confidence_score = $result['confidence_score'];
        $video->ad_last_detected_at = date('Y-m-d H:i:s');
        
        $video->save();
    }

    /**
     * 获取配置的广告关键词
     */
    private function getAdKeywords(): array
    {
        $config = SystemConfig::where('config_key', 'ad_keywords')->find();
        if ($config && $config->config_value) {
            return array_filter(array_map('trim', explode(',', $config->config_value)));
        }
        return [];
    }

    /**
     * 获取配置的广告时长
     */
    private function getAdDuration(): int
    {
        $config = SystemConfig::where('config_key', 'ad_duration')->find();
        return $config ? (int)$config->config_value : 17;
    }

    /**
     * 成功结果
     */
    private function successResult(array $data): array
    {
        return [
            'success' => true,
            'data' => $data
        ];
    }

    /**
     * 错误结果
     */
    private function errorResult(string $message): array
    {
        return [
            'success' => false,
            'message' => $message
        ];
    }

    /**
     * 批量检测视频广告
     * 
     * @param array $videoIds 视频ID数组
     * @param bool $forceRedetect 是否强制重新检测
     * @return array 批量检测结果
     */
    public function batchDetectAds(array $videoIds, bool $forceRedetect = false): array
    {
        $results = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($videoIds as $videoId) {
            $result = $this->detectVideoAds($videoId, $forceRedetect);
            $results[$videoId] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }

        return [
            'success' => true,
            'data' => [
                'results' => $results,
                'summary' => [
                    'total' => count($videoIds),
                    'success' => $successCount,
                    'error' => $errorCount
                ]
            ]
        ];
    }

    /**
     * 手动标记视频广告状态
     * 
     * @param int $videoId 视频ID
     * @param bool $hasAds 是否有广告
     * @param array $adSegments 广告段信息
     * @return array 操作结果
     */
    public function manualMarkAds(int $videoId, bool $hasAds, array $adSegments = []): array
    {
        try {
            $video = Video::find($videoId);
            if (!$video) {
                return $this->errorResult('视频不存在');
            }

            $video->has_ads = $hasAds ? 1 : 0;
            $video->ad_segments = !empty($adSegments) ? json_encode($adSegments) : null;
            $video->ad_detection_status = self::STATUS_MANUAL;
            $video->ad_detection_method = self::METHOD_MANUAL;
            $video->ad_confidence_score = 1.0; // 手动标记置信度最高
            $video->ad_last_detected_at = date('Y-m-d H:i:s');
            
            $video->save();

            Log::info("手动标记视频广告状态", [
                'video_id' => $videoId,
                'has_ads' => $hasAds,
                'segments_count' => count($adSegments)
            ]);

            return $this->successResult([
                'video_id' => $videoId,
                'has_ads' => $hasAds,
                'ad_segments' => $adSegments
            ]);
            
        } catch (\Exception $e) {
            Log::error("手动标记视频广告状态失败", [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            
            return $this->errorResult('标记失败：' . $e->getMessage());
        }
    }
}
