/**
 * API配置 - 统一的环境配置管理
 */

// 环境检测
const isDevelopment = import.meta.env.NODE_ENV === 'development'
const isProduction = import.meta.env.NODE_ENV === 'production'

// 环境配置
const ENV_CONFIG = {
  development: {
    apiBaseURL: 'http://localhost:3000',
    timeout: 30000,
    debug: true,
    logLevel: 'debug'
  },
  production: {
    apiBaseURL: 'http://localhost:3000',
    timeout: 15000,
    debug: false,
    logLevel: 'warn'
  }
}

// 获取当前环境配置
const getCurrentEnvConfig = () => {
  return isDevelopment ? ENV_CONFIG.development : ENV_CONFIG.production
}

// API基础配置
export const API_CONFIG = {
  // API基础URL - 优先使用环境变量，其次使用环境配置
  baseURL: import.meta.env.VITE_API_BASE_URL || getCurrentEnvConfig().apiBaseURL,

  // API密钥 - 管理后台专用
  apiKey: import.meta.env.VITE_API_KEY_ADMIN || 'ShiPinAdmin2024ProductionKey32Bytes!@#$%^&*()_+',

  // 请求超时时间
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || getCurrentEnvConfig().timeout,

  // 环境信息
  environment: import.meta.env.NODE_ENV || 'development',
  isDevelopment,
  isProduction,

  // 调试配置
  debug: import.meta.env.VITE_DEBUG === 'true' || getCurrentEnvConfig().debug,

  // 重试配置
  retry: {
    times: 3,
    delay: 1000
  }
}

// 请求头配置
export const getApiHeaders = () => ({
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-API-Key': API_CONFIG.apiKey,
  'X-Client-Version': '1.0.0',
  'X-Client-Platform': 'admin-web'
})

// Axios配置
export const axiosConfig = {
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: getApiHeaders(),
  
  // 不再需要withCredentials，因为使用API密钥认证
  withCredentials: false
}

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  auth: {
    login: '/api/admin/login',
    logout: '/api/admin/logout',
    profile: '/api/admin/profile',
    refresh: '/api/admin/refresh'
  },
  
  // 用户管理
  users: {
    list: '/api/admin/users',
    detail: '/api/admin/users/:id',
    create: '/api/admin/users',
    update: '/api/admin/users/:id',
    delete: '/api/admin/users/:id'
  },
  
  // 视频管理
  videos: {
    list: '/api/admin/videos',
    detail: '/api/admin/videos/:id',
    create: '/api/admin/videos',
    update: '/api/admin/videos/:id',
    delete: '/api/admin/videos/:id',
    upload: '/api/admin/videos/upload'
  },
  
  // 分类管理
  categories: {
    list: '/api/admin/categories',
    detail: '/api/admin/categories/:id',
    create: '/api/admin/categories',
    update: '/api/admin/categories/:id',
    delete: '/api/admin/categories/:id'
  },
  
  // 采集管理
  collect: {
    sources: '/api/admin/collect/sources',
    tasks: '/api/admin/collect/tasks',
    logs: '/api/admin/collect/logs',
    start: '/api/admin/collect/start',
    stop: '/api/admin/collect/stop'
  },
  
  // 系统管理
  system: {
    config: '/api/admin/system/config',
    logs: '/api/admin/system/logs',
    metrics: '/api/admin/system/metrics'
  }
}

// 错误处理配置
export const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  RATE_LIMITED: 429,
  SERVER_ERROR: 500
}

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.UNAUTHORIZED]: 'API密钥无效或已过期',
  [ERROR_CODES.FORBIDDEN]: '权限不足',
  [ERROR_CODES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.RATE_LIMITED]: '请求频率超限，请稍后再试',
  [ERROR_CODES.SERVER_ERROR]: '服务器内部错误'
}
