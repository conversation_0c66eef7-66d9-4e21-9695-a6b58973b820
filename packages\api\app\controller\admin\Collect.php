<?php
declare(strict_types=1);

namespace app\controller\admin;

use app\BaseController;
use app\service\CollectService;
use app\service\CollectMonitorService;
use app\service\ResponseService;
use think\Request;
use think\Response;
use think\facade\Log;

/**
 * 视频采集控制器
 *
 * 提供视频采集源管理、分类映射、采集任务等功能
 */
class Collect extends BaseController
{
    private CollectService $collectService;
    private CollectMonitorService $monitorService;
    private ResponseService $responseService;

    protected function initialize()
    {
        parent::initialize();
        $this->collectService = new CollectService();
        $this->monitorService = new CollectMonitorService();
        $this->responseService = new ResponseService();
    }

    /**
     * 成功响应
     */
    protected function success($data = [], string $message = '操作成功'): Response
    {
        return $this->responseService->success($data, $message);
    }

    /**
     * 错误响应
     */
    protected function error(string $message = '操作失败', int $code = 400): Response
    {
        return $this->responseService->error($message, $code);
    }
    
    /**
     * 获取采集源列表
     */
    public function getSources(): Response
    {
        try {
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 20);
            $status = $this->request->param('status');
            
            $where = [];
            if ($status !== null) {
                $where['status'] = $status;
            }
            
            $result = $this->collectService->getSources($where, $page, $limit);
            
            return $this->success($result['data'], $result['msg']);
        } catch (\Exception $e) {
            Log::error('获取采集源列表失败', ['error' => $e->getMessage()]);
            return $this->error('获取失败');
        }
    }
    
    /**
     * 添加采集源
     */
    public function addSource(): Response
    {
        try {
            // 使用新的数据获取方法
            $data = $this->getRequestData([
                'name', 'api_url', 'api_type', 'auth_key', 'description', 'status'
            ]);

            // 调试日志（仅在开发环境启用）
            if (env('APP_DEBUG', false)) {
                Log::info('添加采集源请求数据', [
                    'data' => $data,
                    'all_post' => $this->request->post(),
                    'all_param' => $this->request->param()
                ]);
            }

            // 验证必填字段
            if (empty($data['name']) || empty($data['api_url'])) {
                return $this->error('名称和API地址不能为空');
            }
            
            // 设置默认值
            $data['api_type'] = $data['api_type'] ?? 'json';
            $data['status'] = $data['status'] ?? 1;
            
            $result = $this->collectService->addSource($data);
            
            if ($result['code'] === 1) {
                return $this->success($result['data'], $result['msg']);
            } else {
                return $this->error($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('添加采集源失败: ' . $e->getMessage());
            return $this->error('添加失败：' . $e->getMessage());
        }
    }
    
    /**
     * 测试采集源连通性
     */
    public function testSource(): Response
    {
        try {
            // 使用新的数据获取方法
            $data = $this->getRequestData();

            // 调试日志（仅在开发环境启用）
            if (env('APP_DEBUG', false)) {
                Log::info('测试采集源请求数据', [
                    'data' => $data,
                    'all_post' => $this->request->post(),
                    'all_param' => $this->request->param()
                ]);
            }

            $apiUrl = $data['api_url'] ?? '';
            $apiType = $data['api_type'] ?? 'json';

            if (empty($apiUrl)) {
                Log::error('测试采集源验证失败', ['api_url' => $apiUrl]);
                return $this->error('API地址不能为空');
            }

            Log::info('测试采集源连接', ['api_url' => $apiUrl, 'api_type' => $apiType]);

            $result = $this->collectService->testApiConnection($apiUrl, $apiType);

            if ($result['code'] === 1) {
                return $this->success($result['data'] ?? [], $result['msg']);
            } else {
                return $this->error($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('测试采集源失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return $this->error('测试失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取远程分类列表
     */
    public function getRemoteCategories(): Response
    {
        try {
            // 从URL路径参数中获取source_id
            $sourceId = (int)$this->request->param('source_id');

            // 记录调试信息
            Log::info('获取远程分类请求', [
                'source_id' => $sourceId,
                'url' => $this->request->url(),
                'params' => $this->request->param()
            ]);

            if ($sourceId <= 0) {
                Log::error('采集源ID无效', ['source_id' => $sourceId]);
                return $this->error('采集源ID无效');
            }

            $result = $this->collectService->getRemoteCategories($sourceId);

            if ($result['code'] === 1) {
                return $this->success($result['data'], $result['msg']);
            } else {
                Log::error('获取远程分类失败', ['result' => $result]);
                return $this->error($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('获取远程分类异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 保存分类映射
     */
    public function saveCategoryMapping(): Response
    {
        try {
            // 对于POST请求，尝试从请求体中获取JSON数据
            $rawInput = file_get_contents('php://input');
            $jsonData = json_decode($rawInput, true);

            if ($jsonData) {
                $sourceId = (int)($jsonData['source_id'] ?? 0);
                $mappings = $jsonData['mappings'] ?? [];
            } else {
                $sourceId = (int)$this->request->param('source_id');
                $mappings = $this->request->param('mappings', []);
            }

            if ($sourceId <= 0) {
                return $this->error('采集源ID无效');
            }

            if (!is_array($mappings)) {
                return $this->error('映射数据格式错误');
            }
            
            $result = $this->collectService->saveCategoryMapping($sourceId, $mappings);
            
            if ($result['code'] === 1) {
                return $this->success([], $result['msg']);
            } else {
                return $this->error($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('保存分类映射失败', ['error' => $e->getMessage()]);
            return $this->error('保存失败');
        }
    }
    
    /**
     * 更新采集源
     */
    public function updateSource(): Response
    {
        try {
            $id = (int)$this->request->param('id');

            // 使用新的数据获取方法
            $data = $this->getRequestData([
                'name', 'api_url', 'api_type', 'auth_key', 'description', 'status'
            ]);

            // 调试日志
            Log::info('更新采集源请求数据', [
                'id' => $id,
                'data' => $data,
                'all_post' => $this->request->post(),
                'all_param' => $this->request->param()
            ]);

            if ($id <= 0) {
                return $this->error('ID无效');
            }

            if (empty($data['name']) || empty($data['api_url'])) {
                Log::error('更新采集源验证失败', ['name' => $data['name'] ?? 'null', 'api_url' => $data['api_url'] ?? 'null']);
                return $this->error('名称和API地址不能为空');
            }
            
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $result = \think\facade\Db::table('collect_sources')
                ->where('id', $id)
                ->update($data);
                
            if ($result) {
                return $this->success([], '更新成功');
            } else {
                return $this->error('更新失败');
            }
        } catch (\Exception $e) {
            Log::error('更新采集源失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return $this->error('更新失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除采集源
     */
    public function deleteSource(): Response
    {
        try {
            $id = (int)$this->request->param('id');
            
            if ($id <= 0) {
                return $this->error('ID无效');
            }
            
            // 检查是否有关联的任务
            $taskCount = \think\facade\Db::table('collect_tasks')
                ->where('source_id', $id)
                ->where('status', 'in', ['pending', 'running'])
                ->count();
                
            if ($taskCount > 0) {
                return $this->error('该采集源有正在进行的任务，无法删除');
            }
            
            $result = \think\facade\Db::table('collect_sources')
                ->where('id', $id)
                ->delete();
                
            if ($result) {
                return $this->success([], '删除成功');
            } else {
                return $this->error('删除失败');
            }
        } catch (\Exception $e) {
            Log::error('删除采集源失败', ['error' => $e->getMessage()]);
            return $this->error('删除失败');
        }
    }
    
    /**
     * 获取采集配置
     */
    public function getConfig(): Response
    {
        try {
            $configs = \think\facade\Db::table('collect_config')
                ->order('id asc')
                ->select();

            $configData = [];
            foreach ($configs as $config) {
                // 根据配置类型转换值
                $value = $config['config_value'];

                // 数字类型配置
                if (in_array($config['config_key'], [
                    'collect_interval', 'max_concurrent_tasks', 'collect_timeout',
                    'collect_retry', 'collect_sleep'
                ])) {
                    $value = (int)$value;
                }
                // 布尔类型配置
                elseif (in_array($config['config_key'], [
                    'collect_pic', 'collect_content', 'collect_actor', 'collect_director',
                    'collect_tag', 'collect_area', 'collect_lang', 'collect_year',
                    'content_filter_enabled', 'duplicate_check_enabled', 'auto_publish'
                ])) {
                    $value = (bool)((int)$value);
                }
                // 字符串类型保持原样

                $configData[$config['config_key']] = $value;
            }

            return $this->success($configData);
        } catch (\Exception $e) {
            Log::error('获取采集配置失败', ['error' => $e->getMessage()]);
            return $this->error('获取失败');
        }
    }
    
    /**
     * 更新采集配置
     */
    public function updateConfig(): Response
    {
        try {
            // 使用统一的数据获取方法
            $data = $this->getRequestData();
            $configs = $data['configs'] ?? $data;

            if (!is_array($configs)) {
                return $this->error('配置数据格式错误');
            }

            foreach ($configs as $key => $value) {
                // 转换值为字符串存储（数据库中统一存储为字符串）
                if (is_bool($value)) {
                    $value = $value ? '1' : '0';
                } elseif (is_numeric($value)) {
                    $value = (string)$value;
                } else {
                    $value = (string)$value;
                }

                \think\facade\Db::table('collect_config')
                    ->where('config_key', $key)
                    ->update([
                        'config_value' => $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }

            return $this->success([], '配置更新成功');
        } catch (\Exception $e) {
            Log::error('更新采集配置失败: ' . $e->getMessage());
            return $this->error('更新失败');
        }
    }
    
    /**
     * 创建采集任务
     */
    public function createTask(): Response
    {
        try {
            // 对于POST请求，尝试从请求体中获取JSON数据
            $rawInput = file_get_contents('php://input');
            $jsonData = json_decode($rawInput, true);

            if ($jsonData) {
                $sourceId = (int)($jsonData['source_id'] ?? 0);
                $config = $jsonData['config'] ?? [];
            } else {
                $sourceId = (int)$this->request->param('source_id');
                $config = $this->request->param('config', []);
            }

            if ($sourceId <= 0) {
                return $this->error('采集源ID无效');
            }
            
            $result = $this->collectService->createCollectTask($sourceId, $config);
            
            if ($result['code'] === 1) {
                return $this->success($result['data'], $result['msg']);
            } else {
                return $this->error($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('创建采集任务失败', ['error' => $e->getMessage()]);
            return $this->error('创建失败');
        }
    }
    
    /**
     * 启动采集任务
     */
    public function startTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('task_id');

            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }
            
            // 启动后台采集进程
            $command = "php " . app()->getRootPath() . "packages/api/execute_collect_task.php {$taskId}";
            
            if (PHP_OS_FAMILY === 'Windows') {
                pclose(popen("start /B {$command}", "r"));
            } else {
                exec("{$command} > /dev/null 2>&1 &");
            }
            
            return $this->success([], '采集任务已启动');
        } catch (\Exception $e) {
            Log::error('启动采集任务失败', ['error' => $e->getMessage()]);
            return $this->error('启动失败');
        }
    }

    /**
     * 启动指定采集任务
     */
    public function startTaskById(): Response
    {
        try {
            $taskId = (int)$this->request->param('id');

            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            // 检查任务是否存在
            $task = \think\facade\Db::table('collect_tasks')->where('id', $taskId)->find();

            if (!$task) {
                return $this->error('任务不存在');
            }

            // 检查采集源是否存在
            $source = \think\facade\Db::table('collect_sources')->where('id', $task['source_id'])->find();

            if (!$source) {
                return $this->error('采集源ID无效');
            }

            // 检查任务状态
            if ($task['status'] === 'running') {
                return $this->error('任务已在运行中');
            }

            // 更新任务状态为运行中
            \think\facade\Db::table('collect_tasks')
                ->where('id', $taskId)
                ->update([
                    'status' => 'running',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 启动后台采集进程 - 修复脚本路径
            $scriptPath = "/var/www/html/execute_collect_task.php";
            $command = "php \"{$scriptPath}\" {$taskId}";
            $logFile = app()->getRuntimePath() . "collect_task_{$taskId}.log";

            if (PHP_OS_FAMILY === 'Windows') {
                // Windows环境下使用更可靠的后台执行方式
                $fullCommand = "{$command} > \"{$logFile}\" 2>&1";
                $proc = proc_open("start /B {$fullCommand}", [], $pipes);
                if (is_resource($proc)) {
                    proc_close($proc);
                }
            } else {
                // Linux/Docker环境下使用nohup确保后台执行
                $fullCommand = "nohup {$command} > \"{$logFile}\" 2>&1 &";
                exec($fullCommand);
            }

            // 记录启动命令到日志
            Log::info('采集任务启动命令', [
                'task_id' => $taskId,
                'command' => $fullCommand,
                'log_file' => $logFile
            ]);

            Log::info('采集任务已启动', ['task_id' => $taskId, 'task_name' => $task['task_name']]);

            return $this->success(['task_id' => $taskId], '采集任务已启动');
        } catch (\Exception $e) {
            Log::error('启动采集任务失败', ['error' => $e->getMessage()]);
            return $this->error('启动失败：' . $e->getMessage());
        }
    }

    /**
     * 同步执行采集任务（用于测试）
     */
    public function executeTaskSync(): Response
    {
        try {
            $taskId = (int)$this->request->param('id');

            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            // 检查任务是否存在
            $task = \think\facade\Db::table('collect_tasks')->where('id', $taskId)->find();

            if (!$task) {
                return $this->error('任务不存在');
            }

            // 直接执行采集任务
            $collectService = new \app\service\CollectService();
            $result = $collectService->executeCollectTask($taskId);

            if ($result['code'] === 1) {
                return $this->success($result['data'], '采集任务执行完成');
            } else {
                return $this->error($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('同步执行采集任务失败', ['error' => $e->getMessage()]);
            return $this->error('执行失败：' . $e->getMessage());
        }
    }

    /**
     * 停止指定采集任务
     */
    public function stopTaskById(): Response
    {
        try {
            $taskId = (int)$this->request->param('id');

            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            // 检查任务是否存在
            $task = \think\facade\Db::table('collect_tasks')->where('id', $taskId)->find();
            if (!$task) {
                return $this->error('任务不存在');
            }

            // 更新任务状态为已停止
            \think\facade\Db::table('collect_tasks')
                ->where('id', $taskId)
                ->update([
                    'status' => 'cancelled',  // 设置为cancelled状态
                    'end_time' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            Log::info('采集任务已停止', ['task_id' => $taskId, 'task_name' => $task['task_name']]);

            return $this->success(['task_id' => $taskId], '采集任务已停止');
        } catch (\Exception $e) {
            Log::error('停止采集任务失败', ['error' => $e->getMessage()]);
            return $this->error('停止失败：' . $e->getMessage());
        }
    }

    /**
     * 删除采集任务
     */
    public function deleteTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id');

            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            // 检查任务是否存在
            $task = \think\facade\Db::table('collect_tasks')->where('id', $taskId)->find();
            if (!$task) {
                return $this->error('任务不存在');
            }

            // 检查任务状态，运行中的任务不能删除
            if ($task['status'] === 'running') {
                return $this->error('运行中的任务不能删除，请先停止任务');
            }

            // 删除任务
            $result = \think\facade\Db::table('collect_tasks')->where('id', $taskId)->delete();

            if ($result) {
                Log::info('采集任务已删除', ['task_id' => $taskId, 'task_name' => $task['task_name']]);
                return $this->success(['task_id' => $taskId], '任务删除成功');
            } else {
                return $this->error('删除失败');
            }
        } catch (\Exception $e) {
            Log::error('删除采集任务失败', ['error' => $e->getMessage()]);
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取采集任务列表
     */
    public function getTasks(): Response
    {
        try {
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 20);
            $sourceId = $this->request->param('source_id');
            $status = $this->request->param('status');
            
            $where = [];
            if ($sourceId) {
                $where['source_id'] = $sourceId;
            }
            if ($status) {
                $where['status'] = $status;
            }
            
            $total = \think\facade\Db::table('collect_tasks')->where($where)->count();
            $list = \think\facade\Db::table('collect_tasks')
                ->alias('t')
                ->leftJoin('collect_sources s', 't.source_id = s.id')
                ->where($where)
                ->field('t.*, s.name as source_name')
                ->order('t.id desc')
                ->page($page)
                ->limit($limit)
                ->select();
                
            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]);
        } catch (\Exception $e) {
            Log::error('获取采集任务列表失败', ['error' => $e->getMessage()]);
            return $this->error('获取失败');
        }
    }
    
    /**
     * 获取采集日志
     */
    public function getLogs(): Response
    {
        try {
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 50);
            $taskId = $this->request->param('task_id');
            $sourceId = $this->request->param('source_id');
            $logType = $this->request->param('log_type');
            $logLevel = $this->request->param('log_level');
            $startTime = $this->request->param('start_time');
            $endTime = $this->request->param('end_time');
            $keyword = $this->request->param('keyword');

            $where = [];
            if ($taskId) {
                $where['l.task_id'] = $taskId;
            }
            if ($sourceId) {
                $where['l.source_id'] = $sourceId;
            }
            if ($logType) {
                $where['l.log_type'] = $logType;
            }
            if ($logLevel) {
                $where['l.log_level'] = $logLevel;
            }
            if ($startTime) {
                $where[] = ['l.created_at', '>=', $startTime];
            }
            if ($endTime) {
                $where[] = ['l.created_at', '<=', $endTime];
            }

            $query = \think\facade\Db::table('collect_logs')
                ->alias('l')
                ->leftJoin('collect_tasks t', 'l.task_id = t.id')
                ->leftJoin('collect_sources s', 'l.source_id = s.id')
                ->where($where)
                ->field('l.*, t.task_name, s.name as source_name');

            // 关键词搜索
            if ($keyword) {
                $query->where('l.message', 'like', '%' . $keyword . '%');
            }

            $total = $query->count();
            $list = $query->order('l.id desc')
                ->page($page)
                ->limit($limit)
                ->select();

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]);
        } catch (\Exception $e) {
            Log::error('获取采集日志失败', ['error' => $e->getMessage()]);
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 测试采集源连接
     */
    public function testCollectSource(): Response
    {
        try {
            // 对于POST请求，尝试从请求体中获取JSON数据
            $rawInput = file_get_contents('php://input');
            $jsonData = json_decode($rawInput, true);

            if ($jsonData) {
                $data = $jsonData;
            } else {
                $data = $this->request->post();
            }

            if (empty($data['api_url'])) {
                return $this->error('请输入API地址');
            }

            // 测试连接
            $testData = [
                'api_url' => $data['api_url'],
                'type' => $data['type'] ?? 'xml',
                'name' => 'test'
            ];

            $testTask = [
                'config' => json_encode(['ac' => 'list', 'page' => 1])
            ];

            if ($data['type'] === 'xml') {
                $result = $this->collectService->collectXmlData($testData, $testTask);
            } else {
                $result = $this->collectService->collectJsonData($testData, $testTask);
            }

            if ($result['code'] === 1) {
                return $this->success([
                    'status' => 'success',
                    'message' => '连接成功',
                    'data' => $result['data']
                ], '测试成功');
            } else {
                return $this->error('连接失败: ' . $result['msg']);
            }

        } catch (\Exception $e) {
            Log::error('测试采集源失败: ' . $e->getMessage());
            return $this->error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 执行XML采集
     */
    public function collectXml(): Response
    {
        try {
            // 对于POST请求，尝试从请求体中获取JSON数据
            $rawInput = file_get_contents('php://input');
            $jsonData = json_decode($rawInput, true);

            if ($jsonData) {
                $data = $jsonData;
            } else {
                $data = $this->request->post();
            }

            if (empty($data['source_id'])) {
                return $this->error('请选择采集源');
            }

            // 获取采集源信息
            $source = \think\facade\Db::table('collect_sources')
                ->where('id', $data['source_id'])
                ->find();

            if (!$source) {
                return $this->error('采集源不存在');
            }

            // 构建任务配置
            $taskConfig = [
                'ac' => $data['ac'] ?? 'list',
                'page' => $data['page'] ?? 1,
                't' => $data['t'] ?? '',
                'h' => $data['h'] ?? '',
                'ids' => $data['ids'] ?? '',
                'wd' => $data['wd'] ?? ''
            ];

            $task = [
                'config' => json_encode($taskConfig)
            ];

            // 执行采集
            $result = $this->collectService->collectXmlData($source, $task);

            if ($result['code'] === 1) {
                return $this->success($result['data'], $result['msg']);
            } else {
                return $this->error($result['msg']);
            }

        } catch (\Exception $e) {
            Log::error('XML采集失败: ' . $e->getMessage());
            return $this->error('采集失败: ' . $e->getMessage());
        }
    }

    /**
     * 执行JSON采集
     */
    public function collectJson(): Response
    {
        try {
            // 对于POST请求，尝试从请求体中获取JSON数据
            $rawInput = file_get_contents('php://input');
            $jsonData = json_decode($rawInput, true);

            if ($jsonData) {
                $data = $jsonData;
            } else {
                $data = $this->request->post();
            }

            if (empty($data['source_id'])) {
                return $this->error('请选择采集源');
            }

            // 获取采集源信息
            $source = \think\facade\Db::table('collect_sources')
                ->where('id', $data['source_id'])
                ->find();

            if (!$source) {
                return $this->error('采集源不存在');
            }

            // 构建任务配置
            $taskConfig = [
                'ac' => $data['ac'] ?? 'list',
                'page' => $data['page'] ?? 1,
                't' => $data['t'] ?? '',
                'h' => $data['h'] ?? '',
                'ids' => $data['ids'] ?? '',
                'wd' => $data['wd'] ?? ''
            ];

            $task = [
                'config' => json_encode($taskConfig)
            ];

            // 执行采集
            $result = $this->collectService->collectJsonData($source, $task);

            if ($result['code'] === 1) {
                return $this->success($result['data'], $result['msg']);
            } else {
                return $this->error($result['msg']);
            }

        } catch (\Exception $e) {
            Log::error('JSON采集失败: ' . $e->getMessage());
            return $this->error('采集失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取采集统计数据
     */
    public function getStatistics(): Response
    {
        try {
            $period = $this->request->param('period', 'today');
            $stats = $this->monitorService->getCollectStatistics($period);

            return $this->success($stats, '获取统计数据成功');
        } catch (\Exception $e) {
            Log::error('获取采集统计失败', ['error' => $e->getMessage()]);
            return $this->error('获取统计失败');
        }
    }

    /**
     * 重试失败任务
     */
    public function retryFailedTasks(): Response
    {
        try {
            $maxRetries = (int)$this->request->param('max_retries', 3);
            $results = $this->monitorService->retryFailedTasks($maxRetries);

            return $this->success($results, '失败任务重试调度完成');
        } catch (\Exception $e) {
            Log::error('重试失败任务失败', ['error' => $e->getMessage()]);
            return $this->error('重试失败');
        }
    }

    /**
     * 获取采集源健康状态
     */
    public function getSourceHealth(): Response
    {
        try {
            $healthStatus = $this->monitorService->getSourceHealthStatus();

            return $this->success($healthStatus, '获取健康状态成功');
        } catch (\Exception $e) {
            Log::error('获取采集源健康状态失败', ['error' => $e->getMessage()]);
            return $this->error('获取健康状态失败');
        }
    }

    /**
     * 生成采集报表
     */
    public function generateReport(): Response
    {
        try {
            $period = $this->request->param('period', 'week');
            $report = $this->monitorService->generateCollectReport($period);

            return $this->success($report, '报表生成成功');
        } catch (\Exception $e) {
            Log::error('生成采集报表失败', ['error' => $e->getMessage()]);
            return $this->error('报表生成失败');
        }
    }

    /**
     * 清理过期数据
     */
    public function cleanupOldData(): Response
    {
        try {
            $daysToKeep = (int)$this->request->param('days_to_keep', 30);
            $results = $this->monitorService->cleanupOldData($daysToKeep);

            return $this->success($results, '数据清理完成');
        } catch (\Exception $e) {
            Log::error('清理过期数据失败', ['error' => $e->getMessage()]);
            return $this->error('数据清理失败');
        }
    }

    /**
     * 检查采集源连通性
     */
    public function checkSourceConnectivity(): Response
    {
        try {
            $sourceId = (int)$this->request->param('id');

            if ($sourceId <= 0) {
                return $this->error('采集源ID无效');
            }

            $source = \think\facade\Db::table('collect_sources')
                ->where('id', $sourceId)
                ->find();

            if (!$source) {
                return $this->error('采集源不存在');
            }

            $result = $this->collectService->checkSourceConnectivity($source);

            return $this->success($result, '连通性检查完成');
        } catch (\Exception $e) {
            Log::error('检查采集源连通性失败', ['error' => $e->getMessage()]);
            return $this->error('检查失败');
        }
    }

    /**
     * 批量检查采集源健康状态
     */
    public function batchCheckHealth(): Response
    {
        try {
            $results = $this->collectService->batchCheckSourceHealth();

            return $this->success($results, '批量健康检查完成');
        } catch (\Exception $e) {
            Log::error('批量健康检查失败', ['error' => $e->getMessage()]);
            return $this->error('检查失败');
        }
    }

    /**
     * 自动健康检查
     */
    public function autoHealthCheck(): Response
    {
        try {
            $result = $this->monitorService->autoHealthCheck();
            return $this->success($result, '自动健康检查完成');
        } catch (\Exception $e) {
            Log::error('自动健康检查失败', ['error' => $e->getMessage()]);
            return $this->error('自动健康检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取采集趋势数据
     */
    public function getCollectTrends(): Response
    {
        try {
            $days = (int)$this->request->param('days', 7);
            $result = $this->monitorService->getCollectTrendsData($days);
            return $this->success($result, '获取采集趋势数据成功');
        } catch (\Exception $e) {
            Log::error('获取采集趋势数据失败', ['error' => $e->getMessage()]);
            return $this->error('获取采集趋势数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取采集源性能分布数据
     */
    public function getSourcePerformance(): Response
    {
        try {
            $result = $this->monitorService->getSourcePerformanceData();
            return $this->success($result, '获取采集源性能数据成功');
        } catch (\Exception $e) {
            Log::error('获取采集源性能数据失败', ['error' => $e->getMessage()]);
            return $this->error('获取采集源性能数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作采集源
     */
    public function batchOperateSources(): Response
    {
        try {
            $data = $this->getRequestData();
            $sourceIds = $data['source_ids'] ?? [];
            $operation = $data['operation'] ?? '';

            if (empty($sourceIds) || !is_array($sourceIds)) {
                return $this->error('请选择要操作的采集源');
            }

            if (empty($operation)) {
                return $this->error('请选择操作类型');
            }

            $results = [];

            switch ($operation) {
                case 'enable':
                    $affected = \think\facade\Db::table('collect_sources')
                        ->where('id', 'in', $sourceIds)
                        ->update(['status' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
                    $results['message'] = "已启用 {$affected} 个采集源";
                    break;

                case 'disable':
                    $affected = \think\facade\Db::table('collect_sources')
                        ->where('id', 'in', $sourceIds)
                        ->update(['status' => 0, 'updated_at' => date('Y-m-d H:i:s')]);
                    $results['message'] = "已禁用 {$affected} 个采集源";
                    break;

                case 'delete':
                    // 检查是否有正在运行的任务
                    $runningTasks = \think\facade\Db::table('collect_tasks')
                        ->where('source_id', 'in', $sourceIds)
                        ->where('status', 'running')
                        ->count();

                    if ($runningTasks > 0) {
                        return $this->error('有正在运行的采集任务，无法删除');
                    }

                    $affected = \think\facade\Db::table('collect_sources')
                        ->where('id', 'in', $sourceIds)
                        ->delete();
                    $results['message'] = "已删除 {$affected} 个采集源";
                    break;

                default:
                    return $this->error('不支持的操作类型');
            }

            $results['affected_count'] = $affected ?? 0;

            return $this->success($results, '批量操作完成');
        } catch (\Exception $e) {
            Log::error('批量操作采集源失败', ['error' => $e->getMessage()]);
            return $this->error('批量操作失败');
        }
    }

    /**
     * 批量操作采集任务
     */
    public function batchOperateTasks(): Response
    {
        try {
            $data = $this->getRequestData();
            $taskIds = $data['task_ids'] ?? [];
            $operation = $data['operation'] ?? '';

            if (empty($taskIds) || !is_array($taskIds)) {
                return $this->error('请选择要操作的任务');
            }

            if (empty($operation)) {
                return $this->error('请选择操作类型');
            }

            $results = [];

            switch ($operation) {
                case 'start':
                    $pendingTasks = \think\facade\Db::table('collect_tasks')
                        ->where('id', 'in', $taskIds)
                        ->where('status', 'pending')
                        ->select();

                    foreach ($pendingTasks as $task) {
                        // 启动任务的逻辑
                        \think\facade\Db::table('collect_tasks')
                            ->where('id', $task['id'])
                            ->update(['status' => 'running', 'updated_at' => date('Y-m-d H:i:s')]);
                    }

                    $results['message'] = "已启动 " . count($pendingTasks) . " 个任务";
                    break;

                case 'stop':
                    $affected = \think\facade\Db::table('collect_tasks')
                        ->where('id', 'in', $taskIds)
                        ->where('status', 'running')
                        ->update(['status' => 'stopped', 'updated_at' => date('Y-m-d H:i:s')]);
                    $results['message'] = "已停止 {$affected} 个任务";
                    break;

                case 'delete':
                    $affected = \think\facade\Db::table('collect_tasks')
                        ->where('id', 'in', $taskIds)
                        ->where('status', 'not in', ['running'])
                        ->delete();
                    $results['message'] = "已删除 {$affected} 个任务";
                    break;

                case 'reset':
                    $affected = \think\facade\Db::table('collect_tasks')
                        ->where('id', 'in', $taskIds)
                        ->update([
                            'status' => 'pending',
                            'progress' => 0,
                            'collected_count' => 0,
                            'success_count' => 0,
                            'failed_count' => 0,
                            'current_page' => 1,
                            'error_message' => null,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    $results['message'] = "已重置 {$affected} 个任务";
                    break;

                default:
                    return $this->error('不支持的操作类型');
            }

            $results['affected_count'] = $affected ?? count($pendingTasks ?? []);

            return $this->success($results, '批量操作完成');
        } catch (\Exception $e) {
            Log::error('批量操作任务失败', ['error' => $e->getMessage()]);
            return $this->error('批量操作失败');
        }
    }
}
