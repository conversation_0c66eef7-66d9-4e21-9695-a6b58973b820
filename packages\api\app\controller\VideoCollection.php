<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Db;
use app\service\ResponseService;
use app\service\ValidationService;

/**
 * 视频收藏控制器
 * 
 * 处理视频收藏相关的所有操作
 */
class VideoCollection
{
    protected ResponseService $responseService;
    protected ValidationService $validationService;

    public function __construct()
    {
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
    }

    /**
     * 收藏视频
     *
     * @param Request $request
     * @param int $id 视频ID
     * @return Response
     */
    public function collect(Request $request, int $id): Response
    {
        try {
            // 从URL路径获取视频ID
            $videoId = $id;

            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            // 验证视频ID
            if ($videoId <= 0) {
                return $this->responseService->error('视频ID无效', 400);
            }

            // 检查视频是否存在
            $video = Db::table('videos')
                ->where('id', $videoId)
                ->where('status', 'published')
                ->find();

            if (!$video) {
                return $this->responseService->error('视频不存在', 404);
            }

            // 检查用户是否存在
            $user = Db::table('users')
                ->where('id', $userId)
                ->where('status', 1)
                ->find();

            if (!$user) {
                return $this->responseService->error('用户不存在', 404);
            }

            // 检查是否已经收藏
            $existingCollection = Db::table('video_collections')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->find();

            if ($existingCollection) {
                return $this->responseService->error('已经收藏过了', 400);
            }

            // 开始事务
            Db::startTrans();
            try {
                // 添加收藏记录
                Db::table('video_collections')->insert([
                    'video_id' => $videoId,
                    'user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // 更新视频收藏数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->inc('collect_count', 1)
                    ->update();

                Db::commit();

                // 获取最新的收藏数
                $newCollectCount = Db::table('videos')
                    ->where('id', $videoId)
                    ->value('collect_count');

                return $this->responseService->success([
                    'message' => '收藏成功',
                    'collect_count' => $newCollectCount,
                    'is_collected' => true
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->responseService->error('收藏失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 取消收藏
     *
     * @param Request $request
     * @param int $id 视频ID
     * @return Response
     */
    public function uncollect(Request $request, int $id): Response
    {
        try {
            // 从URL路径获取视频ID
            $videoId = $id;

            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            // 验证视频ID
            if ($videoId <= 0) {
                return $this->responseService->error('视频ID无效', 400);
            }

            // 检查收藏记录是否存在
            $existingCollection = Db::table('video_collections')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->find();

            if (!$existingCollection) {
                return $this->responseService->error('还没有收藏', 400);
            }

            // 开始事务
            Db::startTrans();
            try {
                // 删除收藏记录
                Db::table('video_collections')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->delete();

                // 更新视频收藏数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->dec('collect_count', 1)
                    ->update();

                Db::commit();

                // 获取最新的收藏数
                $newCollectCount = Db::table('videos')
                    ->where('id', $videoId)
                    ->value('collect_count');

                return $this->responseService->success([
                    'message' => '取消收藏成功',
                    'collect_count' => max(0, $newCollectCount), // 确保不为负数
                    'is_collected' => false
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->responseService->error('取消收藏失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 检查用户是否收藏了视频
     *
     * @param Request $request
     * @return Response
     */
    public function checkCollection(Request $request): Response
    {
        try {
            $videoId = (int)$request->get('video_id');
            $userId = (int)$request->get('user_id');

            if (!$videoId || !$userId) {
                return $this->responseService->error('参数错误', 400);
            }

            // 检查收藏状态
            $isCollected = Db::table('video_collections')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->count() > 0;

            // 获取视频收藏数
            $collectCount = Db::table('videos')
                ->where('id', $videoId)
                ->value('collect_count') ?: 0;

            return $this->responseService->success([
                'is_collected' => $isCollected,
                'collect_count' => $collectCount
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('检查收藏状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取用户的收藏视频列表
     *
     * @param Request $request
     * @return Response
     */
    public function getUserCollections(Request $request): Response
    {
        try {
            $userId = (int)$request->get('user_id');
            $page = (int)($request->get('page') ?? 1);
            $limit = (int)($request->get('limit') ?? 20);
            $offset = ($page - 1) * $limit;

            if (!$userId) {
                return $this->responseService->error('用户ID不能为空', 400);
            }

            // 获取用户收藏的视频列表
            $videos = Db::table('video_collections')
                ->alias('vc')
                ->join('videos v', 'vc.video_id = v.id')
                ->join('users u', 'v.user_id = u.id')
                ->where('vc.user_id', $userId)
                ->where('v.status', 'published')
                ->field('v.id, v.title, v.description, v.thumbnail, v.duration, v.view_count, v.like_count, v.collect_count, v.created_at, u.username, u.nickname, vc.created_at as collected_at')
                ->order('vc.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 获取总数
            $total = Db::table('video_collections')
                ->alias('vc')
                ->join('videos v', 'vc.video_id = v.id')
                ->where('vc.user_id', $userId)
                ->where('v.status', 'published')
                ->count();

            return $this->responseService->success([
                'videos' => $videos,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('获取用户收藏列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取视频的收藏列表
     *
     * @param Request $request
     * @return Response
     */
    public function getVideoCollections(Request $request): Response
    {
        try {
            $videoId = (int)$request->get('video_id');
            $page = (int)($request->get('page') ?? 1);
            $limit = (int)($request->get('limit') ?? 20);
            $offset = ($page - 1) * $limit;

            if (!$videoId) {
                return $this->responseService->error('视频ID不能为空', 400);
            }

            // 获取收藏列表
            $collections = Db::table('video_collections')
                ->alias('vc')
                ->join('users u', 'vc.user_id = u.id')
                ->where('vc.video_id', $videoId)
                ->field('u.id, u.username, u.nickname, u.avatar, vc.created_at')
                ->order('vc.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 获取总数
            $total = Db::table('video_collections')
                ->where('video_id', $videoId)
                ->count();

            return $this->responseService->success([
                'collections' => $collections,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('获取收藏列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量取消收藏
     *
     * @param Request $request
     * @return Response
     */
    public function batchUncollect(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证参数
            $rules = [
                'video_ids' => 'require|array',
                'user_id' => 'require|integer|gt:0'
            ];
            
            $validation = $this->validationService->validate($data, $rules);
            if (!$validation['valid']) {
                return $this->responseService->error($validation['message'], 400);
            }

            $videoIds = $data['video_ids'];
            $userId = (int)$data['user_id'];

            if (empty($videoIds)) {
                return $this->responseService->error('视频ID列表不能为空', 400);
            }

            // 开始事务
            Db::startTrans();
            try {
                $successCount = 0;
                
                foreach ($videoIds as $videoId) {
                    $videoId = (int)$videoId;
                    
                    // 检查收藏记录是否存在
                    $existingCollection = Db::table('video_collections')
                        ->where('video_id', $videoId)
                        ->where('user_id', $userId)
                        ->find();

                    if ($existingCollection) {
                        // 删除收藏记录
                        Db::table('video_collections')
                            ->where('video_id', $videoId)
                            ->where('user_id', $userId)
                            ->delete();

                        // 更新视频收藏数
                        Db::table('videos')
                            ->where('id', $videoId)
                            ->dec('collect_count', 1)
                            ->update();

                        $successCount++;
                    }
                }

                Db::commit();

                return $this->responseService->success([
                    'message' => "成功取消收藏 {$successCount} 个视频",
                    'success_count' => $successCount
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->responseService->error('批量取消收藏失败: ' . $e->getMessage(), 500);
        }
    }
}
