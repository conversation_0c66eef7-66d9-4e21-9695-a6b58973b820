<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Db;
use think\facade\Validate;
use think\facade\Log;
use think\facade\Cache;
use app\model\Video as VideoModel;
use app\service\VideoService;
use app\service\ResponseService;
use app\service\DatabaseOptimizationService;
use app\service\ValidationService;

/**
 * 视频控制器
 *
 * 提供视频相关的RESTful API接口
 * 包括视频列表、详情、创建、更新、删除等功能
 *
 * <AUTHOR> Platform Team
 * @version 1.0
 */
class Video
{
    /**
     * 视频服务
     * @var VideoService
     */
    private $videoService;

    /**
     * 响应服务
     * @var ResponseService
     */
    private $responseService;

    /**
     * 验证服务
     * @var ValidationService
     */
    private $validationService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->videoService = new VideoService();
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
    }
    /**
     * 获取视频列表
     *
     * 支持分页、筛选、排序等功能
     *
     * @param Request $request 请求对象
     * @return Response JSON响应
     */
    public function index(Request $request): Response
    {
        try {
            $params = $request->get();

            // 添加用户和分类信息
            $params['include_user'] = true;
            $params['include_category'] = true;

            $result = $this->videoService->getVideoList($params);

            return $this->responseService->success($result, '获取视频列表成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '获取视频列表失败');
        }
    }

    /**
     * 构建视频列表查询条件
     *
     * @param array $params 查询参数
     * @return array 查询条件数组
     */
    private function buildVideoListWhere(array $params): array
    {
        $where = [];
        $where[] = ['v.status', '=', 'published']; // 已发布
        $where[] = ['v.audit_status', '=', 'approved']; // 审核通过

        // 视频类型筛选
        if (!empty($params['video_type'])) {
            $where[] = ['v.video_type', '=', $params['video_type']];
        }

        // 分类筛选
        if (!empty($params['category_id'])) {
            $where[] = ['v.category_id', '=', $params['category_id']];
        }

        // 用户筛选
        if (!empty($params['user_id'])) {
            $where[] = ['v.user_id', '=', $params['user_id']];
        }

        // 当前用户视频筛选（用于我的作品页面）
        if (!empty($params['user_only']) && isset($this->request->userInfo['id'])) {
            $where = []; // 重置条件，不限制发布状态和审核状态
            $where[] = ['v.user_id', '=', $this->request->userInfo['id']];

            // 状态筛选
            if (!empty($params['status'])) {
                $where[] = ['v.status', '=', $params['status']];
            }

            // 审核状态筛选
            if (!empty($params['audit_status'])) {
                $where[] = ['v.audit_status', '=', $params['audit_status']];
            }
        }

        return $where;
    }

    /**
     * 获取视频列表数据
     *
     * @param array $where 查询条件
     * @param string $order 排序规则
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @return array 视频列表
     */
    private function getVideoListData(array $where, string $order, int $offset, int $limit): array
    {
        return Db::table('videos')
            ->alias('v')
            ->leftJoin('users u', 'v.user_id = u.id')
            ->leftJoin('video_categories c', 'v.category_id = c.id')
            ->field('v.*, u.username, u.nickname, u.avatar as user_avatar, c.name as category_name')
            ->where($where)
            ->order($order)
            ->limit($offset, $limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取视频列表总数
     *
     * @param array $where 查询条件
     * @return int 总数
     */
    private function getVideoListCount(array $where): int
    {
        return Db::table('videos')->alias('v')->where($where)->count();
    }

    /**
     * 格式化视频列表数据
     *
     * @param array $videos 原始视频数据
     * @return array 格式化后的视频数据
     */
    private function formatVideoList(array $videos): array
    {
        foreach ($videos as &$video) {
            $video = $this->formatVideoData($video);
        }
        return $videos;
    }

    /**
     * 格式化单个视频数据
     *
     * @param array $video 原始视频数据
     * @return array 格式化后的视频数据
     */
    private function formatVideoData(array $video): array
    {
        $video['user'] = [
            'id' => $video['user_id'] ?? null,
            'username' => $video['username'] ?? '',
            'nickname' => $video['nickname'] ?? '',
            'avatar' => $video['user_avatar'] ?? ''
        ];

        $video['category'] = [
            'id' => $video['category_id'] ?? null,
            'name' => $video['category_name'] ?? ''
        ];

        // 处理封面图URL - 确保返回完整的访问路径
        if (!empty($video['cover_image'])) {
            // 如果不是完整URL，添加基础路径
            if (!str_starts_with($video['cover_image'], 'http://') && !str_starts_with($video['cover_image'], 'https://')) {
                // 确保路径以/开头
                $coverPath = str_starts_with($video['cover_image'], '/') ? $video['cover_image'] : '/' . $video['cover_image'];
                $video['cover_image'] = $coverPath;
            }
        }

        // 处理备份封面图URL
        if (!empty($video['cover_image_backup'])) {
            // 如果不是完整URL，添加基础路径
            if (!str_starts_with($video['cover_image_backup'], 'http://') && !str_starts_with($video['cover_image_backup'], 'https://')) {
                // 确保路径以/开头
                $backupCoverPath = str_starts_with($video['cover_image_backup'], '/') ? $video['cover_image_backup'] : '/' . $video['cover_image_backup'];
                $video['cover_image_backup'] = $backupCoverPath;
            }
        }

        // 获取播放地址
        $video['play_urls'] = $this->getVideoPlayUrls($video['id']);

        // 移除冗余字段
        unset($video['username'], $video['nickname'], $video['user_avatar'], $video['category_name']);

        return $video;
    }

    /**
     * 获取视频播放地址
     *
     * @param int $videoId 视频ID
     * @return array 播放地址列表
     */
    private function getVideoPlayUrls(int $videoId): array
    {
        try {
            // 从video_play_urls表获取播放地址
            $playUrls = Db::table('video_play_urls')
                ->where('video_id', $videoId)
                ->where('is_active', 1)
                ->order('sort_order', 'asc')
                ->select()
                ->toArray();

            return $playUrls;
        } catch (\Exception $e) {
            Log::error('获取视频播放地址失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取视频详情
     *
     * 获取指定ID的视频详细信息，并增加观看次数
     *
     * @param int $id 视频ID
     * @return Response JSON响应
     */
    public function read($id): Response
    {
        try {
            // 参数验证
            if (!is_numeric($id) || $id <= 0) {
                return $this->responseService->validationError('视频ID无效');
            }

            // 使用视频服务获取视频详情
            $video = $this->videoService->getVideoDetail((int)$id, true);

            if (!$video) {
                return $this->responseService->notFound('视频不存在或已下架');
            }

            return $this->responseService->success($video, '获取视频详情成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '获取视频详情失败');
        }
    }

    /**
     * 获取视频详情数据
     *
     * @param int $id 视频ID
     * @return array|null 视频数据或null
     */
    private function getVideoDetail(int $id): ?array
    {
        $video = Db::table('videos')
            ->alias('v')
            ->leftJoin('users u', 'v.user_id = u.id')
            ->leftJoin('video_categories c', 'v.category_id = c.id')
            ->field('v.*, u.username, u.nickname, u.avatar as user_avatar, c.name as category_name')
            ->where('v.id', $id)
            ->where('v.status', 'published')
            ->where('v.audit_status', 'approved')
            ->find();

        return $video ? $video : null;
    }

    /**
     * 异步增加视频观看次数
     *
     * @param int $id 视频ID
     * @return void
     */
    private function incrementViewCountAsync(int $id): void
    {
        try {
            // 使用队列或异步方式更新观看次数
            // 这里简化为直接更新，实际项目中可以使用消息队列
            Db::table('videos')->where('id', $id)->inc('view_count')->update();

            // 清除相关缓存
            $this->cacheService->clearVideoCache($id);
        } catch (\Exception $e) {
            // 记录日志但不影响主流程
            Log::error("异步增加观看次数失败: {$e->getMessage()}", ['video_id' => $id]);
        }
    }

    /**
     * 增加视频观看次数（保留兼容性）
     *
     * @param int $id 视频ID
     * @return void
     */
    private function incrementViewCount(int $id): void
    {
        $this->incrementViewCountAsync($id);
    }

    /**
     * 创建视频
     *
     * 创建新的视频记录，需要用户认证
     *
     * @param Request $request 请求对象
     * @return Response JSON响应
     */
    public function save(Request $request): Response
    {
        try {
            $data = $request->post();

            // 获取当前用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->unauthorized('请先登录');
            }
            $userId = $userInfo['id'];

            // 使用视频服务创建视频
            $result = $this->videoService->createVideo($data, $userId);

            // 记录操作日志
            Log::info("用户创建视频", [
                'user_id' => $userId,
                'video_id' => $result['video_id'],
                'title' => $data['title'],
                'video_type' => $data['video_type'] ?? 'upload'
            ]);

            return $this->responseService->success($result, '视频创建成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '视频创建失败');
        }
    }

    /**
     * 验证分类是否存在
     *
     * @param int $categoryId 分类ID
     * @return bool 是否存在
     */
    private function validateCategoryExists(int $categoryId): bool
    {
        return Db::table('video_categories')
            ->where('id', $categoryId)
            ->where('status', 1)
            ->count() > 0;
    }

    /**
     * 构建视频数据
     *
     * @param array $data 输入数据
     * @param int $userId 用户ID
     * @return array 视频数据
     */
    private function buildVideoData(array $data, int $userId): array
    {
        $now = date('Y-m-d H:i:s');

        return [
            'title' => $data['title'],
            'description' => $data['description'] ?? '',
            'video_type' => $data['video_type'],
            'category_id' => $data['category_id'],
            'user_id' => $userId,
            'file_path' => $data['file_path'],
            'cover_image' => $data['cover_image'] ?? '',
            'duration' => $data['duration'] ?? 0,
            'file_size' => $data['file_size'] ?? 0,
            'width' => $data['width'] ?? 0,
            'height' => $data['height'] ?? 0,
            'format' => $data['format'] ?? 'mp4',
            'is_free' => $data['is_free'] ?? 1,
            'points_price' => $data['points_price'] ?? 0,
            'status' => 'published',
            'audit_status' => 'pending',
            'created_at' => $now,
            'updated_at' => $now
        ];
    }

    /**
     * 创建视频记录
     *
     * @param array $videoData 视频数据
     * @return int 视频ID
     */
    private function createVideoRecord(array $videoData): int
    {
        return Db::table('videos')->insertGetId($videoData);
    }

    /**
     * 触发视频处理
     */
    private function triggerVideoProcessing(int $videoId, string $filePath): void
    {
        try {
            // 检查文件是否存在
            $fullPath = root_path() . 'public' . $filePath;
            if (!file_exists($fullPath)) {
                Log::warning('视频文件不存在，跳过处理', ['video_id' => $videoId, 'file_path' => $filePath]);
                return;
            }

            // 创建处理状态记录
            Db::table('video_processing_status')->where('video_id', $videoId)->delete();
            Db::table('video_processing_status')->insert([
                'video_id' => $videoId,
                'status' => 'pending',
                'progress' => 0,
                'current_step' => '等待处理',
                'job_id' => 'auto_' . time() . '_' . $videoId,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 启动后台处理进程
            $command = "php " . root_path() . "packages/api/process_single_video.php {$videoId}";

            if (PHP_OS_FAMILY === 'Windows') {
                pclose(popen("start /B {$command}", "r"));
            } else {
                exec("{$command} > /dev/null 2>&1 &");
            }

            Log::info('视频处理任务已启动', ['video_id' => $videoId, 'file_path' => $filePath]);

        } catch (\Exception $e) {
            Log::error('启动视频处理失败', [
                'video_id' => $videoId,
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新视频
     *
     * 更新指定ID的视频信息
     *
     * @param Request $request 请求对象
     * @param int $id 视频ID
     * @return Response JSON响应
     */
    public function update(Request $request, $id): Response
    {
        try {
            // 参数验证
            if (!is_numeric($id) || $id <= 0) {
                return $this->responseService->validationError('视频ID无效');
            }

            $data = $request->put();

            // 数据验证
            $validation = $this->validationService->validate(
                $data,
                $this->validationService->getVideoUpdateRules(),
                $this->validationService->getVideoUpdateMessages()
            );

            if ($validation !== true) {
                return $this->responseService->validationError($validation['error']);
            }

            // 检查视频是否存在
            $video = $this->getVideoById((int)$id);
            if (!$video) {
                return $this->responseService->notFound('视频不存在');
            }

            // 验证分类是否存在（如果提供了分类ID）
            if (isset($data['category_id']) && !$this->validateCategoryExists($data['category_id'])) {
                return $this->responseService->validationError('指定的分类不存在');
            }

            // 构建更新数据
            $updateData = $this->buildUpdateData($data);

            if (empty($updateData)) {
                return $this->responseService->validationError('没有需要更新的数据');
            }

            // 执行更新
            $this->updateVideoRecord((int)$id, $updateData);

            // 记录操作日志
            Log::info("视频更新", [
                'video_id' => $id,
                'updated_fields' => array_keys($updateData),
                'user_id' => $request->userInfo['id'] ?? null
            ]);

            return $this->responseService->success(['video_id' => $id], '视频更新成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '视频更新失败');
        }
    }

    /**
     * 根据ID获取视频
     *
     * @param int $id 视频ID
     * @return array|null 视频数据或null
     */
    private function getVideoById(int $id): ?array
    {
        $video = Db::table('videos')->where('id', $id)->find();
        if (!$video) {
            return null;
        }

        // Db::table()->find() 返回的是数组，直接返回
        return $video;
    }

    /**
     * 构建更新数据
     *
     * @param array $data 输入数据
     * @return array 更新数据
     */
    private function buildUpdateData(array $data): array
    {
        $updateData = [];
        $allowedFields = ['title', 'description', 'video_type', 'category_id', 'cover_image', 'is_free', 'points_price'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        if (!empty($updateData)) {
            $updateData['updated_at'] = date('Y-m-d H:i:s');
        }

        return $updateData;
    }

    /**
     * 更新视频记录
     *
     * @param int $id 视频ID
     * @param array $updateData 更新数据
     * @return void
     */
    private function updateVideoRecord(int $id, array $updateData): void
    {
        Db::table('videos')->where('id', $id)->update($updateData);
    }

    /**
     * 删除视频
     *
     * 软删除指定ID的视频
     *
     * @param int $id 视频ID
     * @return Response JSON响应
     */
    public function delete($id): Response
    {
        try {
            // 参数验证
            if (!is_numeric($id) || $id <= 0) {
                return $this->responseService->validationError('视频ID无效');
            }

            // 检查视频是否存在
            $video = $this->getVideoById((int)$id);
            if (!$video) {
                return $this->responseService->notFound('视频不存在');
            }

            // 检查视频是否已删除
            if ($video['status'] === 'deleted') {
                return $this->responseService->validationError('视频已删除');
            }

            // 执行软删除
            $this->softDeleteVideo((int)$id);

            // 记录操作日志
            Log::info("视频删除", [
                'video_id' => $id,
                'title' => $video['title'],
                'user_id' => $video['user_id']
            ]);

            return $this->responseService->success(['video_id' => $id], '视频删除成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '视频删除失败');
        }
    }

    /**
     * 软删除视频
     *
     * @param int $id 视频ID
     * @return void
     */
    private function softDeleteVideo(int $id): void
    {
        Db::table('videos')->where('id', $id)->update([
            'status' => 'deleted',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 视频状态管理 - [v20240719]架构规范化
     * 使用状态机替换临时fix接口
     * @return \think\Response
     */
    public function manageVideoStatus()
    {
        try {
            $action = $this->request->param('action', 'batch_publish');
            $videoIds = $this->request->param('video_ids', []);

            switch ($action) {
                case 'batch_publish':
                    $result = $this->videoService->batchPublishVideos($videoIds);
                    return $this->responseService->success($result, '批量发布完成');
                case 'batch_process':
                    $result = $this->videoService->batchProcessVideos($videoIds);
                    return $this->responseService->success($result, '批量处理完成');
                case 'get_status':
                    return $this->getBatchVideoStatus($videoIds);
                default:
                    return $this->responseService->error('不支持的操作', 400);
            }

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '视频状态管理失败');
        }
    }

    /**
     * 批量发布视频 - 使用状态机
     */
    private function batchPublishVideos(array $videoIds = []): Response
    {
        try {
            // 如果没有指定视频ID，处理所有待发布的视频
            if (empty($videoIds)) {
                $videos = Db::table('videos')
                    ->where('status', 'in', ['uploading', 'processing'])
                    ->where('audit_status', '<>', 'rejected')
                    ->field('id')
                    ->select();
                $videoIds = array_column($videos->toArray(), 'id');
            }

            $successCount = 0;
            $failedCount = 0;
            $errors = [];

            foreach ($videoIds as $videoId) {
                try {
                    // 获取或创建视频处理记录
                    $process = \app\model\VideoProcess::where('video_id', $videoId)->find();
                    if (!$process) {
                        $process = \app\model\VideoProcess::createForVideo($videoId);
                    }

                    // 状态机转换：完成处理
                    $process->transitionStatus(
                        \app\model\VideoProcess::STATUS_COMPLETED,
                        '管理员批量发布',
                        ['admin_action' => true, 'batch_publish' => true]
                    );

                    $successCount++;

                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "视频ID {$videoId}: " . $e->getMessage();

                    Log::error('批量发布视频失败', [
                        'video_id' => $videoId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return $this->responseService->success([
                'total_processed' => count($videoIds),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'errors' => $errors,
                'message' => "成功发布 {$successCount} 个视频" . ($failedCount > 0 ? "，{$failedCount} 个失败" : "")
            ], '批量发布完成');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '批量发布失败');
        }
    }

    /**
     * 批量处理视频 - 重新开始处理
     */
    private function batchProcessVideos(array $videoIds): Response
    {
        try {
            $successCount = 0;
            $failedCount = 0;
            $errors = [];

            foreach ($videoIds as $videoId) {
                try {
                    // 获取或创建视频处理记录
                    $process = \app\model\VideoProcess::where('video_id', $videoId)->find();
                    if (!$process) {
                        $process = \app\model\VideoProcess::createForVideo($videoId);
                    }

                    // 状态机转换：开始处理
                    if ($process->status === \app\model\VideoProcess::STATUS_FAILED) {
                        // 失败状态可以重新开始处理
                        $process->transitionStatus(
                            \app\model\VideoProcess::STATUS_PROCESSING,
                            '管理员重新处理',
                            ['admin_action' => true, 'retry' => true]
                        );
                    } elseif ($process->status === \app\model\VideoProcess::STATUS_PENDING) {
                        // 待处理状态转为处理中
                        $process->transitionStatus(
                            \app\model\VideoProcess::STATUS_PROCESSING,
                            '管理员启动处理',
                            ['admin_action' => true]
                        );
                    }

                    $successCount++;

                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "视频ID {$videoId}: " . $e->getMessage();
                }
            }

            return $this->responseService->success([
                'total_processed' => count($videoIds),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ], '批量处理完成');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '批量处理失败');
        }
    }

    /**
     * 获取批量视频状态
     */
    private function getBatchVideoStatus(array $videoIds): Response
    {
        try {
            if (empty($videoIds)) {
                // 获取最近的视频状态
                $videoIds = Db::table('videos')
                    ->order('id', 'desc')
                    ->limit(50)
                    ->column('id');
            }

            $statusData = \app\model\VideoProcess::getBatchStatus($videoIds);

            // 补充没有处理记录的视频
            $missingVideoIds = array_diff($videoIds, array_keys($statusData));
            foreach ($missingVideoIds as $videoId) {
                $statusData[$videoId] = [
                    'status' => \app\model\VideoProcess::STATUS_PENDING,
                    'status_text' => '待处理',
                    'progress' => 0,
                    'error_message' => '',
                    'updated_at' => null
                ];
            }

            return $this->responseService->success($statusData, '获取状态成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '获取状态失败');
        }
    }

    /**
     * 点赞视频
     *
     * 用户对指定视频进行点赞操作
     *
     * @param Request $request 请求对象
     * @param int $id 视频ID
     * @return Response JSON响应
     */
    public function like(Request $request, $id): Response
    {
        try {
            // 参数验证
            if (!is_numeric($id) || $id <= 0) {
                return $this->responseService->validationError('视频ID无效');
            }

            // 获取当前用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->unauthorized('请先登录');
            }
            $userId = $userInfo['id'];

            // 检查视频是否存在
            if (!$this->getVideoById((int)$id)) {
                return $this->responseService->notFound('视频不存在');
            }

            // 检查是否已点赞
            if ($this->hasUserLikedVideo((int)$id, $userId)) {
                return $this->responseService->validationError('已经点赞过了');
            }

            // 执行点赞操作
            $this->addVideoLike((int)$id, $userId);

            return $this->responseService->success(['video_id' => $id], '点赞成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '点赞失败');
        }
    }

    /**
     * 取消点赞
     *
     * 用户取消对指定视频的点赞
     *
     * @param Request $request 请求对象
     * @param int $id 视频ID
     * @return Response JSON响应
     */
    public function unlike(Request $request, $id): Response
    {
        try {
            // 参数验证
            if (!is_numeric($id) || $id <= 0) {
                return $this->responseService->validationError('视频ID无效');
            }

            // 获取当前用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->unauthorized('请先登录');
            }
            $userId = $userInfo['id'];

            // 执行取消点赞操作
            $this->removeVideoLike((int)$id, $userId);

            return $this->responseService->success(['video_id' => $id], '取消点赞成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '取消点赞失败');
        }
    }

    /**
     * 检查用户是否已点赞视频
     *
     * @param int $videoId 视频ID
     * @param int $userId 用户ID
     * @return bool 是否已点赞
     */
    private function hasUserLikedVideo(int $videoId, int $userId): bool
    {
        return Db::table('video_likes')
            ->where('video_id', $videoId)
            ->where('user_id', $userId)
            ->count() > 0;
    }

    /**
     * 添加视频点赞
     *
     * @param int $videoId 视频ID
     * @param int $userId 用户ID
     * @return void
     */
    private function addVideoLike(int $videoId, int $userId): void
    {
        Db::transaction(function () use ($videoId, $userId) {
            // 添加点赞记录
            Db::table('video_likes')->insert([
                'video_id' => $videoId,
                'user_id' => $userId,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 更新视频点赞数
            Db::table('videos')->where('id', $videoId)->inc('like_count')->update();
        });
    }

    /**
     * 移除视频点赞
     *
     * @param int $videoId 视频ID
     * @param int $userId 用户ID
     * @return void
     */
    private function removeVideoLike(int $videoId, int $userId): void
    {
        Db::transaction(function () use ($videoId, $userId) {
            // 删除点赞记录
            $result = Db::table('video_likes')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->delete();

            // 如果删除成功，更新视频点赞数
            if ($result) {
                Db::table('videos')->where('id', $videoId)->dec('like_count')->update();
            }
        });
    }

    /**
     * 收藏视频
     *
     * 用户对指定视频进行收藏操作
     *
     * @param Request $request 请求对象
     * @param int $id 视频ID
     * @return Response JSON响应
     */
    public function collect(Request $request, $id): Response
    {
        try {
            // 参数验证
            if (!is_numeric($id) || $id <= 0) {
                return $this->responseService->validationError('视频ID无效');
            }

            // 获取当前用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->unauthorized('请先登录');
            }
            $userId = $userInfo['id'];

            // 检查视频是否存在
            if (!$this->getVideoById((int)$id)) {
                return $this->responseService->notFound('视频不存在');
            }

            // 检查是否已收藏
            if ($this->hasUserCollectedVideo((int)$id, $userId)) {
                return $this->responseService->validationError('已经收藏过了');
            }

            // 执行收藏操作
            $this->addVideoCollection((int)$id, $userId);

            return $this->responseService->success(['video_id' => $id], '收藏成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '收藏失败');
        }
    }

    /**
     * 取消收藏
     *
     * 用户取消对指定视频的收藏
     *
     * @param Request $request 请求对象
     * @param int $id 视频ID
     * @return Response JSON响应
     */
    public function uncollect(Request $request, $id): Response
    {
        try {
            // 参数验证
            if (!is_numeric($id) || $id <= 0) {
                return $this->responseService->validationError('视频ID无效');
            }

            // 获取当前用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->unauthorized('请先登录');
            }
            $userId = $userInfo['id'];

            // 执行取消收藏操作
            $this->removeVideoCollection((int)$id, $userId);

            return $this->responseService->success(['video_id' => $id], '取消收藏成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '取消收藏失败');
        }
    }

    /**
     * 检查用户是否已收藏视频
     *
     * @param int $videoId 视频ID
     * @param int $userId 用户ID
     * @return bool 是否已收藏
     */
    private function hasUserCollectedVideo(int $videoId, int $userId): bool
    {
        return Db::table('video_collections')
            ->where('video_id', $videoId)
            ->where('user_id', $userId)
            ->count() > 0;
    }

    /**
     * 添加视频收藏
     *
     * @param int $videoId 视频ID
     * @param int $userId 用户ID
     * @return void
     */
    private function addVideoCollection(int $videoId, int $userId): void
    {
        Db::transaction(function () use ($videoId, $userId) {
            // 添加收藏记录
            Db::table('video_collections')->insert([
                'video_id' => $videoId,
                'user_id' => $userId,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 更新视频收藏数
            Db::table('videos')->where('id', $videoId)->inc('collect_count')->update();
        });
    }

    /**
     * 移除视频收藏
     *
     * @param int $videoId 视频ID
     * @param int $userId 用户ID
     * @return void
     */
    private function removeVideoCollection(int $videoId, int $userId): void
    {
        Db::transaction(function () use ($videoId, $userId) {
            // 删除收藏记录
            $result = Db::table('video_collections')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->delete();

            // 如果删除成功，更新视频收藏数
            if ($result) {
                Db::table('videos')->where('id', $videoId)->dec('collect_count')->update();
            }
        });
    }

    /**
     * 搜索视频
     *
     * 根据关键词搜索视频，支持标题、描述、用户昵称搜索
     *
     * @param Request $request 请求对象
     * @return Response JSON响应
     */
    public function search(Request $request): Response
    {
        try {
            $params = $request->get();

            // 参数验证
            $validation = $this->validationService->validate(
                $params,
                $this->validationService->getSearchRules(),
                $this->validationService->getSearchMessages()
            );

            if ($validation !== true) {
                return $this->responseService->validationError($validation['error']);
            }

            $keyword = $params['keyword'];

            // 标准化分页参数
            $pagination = $this->validationService->normalizePaginationParams($params);
            $page = $pagination['page'];
            $limit = $pagination['limit'];
            $offset = ($page - 1) * $limit;

            // 构建搜索条件
            $where = $this->buildSearchWhere($params);

            // 执行搜索
            $videos = $this->searchVideos($where, $keyword, $offset, $limit);
            $total = $this->getSearchCount($where, $keyword);

            // 格式化数据
            $formattedVideos = $this->formatVideoList($videos);

            return $this->responseService->paginated(
                $formattedVideos,
                $total,
                $page,
                $limit,
                "找到 {$total} 个相关视频"
            );

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '搜索失败');
        }
    }

    /**
     * 构建搜索查询条件
     *
     * @param array $params 查询参数
     * @return array 查询条件数组
     */
    private function buildSearchWhere(array $params): array
    {
        $where = [];
        $where[] = ['v.status', '=', 'published'];
        $where[] = ['v.audit_status', '=', 'approved'];

        // 视频类型筛选
        if (!empty($params['video_type'])) {
            $where[] = ['v.video_type', '=', $params['video_type']];
        }

        return $where;
    }

    /**
     * 执行视频搜索
     *
     * @param array $where 基础查询条件
     * @param string $keyword 搜索关键词
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @return array 搜索结果
     */
    private function searchVideos(array $where, string $keyword, int $offset, int $limit): array
    {
        return Db::table('videos')
            ->alias('v')
            ->leftJoin('users u', 'v.user_id = u.id')
            ->leftJoin('video_categories c', 'v.category_id = c.id')
            ->field('v.*, u.username, u.nickname, u.avatar as user_avatar, c.name as category_name')
            ->where($where)
            ->where(function($query) use ($keyword) {
                $query->whereLike('v.title', "%{$keyword}%")
                      ->whereOr('v.description', 'like', "%{$keyword}%")
                      ->whereOr('u.nickname', 'like', "%{$keyword}%");
            })
            ->order('v.view_count desc, v.created_at desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取搜索结果总数
     *
     * @param array $where 基础查询条件
     * @param string $keyword 搜索关键词
     * @return int 总数
     */
    private function getSearchCount(array $where, string $keyword): int
    {
        return Db::table('videos')
            ->alias('v')
            ->leftJoin('users u', 'v.user_id = u.id')
            ->where($where)
            ->where(function($query) use ($keyword) {
                $query->whereLike('v.title', "%{$keyword}%")
                      ->whereOr('v.description', 'like', "%{$keyword}%")
                      ->whereOr('u.nickname', 'like', "%{$keyword}%");
            })
            ->count();
    }

    /**
     * 获取推荐视频
     *
     * 基于互动评分和观看量获取推荐视频列表
     *
     * @param Request $request 请求对象
     * @return Response JSON响应
     */
    public function recommend(Request $request): Response
    {
        try {
            $params = $request->get();

            // 参数验证
            $validation = $this->validationService->validate(
                $params,
                $this->validationService->getVideoListRules(),
                $this->validationService->getVideoListMessages()
            );

            if ($validation !== true) {
                return $this->responseService->validationError($validation['error']);
            }

            // 标准化分页参数
            $pagination = $this->validationService->normalizePaginationParams($params);
            $page = $pagination['page'];
            $limit = $pagination['limit'];
            $offset = ($page - 1) * $limit;

            // 视频类型
            $videoType = $params['video_type'] ?? 'short';

            // 额外参数
            $extraParams = [];
            if (!empty($params['category_id'])) {
                $extraParams['category_id'] = $params['category_id'];
            }
            if (!empty($params['exclude_video_id'])) {
                $extraParams['exclude_video_id'] = $params['exclude_video_id'];
            }

            // 查询推荐视频
            $videos = $this->getRecommendedVideos($videoType, $offset, $limit, $extraParams);
            $total = $this->getRecommendedVideosCount($videoType, $extraParams);

            // 格式化数据
            $formattedVideos = $this->formatVideoList($videos);

            return $this->responseService->paginated(
                $formattedVideos,
                $total,
                $page,
                $limit,
                '推荐视频获取成功'
            );

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '获取推荐视频失败');
        }
    }

    /**
     * 获取推荐视频数据
     *
     * @param string $videoType 视频类型
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @param array $params 额外参数
     * @return array 推荐视频列表
     */
    private function getRecommendedVideos(string $videoType, int $offset, int $limit, array $params = []): array
    {
        $query = Db::table('videos')
            ->alias('v')
            ->leftJoin('users u', 'v.user_id = u.id')
            ->leftJoin('video_categories c', 'v.category_id = c.id')
            ->field('v.*, u.username, u.nickname, u.avatar as user_avatar, c.name as category_name')
            ->where('v.video_type', $videoType)
            ->where('v.status', 'published')
            ->where('v.audit_status', 'approved');

        // 如果指定了分类ID，只推荐同分类的视频
        if (!empty($params['category_id'])) {
            $query->where('v.category_id', $params['category_id']);
        }

        // 如果指定了排除的视频ID，排除该视频
        if (!empty($params['exclude_video_id'])) {
            $query->where('v.id', '<>', $params['exclude_video_id']);
        }

        return $query->order('v.engagement_score desc, v.view_count desc, v.created_at desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取推荐视频总数
     *
     * @param string $videoType 视频类型
     * @param array $params 额外参数
     * @return int 总数
     */
    private function getRecommendedVideosCount(string $videoType, array $params = []): int
    {
        $query = Db::table('videos')
            ->where('video_type', $videoType)
            ->where('status', 'published')
            ->where('audit_status', 'approved');

        // 如果指定了分类ID，只统计同分类的视频
        if (!empty($params['category_id'])) {
            $query->where('category_id', $params['category_id']);
        }

        // 如果指定了排除的视频ID，排除该视频
        if (!empty($params['exclude_video_id'])) {
            $query->where('id', '<>', $params['exclude_video_id']);
        }

        return $query->count();
    }

    /**
     * 记录视频观看
     *
     * @param Request $request
     * @param int $id 视频ID
     * @return Response
     */
    public function recordView(Request $request, int $id): Response
    {
        try {
            // 获取用户信息（可能为空，支持游客观看）
            $userId = isset($request->userInfo) ? ($request->userInfo['id'] ?? null) : null;
            $ip = $request->ip();

            // 使用视频服务记录观看
            $result = $this->videoService->recordView($id, $userId, $ip);

            if ($result) {
                return $this->responseService->success(null, '观看记录成功');
            } else {
                return $this->responseService->success(null, '观看记录已存在');
            }

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '记录观看失败');
        }
    }

    /**
     * 视频播放接口
     *
     * @param int $id 视频ID
     * @return Response
     */
    public function play(int $id): Response
    {
        try {
            $video = VideoModel::find($id);

            if (!$video) {
                return $this->responseService->error('视频不存在', 404);
            }

            if ($video->status !== 'published') {
                return $this->responseService->error('视频未发布', 403);
            }

            // 优先检查是否有采集的播放地址
            $playUrls = $this->getVideoPlayUrls($id);
            if (!empty($playUrls)) {
                // 优先选择m3u8格式的播放地址
                $m3u8Url = null;
                foreach ($playUrls as $url) {
                    if ($url['source_name'] === 'mtm3u8' || strpos($url['play_url'], '.m3u8') !== false) {
                        $m3u8Url = $url;
                        break;
                    }
                }

                if ($m3u8Url) {
                    // 重定向到M3U8播放地址
                    return redirect($m3u8Url['play_url'], 302);
                }

                // 如果没有M3U8，使用第一个可用的播放地址
                if (!empty($playUrls[0]['play_url'])) {
                    return redirect($playUrls[0]['play_url'], 302);
                }
            }

            // 如果没有采集的播放地址，尝试本地文件
            if (empty($video->file_path)) {
                return $this->responseService->error('视频文件不存在，且没有可用的播放地址', 404);
            }

            // 构建视频文件路径 - 尝试多个可能的位置
            $normalizedPath = str_replace('\\', '/', $video->file_path);
            $apiRoot = dirname(dirname(root_path())); // 回到项目根目录
            $possiblePaths = [
                root_path() . 'public' . $video->file_path,
                root_path() . 'public' . $normalizedPath,
                $apiRoot . '/packages/public' . $video->file_path,
                $apiRoot . '/packages/public' . $normalizedPath,
                $apiRoot . '/packages/public/storage/videos' . $video->file_path,
                $apiRoot . '/packages/public/storage/videos' . $normalizedPath,
                $apiRoot . '/deployment/production/video-platform-production/packages/api/public/storage/videos' . $video->file_path,
                $apiRoot . '/deployment/production/video-platform-production/packages/api/public/storage/videos' . $normalizedPath,
            ];

            $videoPath = null;
            foreach ($possiblePaths as $path) {
                Log::info('尝试路径: ' . $path . ' - 存在: ' . (file_exists($path) ? '是' : '否'));
                if (file_exists($path)) {
                    $videoPath = $path;
                    break;
                }
            }

            if (!$videoPath) {
                // 如果都找不到，记录调试信息
                Log::warning('视频文件未找到', [
                    'video_id' => $id,
                    'file_path' => $video->file_path,
                    'root_path' => root_path(),
                    'tried_paths' => $possiblePaths
                ]);
            }

            if (!$videoPath) {
                return $this->responseService->error('视频文件不存在', 404);
            }

            // 构建相对于public目录的路径
            $relativePath = str_replace(root_path() . 'public', '', $videoPath);
            $relativePath = str_replace('\\', '/', $relativePath);

            // 重定向到静态文件路由
            return redirect($relativePath, 302);

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '播放视频失败');
        }
    }

    /**
     * HLS播放接口
     *
     * @param int $id 视频ID
     * @return Response
     */
    public function hls(int $id): Response
    {
        try {
            $video = VideoModel::find($id);

            if (!$video) {
                return $this->responseService->error('视频不存在', 404);
            }

            if ($video->status !== 'published') {
                return $this->responseService->error('视频未发布', 403);
            }

            // 检查是否有HLS文件
            if (empty($video->hls_path)) {
                return $this->responseService->error('HLS文件不存在', 404);
            }

            // 构建HLS播放列表路径 - 尝试多个可能的位置
            $possibleHlsPaths = [
                root_path() . 'packages/public' . $video->hls_path,
                root_path() . 'packages/public/storage/videos' . $video->hls_path,
                root_path() . 'deployment/production/video-platform-production/packages/api/public/storage/videos' . $video->hls_path,
                // 处理Windows路径分隔符
                root_path() . 'packages/public' . str_replace('\\', '/', $video->hls_path),
                root_path() . 'packages/public/storage/videos' . str_replace('\\', '/', $video->hls_path),
            ];

            $hlsPath = null;
            foreach ($possibleHlsPaths as $path) {
                if (file_exists($path)) {
                    $hlsPath = $path;
                    break;
                }
            }

            if (!$hlsPath) {
                return $this->responseService->error('HLS播放列表不存在', 404);
            }

            // 返回HLS播放列表
            return response()->file($hlsPath, 200, [], 'application/vnd.apple.mpegurl');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, 'HLS播放失败');
        }
    }

    /**
     * 视频流接口
     *
     * @param int $id 视频ID
     * @return Response
     */
    public function stream(int $id): Response
    {
        try {
            $video = VideoModel::find($id);

            if (!$video) {
                return $this->responseService->error('视频不存在', 404);
            }

            if ($video->status !== 'published') {
                return $this->responseService->error('视频未发布', 403);
            }

            // 构建视频文件路径 - 尝试多个可能的位置
            $possiblePaths = [
                root_path() . 'packages/public' . $video->file_path,
                root_path() . 'packages/public/storage/videos' . $video->file_path,
                root_path() . 'deployment/production/video-platform-production/packages/api/public/storage/videos' . $video->file_path,
                // 处理Windows路径分隔符
                root_path() . 'packages/public' . str_replace('\\', '/', $video->file_path),
                root_path() . 'packages/public/storage/videos' . str_replace('\\', '/', $video->file_path),
            ];

            $videoPath = null;
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    $videoPath = $path;
                    break;
                }
            }

            if (!$videoPath) {
                return $this->responseService->error('视频文件不存在', 404);
            }

            // 支持Range请求的流式播放
            $fileSize = filesize($videoPath);
            $start = 0;
            $end = $fileSize - 1;

            $headers = [
                'Content-Type' => 'video/mp4',
                'Accept-Ranges' => 'bytes',
                'Content-Length' => $fileSize,
            ];

            // 处理Range请求
            if (isset($_SERVER['HTTP_RANGE'])) {
                if (preg_match('/bytes=(\d+)-(\d*)/', $_SERVER['HTTP_RANGE'], $matches)) {
                    $start = intval($matches[1]);
                    if (!empty($matches[2])) {
                        $end = intval($matches[2]);
                    }
                }

                $headers['Content-Length'] = ($end - $start + 1);
                $headers['Content-Range'] = "bytes $start-$end/$fileSize";

                $response = response('', 206, $headers);
            } else {
                $response = response('', 200, $headers);
            }

            // 读取文件内容
            $handle = fopen($videoPath, 'rb');
            fseek($handle, $start);
            $content = fread($handle, $end - $start + 1);
            fclose($handle);

            return $response->data($content);

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '流式播放失败');
        }
    }

    /**
     * 视频信息接口
     *
     * @param int $id 视频ID
     * @return Response
     */
    public function info(int $id): Response
    {
        try {
            $video = VideoModel::find($id);

            if (!$video) {
                return $this->responseService->error('视频不存在', 404);
            }

            $info = [
                'id' => $video->id,
                'title' => $video->title,
                'duration' => $video->duration,
                'file_size' => $video->file_size,
                'resolution' => $video->resolution,
                'format' => pathinfo($video->file_path, PATHINFO_EXTENSION),
                'has_hls' => !empty($video->hls_path),
                'status' => $video->status
            ];

            return $this->responseService->success($info, '获取视频信息成功');

        } catch (\Exception $e) {
            return $this->responseService->handleException($e, '获取视频信息失败');
        }
    }
}
