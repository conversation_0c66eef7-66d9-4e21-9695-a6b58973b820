# ==========================================
# 🔧 企业级视频平台 - 环境配置模板
# ==========================================
# 复制此文件为 .env 并根据实际环境修改配置

# ==========================================
# 🌍 环境配置
# ==========================================
NODE_ENV=development
APP_ENV=development
APP_DEBUG=true
APP_NAME=51吃瓜网
APP_VERSION=2.0.0

# ==========================================
# 🖥️ 服务器配置
# ==========================================
SERVER_IP=localhost
SERVER_DOMAIN=localhost

# 端口配置
API_PORT=3000
ADMIN_PORT=3001
FRONTEND_PORT=3002
MYSQL_PORT=3306
REDIS_PORT=6379

# ==========================================
# 🗄️ 数据库配置
# ==========================================
DB_TYPE=mysql
DB_HOST=mysql
DB_PORT=3306
DB_NAME=shipin_db
DB_USER=shipin_user
DB_PASS=shipin123456
DB_CHARSET=utf8mb4
DB_PREFIX=zs_

# ==========================================
# 🔴 Redis配置
# ==========================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SELECT=0
REDIS_TIMEOUT=0
REDIS_EXPIRE=3600
REDIS_PERSISTENT=false
REDIS_PREFIX=shipin_

# ==========================================
# 🔐 安全配置 (重要：生产环境必须修改)
# ==========================================
# JWT密钥 - 生产环境必须使用强密钥
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRE=7200
JWT_REFRESH_EXPIRE=604800

# API密钥 - 生产环境必须重新生成
API_KEY_ADMIN=your-admin-api-key-change-in-production
API_KEY_USER=your-user-api-key-change-in-production
API_KEY_MOBILE=your-mobile-api-key-change-in-production
API_KEY_THIRD_PARTY=your-third-party-api-key-change-in-production

# 加密密钥
ENCRYPTION_KEY=your-encryption-key-32-chars-long
HASH_SALT=your-hash-salt-change-in-production

# ==========================================
# 📁 文件存储配置
# ==========================================
UPLOAD_PATH=/app/storage/uploads
VIDEO_PATH=/app/storage/videos
TEMP_PATH=/app/storage/temp
LOG_PATH=/app/storage/logs

# 上传限制
UPLOAD_MAX_SIZE=500
UPLOAD_CHUNK_SIZE=5
UPLOAD_ALLOWED_TYPES=mp4,avi,mov,wmv,flv,webm,mkv,jpg,jpeg,png,gif,webp

# ==========================================
# 🎬 视频处理配置
# ==========================================
FFMPEG_PATH=/usr/bin/ffmpeg
FFPROBE_PATH=/usr/bin/ffprobe
VIDEO_ENCRYPTION_ENABLED=false
HLS_SEGMENT_DURATION=10
HLS_PLAYLIST_SIZE=5

# ==========================================
# 📧 邮件配置
# ==========================================
MAIL_DRIVER=smtp
MAIL_HOST=
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=视频平台

# ==========================================
# 🔗 CORS配置
# ==========================================
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://localhost:3002
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-API-Key

# ==========================================
# 📊 监控配置
# ==========================================
MONITOR_ENABLED=true
MONITOR_INTERVAL=60
PERFORMANCE_LOG=true
ERROR_REPORTING=true

# ==========================================
# 🕷️ 采集配置
# ==========================================
COLLECT_ENABLED=true
COLLECT_CONCURRENT_LIMIT=3
COLLECT_TIMEOUT=300
COLLECT_USER_AGENT=Mozilla/5.0 (compatible; VideoCollector/1.0)

# ==========================================
# 🎯 功能开关
# ==========================================
FEATURE_LIVE=true
FEATURE_VIP=true
FEATURE_COMMENT=true
FEATURE_SHARE=true
FEATURE_COLLECT=true

# ==========================================
# 🚀 性能配置
# ==========================================
CACHE_ENABLED=true
CACHE_EXPIRE_TIME=3600
OPCACHE_ENABLE=true
GZIP_ENABLE=true
STATIC_CACHE_EXPIRE=31536000

# ==========================================
# 🔧 开发工具配置
# ==========================================
VITE_ENABLE_DEVTOOLS=false
VITE_SOURCE_MAP=false
VITE_CONSOLE_LOG=false
VITE_PERFORMANCE_MONITOR=false

# ==========================================
# 🌐 前端配置
# ==========================================
VITE_API_BASE_URL=http://localhost:3000
VITE_ADMIN_BASE_URL=http://localhost:3001
VITE_FRONTEND_BASE_URL=http://localhost:3002
VITE_WS_URL=ws://localhost:3000/ws

# 前端API密钥（注意：这些将暴露给客户端，仅用于识别来源）
VITE_API_KEY_USER=public-user-key-for-frontend
VITE_API_KEY_MOBILE=public-mobile-key-for-frontend

# ==========================================
# 🎨 UI配置
# ==========================================
VITE_APP_TITLE=51吃瓜网
VITE_APP_DESCRIPTION=专业的视频分享平台
VITE_THEME_COLOR=#667eea

# ==========================================
# 📈 第三方服务配置
# ==========================================
# CDN配置
CDN_URL=
STATIC_URL=

# 对象存储配置
OSS_ACCESS_KEY=
OSS_SECRET_KEY=
OSS_BUCKET=
OSS_ENDPOINT=

# 分析服务
ANALYTICS_ID=
SENTRY_DSN=

# ==========================================
# 🐳 Docker配置
# ==========================================
COMPOSE_PROJECT_NAME=zhengshiban
DOCKER_REGISTRY=
DOCKER_TAG=latest

# ==========================================
# 🔒 生产环境安全提醒
# ==========================================
# 生产环境部署前请确保：
# 1. 修改所有默认密码和密钥
# 2. 启用HTTPS
# 3. 配置防火墙
# 4. 启用日志监控
# 5. 定期备份数据库
# 6. 更新依赖包到最新版本
