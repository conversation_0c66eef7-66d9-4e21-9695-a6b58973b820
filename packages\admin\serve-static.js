const express = require('express');
const path = require('path');
const app = express();
const port = 3001;

// 启用CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key');

  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 提供静态文件
app.use(express.static(path.join(__dirname, 'dist')));

// 健康检查
app.get('/health', (req, res) => {
  res.status(200).send('healthy');
});

// SPA路由支持 - 所有其他路由都返回index.html
app.use((req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, '0.0.0.0', () => {
  console.log(`✅ 管理后台静态文件服务器启动成功`);
  console.log(`🌐 访问地址: http://localhost:${port}`);
  console.log(`📁 静态文件目录: ${path.join(__dirname, 'dist')}`);
});
