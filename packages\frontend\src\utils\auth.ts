/**
 * JWT认证工具类
 * 
 * 提供安全的用户认证管理，替代硬编码的API密钥
 * 
 * <AUTHOR> Video Platform Team
 * @version 2.0.0
 */

import { config } from '@/config/env'

// Token存储键名
const ACCESS_TOKEN_KEY = 'access_token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const USER_INFO_KEY = 'user_info'

// Token接口定义
interface TokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: string
}

interface UserInfo {
  id: number
  username: string
  email: string
  avatar?: string
  role: string
  permissions: string[]
}

// JWT认证类
class AuthService {
  private accessToken: string | null = null
  private refreshToken: string | null = null
  private userInfo: UserInfo | null = null
  private refreshTimer: number | null = null

  constructor() {
    this.loadTokensFromStorage()
    this.setupAutoRefresh()
  }

  /**
   * 从本地存储加载token
   */
  private loadTokensFromStorage(): void {
    this.accessToken = localStorage.getItem(ACCESS_TOKEN_KEY)
    this.refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY)
    
    const userInfoStr = localStorage.getItem(USER_INFO_KEY)
    if (userInfoStr) {
      try {
        this.userInfo = JSON.parse(userInfoStr)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        this.clearUserInfo()
      }
    }
  }

  /**
   * 保存token到本地存储
   */
  private saveTokensToStorage(tokens: TokenResponse): void {
    this.accessToken = tokens.access_token
    this.refreshToken = tokens.refresh_token
    
    localStorage.setItem(ACCESS_TOKEN_KEY, tokens.access_token)
    localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refresh_token)
    
    // 设置token过期时间
    const expiresAt = Date.now() + (tokens.expires_in * 1000)
    localStorage.setItem('token_expires_at', expiresAt.toString())
  }

  /**
   * 保存用户信息
   */
  private saveUserInfo(userInfo: UserInfo): void {
    this.userInfo = userInfo
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
  }

  /**
   * 清除所有认证信息
   */
  private clearAuthData(): void {
    this.accessToken = null
    this.refreshToken = null
    this.userInfo = null
    
    localStorage.removeItem(ACCESS_TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
    localStorage.removeItem(USER_INFO_KEY)
    localStorage.removeItem('token_expires_at')
    
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  /**
   * 清除用户信息
   */
  private clearUserInfo(): void {
    this.userInfo = null
    localStorage.removeItem(USER_INFO_KEY)
  }

  /**
   * 设置自动刷新token
   */
  private setupAutoRefresh(): void {
    if (!this.accessToken) return

    const expiresAt = localStorage.getItem('token_expires_at')
    if (!expiresAt) return

    const expiresTime = parseInt(expiresAt)
    const now = Date.now()
    const timeUntilExpiry = expiresTime - now
    
    // 在token过期前5分钟刷新
    const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 0)
    
    if (refreshTime > 0) {
      this.refreshTimer = window.setTimeout(() => {
        this.refreshAccessToken()
      }, refreshTime)
    }
  }

  /**
   * 用户登录
   */
  async login(credentials: { username: string; password: string }): Promise<UserInfo> {
    try {
      const response = await fetch(`${config.apiBaseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials)
      })

      if (!response.ok) {
        throw new Error(`登录失败: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.code !== 200) {
        throw new Error(data.message || '登录失败')
      }

      // 保存token和用户信息
      this.saveTokensToStorage(data.data.tokens)
      this.saveUserInfo(data.data.user)
      this.setupAutoRefresh()

      return data.data.user
    } catch (error) {
      console.error('登录错误:', error)
      throw error
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      if (this.accessToken) {
        await fetch(`${config.apiBaseUrl}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          }
        })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      this.clearAuthData()
    }
  }

  /**
   * 刷新访问token
   */
  async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) {
      this.clearAuthData()
      return false
    }

    try {
      const response = await fetch(`${config.apiBaseUrl}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: this.refreshToken
        })
      })

      if (!response.ok) {
        throw new Error('刷新token失败')
      }

      const data = await response.json()
      
      if (data.code !== 200) {
        throw new Error(data.message || '刷新token失败')
      }

      this.saveTokensToStorage(data.data)
      this.setupAutoRefresh()
      
      return true
    } catch (error) {
      console.error('刷新token错误:', error)
      this.clearAuthData()
      return false
    }
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    return !!this.accessToken && !!this.userInfo
  }

  /**
   * 获取访问token
   */
  getAccessToken(): string | null {
    return this.accessToken
  }

  /**
   * 获取用户信息
   */
  getUserInfo(): UserInfo | null {
    return this.userInfo
  }

  /**
   * 检查用户权限
   */
  hasPermission(permission: string): boolean {
    if (!this.userInfo) return false
    return this.userInfo.permissions.includes(permission) || this.userInfo.permissions.includes('*')
  }

  /**
   * 检查用户角色
   */
  hasRole(role: string): boolean {
    if (!this.userInfo) return false
    return this.userInfo.role === role || this.userInfo.role === 'admin'
  }

  /**
   * 获取认证头
   */
  getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    if (this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`
    }

    return headers
  }
}

// 创建全局认证服务实例
export const authService = new AuthService()

// 导出类型
export type { TokenResponse, UserInfo }
export default AuthService
