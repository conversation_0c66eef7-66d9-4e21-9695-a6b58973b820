<?php

namespace app\service;

use think\facade\Db;
use think\facade\Config;

/**
 * 数据库管理服务
 * 
 * 为分布式架构提供统一的数据库访问接口
 * 支持多数据库连接、读写分离、分库分表等功能
 */
class DatabaseManagerService
{
    /**
     * 默认数据库连接
     */
    private string $defaultConnection = 'mysql';

    /**
     * 视频相关数据库连接
     */
    private string $videoConnection = 'mysql';

    /**
     * 用户相关数据库连接
     */
    private string $userConnection = 'mysql';

    /**
     * 日志相关数据库连接
     */
    private string $logConnection = 'mysql';

    public function __construct()
    {
        // 从配置中读取不同服务的数据库连接
        $this->videoConnection = Config::get('database.video_connection', 'mysql');
        $this->userConnection = Config::get('database.user_connection', 'mysql');
        $this->logConnection = Config::get('database.log_connection', 'mysql');
    }

    /**
     * 获取表操作对象
     * 
     * @param string $table 表名
     * @param string $type 数据类型 (video|user|log|default)
     * @return \think\db\Query
     */
    public function table(string $table, string $type = 'default')
    {
        $connection = $this->getConnectionByType($type, $table);
        return Db::connect($connection)->table($table);
    }

    /**
     * 获取写入表操作对象（用于插入、更新、删除）
     * 
     * @param string $table 表名
     * @param string $type 数据类型
     * @return \think\db\Query
     */
    public function writeTable(string $table, string $type = 'default')
    {
        // 在读写分离环境中，写操作使用主库
        $connection = $this->getWriteConnectionByType($type, $table);
        return Db::connect($connection)->table($table);
    }

    /**
     * 获取读取表操作对象（用于查询）
     * 
     * @param string $table 表名
     * @param string $type 数据类型
     * @return \think\db\Query
     */
    public function readTable(string $table, string $type = 'default')
    {
        // 在读写分离环境中，读操作可以使用从库
        $connection = $this->getReadConnectionByType($type, $table);
        return Db::connect($connection)->table($table);
    }

    /**
     * 根据数据类型获取数据库连接
     * 
     * @param string $type 数据类型
     * @param string $table 表名
     * @return string
     */
    private function getConnectionByType(string $type, string $table): string
    {
        // 根据表名自动判断数据类型
        if ($type === 'default') {
            $type = $this->detectTypeByTable($table);
        }

        switch ($type) {
            case 'video':
                return $this->videoConnection;
            case 'user':
                return $this->userConnection;
            case 'log':
                return $this->logConnection;
            default:
                return $this->defaultConnection;
        }
    }

    /**
     * 获取写连接
     */
    private function getWriteConnectionByType(string $type, string $table): string
    {
        // 目前返回相同连接，未来可以配置主从分离
        return $this->getConnectionByType($type, $table);
    }

    /**
     * 获取读连接
     */
    private function getReadConnectionByType(string $type, string $table): string
    {
        // 目前返回相同连接，未来可以配置主从分离
        return $this->getConnectionByType($type, $table);
    }

    /**
     * 根据表名自动检测数据类型
     * 
     * @param string $table 表名
     * @return string
     */
    private function detectTypeByTable(string $table): string
    {
        // 视频相关表
        if (strpos($table, 'video') !== false || 
            strpos($table, 'category') !== false ||
            strpos($table, 'encryption') !== false) {
            return 'video';
        }

        // 用户相关表
        if (strpos($table, 'user') !== false || 
            strpos($table, 'admin') !== false ||
            strpos($table, 'auth') !== false) {
            return 'user';
        }

        // 日志相关表
        if (strpos($table, 'log') !== false || 
            strpos($table, 'audit') !== false ||
            strpos($table, 'access') !== false) {
            return 'log';
        }

        return 'default';
    }

    /**
     * 开始事务
     * 
     * @param string $type 数据类型
     * @return void
     */
    public function startTrans(string $type = 'default'): void
    {
        $connection = $this->getConnectionByType($type, '');
        Db::connect($connection)->startTrans();
    }

    /**
     * 提交事务
     * 
     * @param string $type 数据类型
     * @return void
     */
    public function commit(string $type = 'default'): void
    {
        $connection = $this->getConnectionByType($type, '');
        Db::connect($connection)->commit();
    }

    /**
     * 回滚事务
     * 
     * @param string $type 数据类型
     * @return void
     */
    public function rollback(string $type = 'default'): void
    {
        $connection = $this->getConnectionByType($type, '');
        Db::connect($connection)->rollback();
    }

    /**
     * 执行原生SQL
     * 
     * @param string $sql SQL语句
     * @param array $bind 绑定参数
     * @param string $type 数据类型
     * @return mixed
     */
    public function query(string $sql, array $bind = [], string $type = 'default')
    {
        $connection = $this->getConnectionByType($type, '');
        return Db::connect($connection)->query($sql, $bind);
    }

    /**
     * 执行原生SQL（写操作）
     * 
     * @param string $sql SQL语句
     * @param array $bind 绑定参数
     * @param string $type 数据类型
     * @return mixed
     */
    public function execute(string $sql, array $bind = [], string $type = 'default')
    {
        $connection = $this->getWriteConnectionByType($type, '');
        return Db::connect($connection)->execute($sql, $bind);
    }
}
