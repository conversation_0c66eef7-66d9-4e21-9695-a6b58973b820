# ==========================================
# 🔧 开发环境 Docker Compose 配置
# ==========================================
# 专为开发环境优化的容器配置
# 包含热重载、调试工具、开发数据库等

version: '3.8'

services:
  # ==========================================
  # 🗄️ MySQL 数据库服务
  # ==========================================
  mysql:
    image: mysql:8.0
    container_name: shipin_mysql_dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: ${DB_NAME:-shipin_db}
      MYSQL_USER: ${DB_USER:-shipin_user}
      MYSQL_PASSWORD: ${DB_PASS:-shipin123456}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./packages/api/database/init:/docker-entrypoint-initdb.d
      - ./config/mysql/dev.cnf:/etc/mysql/conf.d/custom.cnf
    networks:
      - shipin_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # ==========================================
  # 🔴 Redis 缓存服务
  # ==========================================
  redis:
    image: redis:7-alpine
    container_name: shipin_redis_dev
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_dev_data:/data
      - ./config/redis/dev.conf:/usr/local/etc/redis/redis.conf
    networks:
      - shipin_network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # 🚀 API 后端服务
  # ==========================================
  api:
    build:
      context: ./packages/api
      dockerfile: Dockerfile.dev
      target: development
    container_name: shipin_api_dev
    restart: unless-stopped
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${DB_NAME:-shipin_db}
      - DB_USER=${DB_USER:-shipin_user}
      - DB_PASS=${DB_PASS:-shipin123456}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - API_KEY_ADMIN=${API_KEY_ADMIN:-dev-admin-key}
      - API_KEY_USER=${API_KEY_USER:-dev-user-key}
    ports:
      - "${API_PORT:-3000}:3000"
    volumes:
      - ./packages/api:/var/www/html
      - ./storage/logs:/var/www/html/runtime/logs
      - ./storage/uploads:/var/www/html/public/uploads
      - ./storage/videos:/var/www/html/public/videos
    networks:
      - shipin_network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # 🎨 前端服务
  # ==========================================
  frontend:
    build:
      context: ./packages/frontend
      dockerfile: Dockerfile.dev
      target: development
    container_name: shipin_frontend_dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - VITE_APP_ENV=development
      - VITE_API_BASE_URL=http://localhost:${API_PORT:-3000}
      - VITE_API_KEY_USER=${VITE_API_KEY_USER:-dev-user-key}
      - VITE_SOURCE_MAP=true
      - VITE_CONSOLE_LOG=true
      - VITE_ENABLE_DEVTOOLS=true
      - VITE_PERFORMANCE_MONITOR=true
    ports:
      - "${FRONTEND_PORT:-3002}:3002"
    volumes:
      - ./packages/frontend:/app
      - /app/node_modules
      - /app/dist
    networks:
      - shipin_network
    depends_on:
      - api
    command: npm run dev

  # ==========================================
  # ⚙️ 管理后台服务
  # ==========================================
  admin:
    build:
      context: ./packages/admin
      dockerfile: Dockerfile.dev
      target: development
    container_name: shipin_admin_dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - VITE_APP_ENV=development
      - VITE_API_BASE_URL=http://localhost:${API_PORT:-3000}
      - VITE_API_KEY_ADMIN=${VITE_API_KEY_ADMIN:-dev-admin-key}
      - VITE_SOURCE_MAP=true
      - VITE_CONSOLE_LOG=true
      - VITE_ENABLE_DEVTOOLS=true
    ports:
      - "${ADMIN_PORT:-3001}:3001"
    volumes:
      - ./packages/admin:/app
      - /app/node_modules
      - /app/dist
    networks:
      - shipin_network
    depends_on:
      - api
    command: npm run dev

  # ==========================================
  # 📊 开发工具服务
  # ==========================================
  
  # phpMyAdmin 数据库管理
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: shipin_phpmyadmin_dev
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${DB_USER:-shipin_user}
      PMA_PASSWORD: ${DB_PASS:-shipin123456}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root123456}
    ports:
      - "8080:80"
    networks:
      - shipin_network
    depends_on:
      - mysql

  # Redis Commander 缓存管理
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: shipin_redis_commander_dev
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - shipin_network
    depends_on:
      - redis

  # Mailhog 邮件测试
  mailhog:
    image: mailhog/mailhog:latest
    container_name: shipin_mailhog_dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - shipin_network

# ==========================================
# 📦 数据卷配置
# ==========================================
volumes:
  mysql_dev_data:
    driver: local
    name: shipin_mysql_dev_data
  redis_dev_data:
    driver: local
    name: shipin_redis_dev_data

# ==========================================
# 🌐 网络配置
# ==========================================
networks:
  shipin_network:
    driver: bridge
    name: shipin_dev_network
    ipam:
      config:
        - subnet: **********/16
