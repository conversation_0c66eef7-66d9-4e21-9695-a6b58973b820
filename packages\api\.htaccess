# ThinkPHP URL重写规则
<IfModule mod_rewrite.c>
    Options +FollowSymlinks -Multiviews
    RewriteEngine On

    # CORS支持 - 为HLS和静态文件添加CORS头
    <IfModule mod_headers.c>
        # 为HLS文件添加CORS头
        <FilesMatch "\.(m3u8|ts)$">
            Header always set Access-Control-Allow-Origin "*"
            Header always set Access-Control-Allow-Methods "GET, OPTIONS"
            Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
        </FilesMatch>

        # 为视频文件添加CORS头
        <FilesMatch "\.(mp4|webm|ogg)$">
            Header always set Access-Control-Allow-Origin "*"
            Header always set Access-Control-Allow-Methods "GET, OPTIONS"
            Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
        </FilesMatch>

        # 为图片文件添加CORS头
        <FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
            Header always set Access-Control-Allow-Origin "*"
            Header always set Access-Control-Allow-Methods "GET, OPTIONS"
            Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
        </FilesMatch>
    </IfModule>

    # 处理OPTIONS预检请求
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # 静态文件直接访问
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^(.*)$ - [L]

    # API路由重写
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [L,E=PATH_INFO:$1]
</IfModule>