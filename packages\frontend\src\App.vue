<template>
  <div id="app">
    <!-- 主内容区域 -->
    <main class="main-content" :class="{
      'no-bottom-nav': isHomePage,
      'video-player-page': isVideoPlayerPage
    }">
      <router-view />
    </main>

    <!-- 底部导航 - 只在非首页和非视频播放页显示 -->
    <BottomNavigation v-if="!isHomePage && !isVideoPlayerPage" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import BottomNavigation from '@/components/BottomNavigation.vue'

const route = useRoute()
const userStore = useUserStore()

// 判断是否为首页
const isHomePage = computed(() => {
  return route.path === '/'
})

// 判断是否为视频播放页面
const isVideoPlayerPage = computed(() => {
  return route.path.startsWith('/video/')
})

onMounted(async () => {
  console.log('51吃瓜网 - 前端应用启动成功')

  // 初始化用户认证状态
  try {
    await userStore.initializeAuth()
  } catch (error) {
    console.warn('初始化用户认证状态失败:', error)
  }
})
</script>

<style>
/* 导入设计系统 */
@import '@/styles/design-system.css';
@import '@/styles/mobile.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  background: var(--bg-primary) !important;
  color: var(--text-primary);
  font-family: var(--font-sans);
  overflow-x: hidden;
  /* 移动端优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

#app {
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding-top: 50px; /* 为顶部导航留出空间 */
  padding-bottom: 85px; /* 为更大气的底部导航留出空间 */
}

/* 首页不需要底部导航的空间 */
.main-content.no-bottom-nav {
  padding-top: 0; /* 首页也不需要顶部导航空间 */
  padding-bottom: 0;
}

/* 视频播放页面不需要任何padding */
.main-content.video-player-page {
  padding-top: 0;
  padding-bottom: 0;
  overflow-y: visible;
}

/* 确保路由视图占满可用空间 */
.router-view {
  min-height: calc(100vh - 135px); /* 减去顶部和底部导航的高度 */
}

/* 首页的路由视图占满全屏 */
.no-bottom-nav .router-view {
  min-height: 100vh;
}
</style>