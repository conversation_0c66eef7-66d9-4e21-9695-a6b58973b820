<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Log;
use think\facade\Config;
use think\facade\Db;
use think\Queue;
use app\service\VideoEncryptionService;

/**
 * 视频处理服务
 * 
 * 提供视频切片、转码、加密等功能
 * - HLS切片处理
 * - 多码率转码
 * - 视频加密
 * - 云存储上传
 */
class VideoProcessingService
{
    /**
     * FFmpeg可执行文件路径
     */
    private string $ffmpegPath;

    /**
     * FFprobe可执行文件路径
     */
    private string $ffprobePath;

    /**
     * 临时处理目录
     */
    private string $tempDir;

    /**
     * 输出目录
     */
    private string $outputDir;

    /**
     * 视频加密服务
     */
    private VideoEncryptionService $encryptionService;

    /**
     * 支持的视频格式
     */
    private array $supportedFormats = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];

    /**
     * 转码质量配置（从配置文件加载）
     */
    private array $qualitySettings;

    public function __construct()
    {
        // 加载转码质量配置
        $this->qualitySettings = Config::get('video.quality_settings', []);

        // 在Windows环境下设置PATH环境变量
        if (PHP_OS_FAMILY === 'Windows') {
            $machinePath = getenv('PATH') ?: '';
            $userPath = '';
            exec('echo %PATH%', $pathOutput);
            if (!empty($pathOutput)) {
                $userPath = implode(';', $pathOutput);
            }
            $newPath = $machinePath . ';' . $userPath;
            putenv("PATH={$newPath}");
        }

        // 从配置文件中获取FFmpeg可执行文件路径，如果没有配置则使用默认路径
        if (PHP_OS_FAMILY === 'Windows') {
            // Windows环境下尝试找到FFmpeg的完整路径
            $possiblePaths = [
                'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\\ffmpeg-7.1.1-full_build\\bin\\ffmpeg.exe',
                'ffmpeg.exe',
                'ffmpeg'
            ];

            $this->ffmpegPath = 'ffmpeg'; // 默认值
            foreach ($possiblePaths as $path) {
                $this->safeLog('测试FFmpeg路径: ' . $path . ', 存在: ' . (file_exists($path) ? '是' : '否'));
                if (file_exists($path) || $this->testFFmpegPath($path)) {
                    $this->ffmpegPath = $path;
                    $this->safeLog('FFmpeg路径检测成功: ' . $path);
                    break;
                }
            }
            $this->safeLog('最终FFmpeg路径: ' . $this->ffmpegPath);

            // 设置ffprobe路径 - 只替换文件名，不替换路径中的ffmpeg
            if (strpos($this->ffmpegPath, 'ffmpeg.exe') !== false) {
                $this->ffprobePath = str_replace('ffmpeg.exe', 'ffprobe.exe', $this->ffmpegPath);
            } else {
                // 如果路径中包含bin目录，直接构建ffprobe路径
                $pathParts = pathinfo($this->ffmpegPath);
                $this->ffprobePath = $pathParts['dirname'] . DIRECTORY_SEPARATOR . 'ffprobe.exe';
            }
        } else {
            $this->ffmpegPath = Config::get('video.ffmpeg_path', '/usr/bin/ffmpeg');
            $this->ffprobePath = Config::get('video.ffprobe_path', '/usr/bin/ffprobe');
        }

        // 设置临时处理目录，用于存放处理过程中的临时文件
        $rootPath = $this->getRootPath();
        $this->tempDir = $rootPath . 'runtime' . DIRECTORY_SEPARATOR . 'video_temp';

        // 设置输出目录，用于存放处理完成的视频文件
        $this->outputDir = $rootPath . 'public' . DIRECTORY_SEPARATOR . 'storage' . DIRECTORY_SEPARATOR . 'videos';

        // 初始化视频加密服务（仅在框架环境中）
        try {
            if (function_exists('app')) {
                $this->encryptionService = new VideoEncryptionService();
            }
        } catch (\Exception $e) {
            $this->safeLog('跳过加密服务初始化: ' . $e->getMessage());
        }

        // 确保必要的目录存在，如果不存在则创建
        $this->ensureDirectoryExists($this->tempDir);
        $this->ensureDirectoryExists($this->outputDir);
    }

    /**
     * 处理视频（主入口）
     * 
     * @param int $videoId 视频ID
     * @param string $inputPath 输入文件路径
     * @return array 处理结果
     */
    public function processVideo(int $videoId, string $inputPath): array
    {
        // 使用路径服务统一处理路径
        $pathService = new \app\service\VideoPathService();
        $inputPath = $pathService->toFullPath($inputPath);

        $this->safeLog('开始处理视频 ID: ' . $videoId . ', 路径: ' . $inputPath);

        try {
            // 第一步：验证输入文件的有效性
            $this->validateInputFile($inputPath, $videoId);

            // 第二步：获取视频的基本信息
            // 使用FFprobe提取视频的时长、分辨率、码率等元数据信息
            $videoInfo = $this->getVideoInfo($inputPath);

            // 第三步：为当前视频创建专用的处理目录
            // 每个视频都有独立的处理目录，避免文件冲突
            $processDir = $this->createProcessDirectory($videoId);

            // 第四步：生成视频缩略图
            // 从视频中提取一帧作为封面图片，通常取第1秒的画面
            $thumbnail = $this->generateThumbnail($inputPath, $processDir, $videoId);

            // 第五步：HLS切片处理（支持加密）
            // 将视频切分成多个小片段，生成m3u8播放列表，支持自适应码率播放
            // 如果启用了加密，会对每个切片进行AES-128加密
            $hlsResult = $this->processHLS($inputPath, $processDir, $videoId);

            // 第六步：多码率转码
            // 生成不同分辨率和码率的视频版本，适应不同网络环境和设备
            $transcodeResults = $this->processMultiQuality($inputPath, $processDir);

            // 7. 更新数据库
            $this->updateVideoRecord($videoId, [
                'duration' => $videoInfo['duration'],
                'width' => $videoInfo['width'],
                'height' => $videoInfo['height'],
                'file_size' => filesize($inputPath),
                'cover_image' => $thumbnail,
                'hls_url' => $hlsResult['playlist'],
                'status' => 'published',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $this->safeLog('视频处理完成 ID: ' . $videoId . ', 时长: ' . $videoInfo['duration']);

            return [
                'success' => true,
                'video_id' => $videoId,
                'duration' => $videoInfo['duration'],
                'width' => $videoInfo['width'],
                'height' => $videoInfo['height'],
                'hls_path' => $hlsResult['playlist'],
                'thumbnail' => $thumbnail
            ];

        } catch (\Exception $e) {
            Log::error('视频处理失败', [
                'video_id' => $videoId,
                'input_path' => $inputPath,
                'error' => $e->getMessage()
            ]);

            // 更新数据库状态（保持published状态，错误信息记录在processing_status中）
            $this->updateVideoRecord($videoId, [
                'status' => 'published', // 保持published状态，不设置为failed
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            throw $e;
        }
    }

    /**
     * HLS切片处理（支持加密）
     *
     * 将视频切分成HLS格式的小片段，支持AES-128加密
     * 如果启用加密，会对每个TS切片进行加密处理
     *
     * @param string $inputPath 输入文件路径
     * @param string $outputDir 输出目录
     * @param int $videoId 视频ID（用于生成加密密钥）
     * @return array 处理结果
     */
    public function processHLS(string $inputPath, string $outputDir, int $videoId = 0): array
    {
        // 检查是否启用视频加密
        $encryptionEnabled = Config::get('video.encryption.enabled', false);
        Log::info('HLS加密配置检查', [
            'encryption_enabled' => $encryptionEnabled,
            'video_id' => $videoId,
            'config_value' => Config::get('video.encryption.enabled', false),
            'env_value' => env('VIDEO_ENCRYPTION_ENABLED', 'not_set')
        ]);
        $keyInfo = null;

        if ($encryptionEnabled && $videoId > 0) {
            // 为视频生成加密密钥
            $keyInfo = $this->encryptionService->generateEncryptionKey($videoId);
            Log::info('为HLS切片生成加密密钥', [
                'video_id' => $videoId,
                'key_id' => $keyInfo['key_id']
            ]);
        }

        $playlistPath = $outputDir . DIRECTORY_SEPARATOR . 'playlist.m3u8';
        $segmentPattern = $outputDir . DIRECTORY_SEPARATOR . 'segment_%03d.ts';

        // 构建优化的HLS切片FFmpeg命令
        $command = sprintf(
            '%s -i "%s" -c:v libx264 -c:a aac -preset fast -crf 25 -tune fastdecode -threads 0 -f hls -hls_time 10 -hls_playlist_type vod -hls_segment_filename "%s" -movflags +faststart "%s"',
            $this->ffmpegPath,
            $inputPath,
            $segmentPattern,
            $playlistPath
        );

        Log::info('执行HLS切片命令', ['command' => $command]);

        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception('HLS切片处理失败: ' . implode("\n", $output));
        }

        // 获取生成的切片文件
        $segments = glob($outputDir . DIRECTORY_SEPARATOR . 'segment_*.ts');

        // 如果启用了加密，对切片文件进行加密处理
        if ($encryptionEnabled && $keyInfo) {
            $segments = $this->encryptHLSSegments($segments, $keyInfo, $outputDir);

            // 生成包含加密信息的播放列表
            $keyUrl = $this->generateKeyUrl($keyInfo['key_id']);
            $this->encryptionService->generateEncryptedPlaylist($playlistPath, $keyInfo, $keyUrl);
        }

        // 将HLS文件移动到public目录
        $publicHlsDir = root_path() . 'public' . DIRECTORY_SEPARATOR . 'hls' . DIRECTORY_SEPARATOR . 'video_' . $videoId;
        if (!is_dir($publicHlsDir)) {
            mkdir($publicHlsDir, 0755, true);
        }

        // 移动播放列表文件
        $publicPlaylistPath = $publicHlsDir . DIRECTORY_SEPARATOR . 'playlist.m3u8';
        if (file_exists($playlistPath)) {
            copy($playlistPath, $publicPlaylistPath);
        }

        // 移动切片文件
        $publicSegments = [];
        foreach ($segments as $segment) {
            $segmentName = basename($segment);
            $publicSegmentPath = $publicHlsDir . DIRECTORY_SEPARATOR . $segmentName;
            if (file_exists($segment)) {
                copy($segment, $publicSegmentPath);
                $publicSegments[] = $publicSegmentPath;
            }
        }

        return [
            'playlist' => $this->getRelativePath($publicPlaylistPath),
            'segments' => array_map([$this, 'getRelativePath'], $publicSegments),
            'segment_count' => count($publicSegments),
            'encrypted' => $encryptionEnabled && $keyInfo !== null,
            'key_info' => $keyInfo ? [
                'key_id' => $keyInfo['key_id'],
                'expires_at' => $keyInfo['expires_at']
            ] : null
        ];
    }

    /**
     * 多码率转码
     *
     * @param string $inputPath 输入文件路径
     * @param string $outputDir 输出目录
     * @return array 转码结果
     */
    public function processMultiQuality(string $inputPath, string $outputDir): array
    {
        $results = [];

        // 从数据库配置中获取启用的质量
        $enabledQualities = $this->getEnabledQualities();

        foreach ($enabledQualities as $quality) {
            if (!isset($this->qualitySettings[$quality])) {
                Log::warning("跳过未知的质量设置: {$quality}");
                continue;
            }

            $settings = $this->qualitySettings[$quality];
            try {
                $outputPath = $outputDir . DIRECTORY_SEPARATOR . "video_{$quality}.mp4";

                // 构建优化的FFmpeg命令
                $command = sprintf(
                    '%s -i "%s" -c:v libx264 -c:a aac -vf scale=%d:%d -b:v %s -b:a %s -preset %s -crf %d -tune %s -threads 0 -movflags +faststart "%s"',
                    $this->ffmpegPath,
                    $inputPath,
                    $settings['width'],
                    $settings['height'],
                    $settings['bitrate'],
                    $settings['audio_bitrate'],
                    $settings['preset'] ?? 'fast',
                    $settings['crf'] ?? 25,
                    $settings['tune'] ?? 'fastdecode',
                    $outputPath
                );

                Log::info("执行{$quality}转码命令", ['command' => $command]);

                $output = [];
                $returnCode = 0;
                exec($command . ' 2>&1', $output, $returnCode);

                if ($returnCode === 0 && file_exists($outputPath)) {
                    $results[$quality] = [
                        'path' => $this->getRelativePath($outputPath),
                        'size' => filesize($outputPath),
                        'bitrate' => $settings['bitrate'],
                        'resolution' => $settings['width'] . 'x' . $settings['height']
                    ];
                } else {
                    Log::warning("{$quality}转码失败", [
                        'output' => implode("\n", $output),
                        'return_code' => $returnCode
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("{$quality}转码异常", ['error' => $e->getMessage()]);
            }
        }

        return $results;
    }

    /**
     * 获取启用的转码质量
     *
     * @return array 启用的质量列表
     */
    private function getEnabledQualities(): array
    {
        try {
            // 从数据库配置中获取启用的质量
            $config = \think\facade\Db::table('system_configs')
                ->where('key', 'enabled_qualities')
                ->find();

            if ($config && $config['value']) {
                $qualities = json_decode($config['value'], true);
                if (is_array($qualities) && !empty($qualities)) {
                    Log::info('从数据库配置获取启用的质量', ['qualities' => $qualities]);
                    return $qualities;
                }
            }

            // 如果配置不存在或为空，使用默认质量
            $defaultQualities = ['720p'];
            Log::info('使用默认转码质量', ['qualities' => $defaultQualities]);
            return $defaultQualities;

        } catch (\Exception $e) {
            Log::error('获取启用质量配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // 出错时使用默认质量
            return ['720p'];
        }
    }

    /**
     * 生成缩略图
     *
     * @param string $inputPath 输入文件路径
     * @param string $outputDir 输出目录
     * @param string $videoId 视频ID（可选，用于生成唯一文件名）
     * @return string 缩略图路径
     */
    public function generateThumbnail(string $inputPath, string $outputDir, $videoId = null): string
    {
        // 确保输出目录存在
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }

        // 生成唯一的缩略图文件名
        $uniqueId = $videoId ?: uniqid('thumb_', true);
        $thumbnailPath = $outputDir . DIRECTORY_SEPARATOR . 'thumbnail_' . $uniqueId . '.jpg';

        $command = sprintf(
            '"%s" -i "%s" -ss 00:00:01 -vframes 1 -vf scale=320:240 -update 1 -y "%s"',
            $this->ffmpegPath,
            $inputPath,
            $thumbnailPath
        );

        $this->safeLog('生成缩略图命令: ' . $command);

        $output = [];
        $returnCode = 0;

        exec($command . ' 2>&1', $output, $returnCode);

        if ($returnCode !== 0 || !file_exists($thumbnailPath)) {
            throw new \Exception('缩略图生成失败: ' . implode("\n", $output));
        }

        // 将缩略图移动到public目录下的uploads/thumbnails
        $publicThumbnailDir = $this->getRootPath() . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'thumbnails';
        if (!is_dir($publicThumbnailDir)) {
            mkdir($publicThumbnailDir, 0755, true);
        }

        // 使用唯一的文件名
        $uniqueFileName = 'thumbnail_' . $uniqueId . '.jpg';
        $publicThumbnailPath = $publicThumbnailDir . DIRECTORY_SEPARATOR . $uniqueFileName;

        if (copy($thumbnailPath, $publicThumbnailPath)) {
            // 删除临时文件
            unlink($thumbnailPath);
            return $this->getRelativePath($publicThumbnailPath);
        } else {
            throw new \Exception('移动缩略图到public目录失败');
        }
    }

    /**
     * 测试FFmpeg路径是否有效
     */
    private function testFFmpegPath(string $path): bool
    {
        $output = [];
        $returnCode = 0;
        exec($path . ' -version 2>&1', $output, $returnCode);
        return $returnCode === 0;
    }

    /**
     * 安全的日志记录方法
     * 在Log facade不可用时使用echo输出
     */
    private function safeLog(string $message): void
    {
        try {
            if (class_exists('think\facade\Log')) {
                Log::info($message);
            } else {
                echo "[VideoProcessingService] " . $message . "\n";
            }
        } catch (\Exception $e) {
            echo "[VideoProcessingService] " . $message . "\n";
        }
    }

    /**
     * 获取根路径
     * 兼容独立运行和框架运行
     */
    private function getRootPath(): string
    {
        try {
            if (function_exists('app')) {
                return app()->getRootPath();
            }
        } catch (\Exception $e) {
            // 忽略错误，使用备用方案
        }

        // 备用方案：从当前文件路径推导
        return dirname(dirname(dirname(__DIR__))) . DIRECTORY_SEPARATOR;
    }

    /**
     * 获取视频信息
     * 
     * @param string $inputPath 输入文件路径
     * @return array 视频信息
     */
    public function getVideoInfo(string $inputPath): array
    {
        $command = sprintf(
            '"%s" -i "%s" -v quiet -print_format json -show_format -show_streams',
            $this->ffprobePath,
            $inputPath
        );

        $this->safeLog('FFprobe命令: ' . $command);

        // 在Windows环境下，优先使用exec而不是shell_exec
        if (PHP_OS_FAMILY === 'Windows') {
            $execOutput = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $execOutput, $returnCode);
            $output = implode("\n", $execOutput);

            if ($returnCode !== 0) {
                throw new \Exception('FFprobe执行失败，返回码: ' . $returnCode . '，输出: ' . $output);
            }
        } else {
            $output = shell_exec($command);
        }

        if (!$output) {
            throw new \Exception('FFprobe没有输出');
        }

        $info = json_decode($output, true);

        if (!$info) {
            throw new \Exception('无法解析视频信息JSON，输出: ' . substr($output, 0, 500));
        }

        $videoStream = null;
        foreach ($info['streams'] as $stream) {
            if ($stream['codec_type'] === 'video') {
                $videoStream = $stream;
                break;
            }
        }

        if (!$videoStream) {
            throw new \Exception('未找到视频流');
        }

        return [
            'duration' => (float) $info['format']['duration'],
            'width' => (int) $videoStream['width'],
            'height' => (int) $videoStream['height'],
            'bitrate' => (int) ($info['format']['bit_rate'] ?? 0),
            'format' => $info['format']['format_name'],
            'codec' => $videoStream['codec_name']
        ];
    }

    /**
     * 验证输入文件
     * 
     * @param string $inputPath 输入文件路径
     * @return void
     * @throws \Exception
     */
    private function validateInputFile(string $inputPath, int $videoId): void
    {
        if (!file_exists($inputPath)) {
            throw \app\exception\VideoProcessingException::fileNotFound($inputPath, 'validation', $videoId);
        }

        $extension = strtolower(pathinfo($inputPath, PATHINFO_EXTENSION));
        if (!in_array($extension, $this->supportedFormats)) {
            throw \app\exception\VideoProcessingException::invalidFormat($extension, 'validation', $videoId);
        }

        $fileSize = filesize($inputPath);
        if ($fileSize === 0) {
            throw new \app\exception\VideoProcessingException('输入文件为空', 'validation', $videoId, 'file_not_found');
        }

        // 检查文件大小限制
        $maxSize = config('video.file_limits.max_file_size', 2 * 1024 * 1024 * 1024); // 2GB
        if ($fileSize > $maxSize) {
            throw \app\exception\VideoProcessingException::fileTooLarge($fileSize, $maxSize, 'validation', $videoId);
        }
    }

    /**
     * 创建处理目录
     * 
     * @param int $videoId 视频ID
     * @return string 处理目录路径
     */
    private function createProcessDirectory(int $videoId): string
    {
        $processDir = $this->outputDir . DIRECTORY_SEPARATOR . 'processed' . DIRECTORY_SEPARATOR . $videoId;
        $this->ensureDirectoryExists($processDir);
        return $processDir;
    }

    /**
     * 更新视频记录
     * 
     * @param int $videoId 视频ID
     * @param array $data 更新数据
     * @return void
     */
    private function updateVideoRecord(int $videoId, array $data): void
    {
        // 直接使用ThinkPHP的Db类更新视频记录
        Db::table('videos')
            ->where('id', $videoId)
            ->update($data);
    }

    /**
     * 确保目录存在
     * 
     * @param string $path 目录路径
     * @return void
     */
    private function ensureDirectoryExists(string $path): void
    {
        if (!is_dir($path)) {
            if (!mkdir($path, 0755, true)) {
                throw new \Exception('无法创建目录: ' . $path);
            }
        }
    }

    /**
     * 加密HLS切片文件
     *
     * 对所有TS切片文件进行AES-128加密处理
     * 加密后的文件会替换原始文件
     *
     * @param array $segments 切片文件路径数组
     * @param array $keyInfo 加密密钥信息
     * @param string $outputDir 输出目录
     * @return array 加密后的切片文件路径数组
     */
    private function encryptHLSSegments(array $segments, array $keyInfo, string $outputDir): array
    {
        $encryptedSegments = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($segments as $segment) {
            try {
                // 生成加密后的文件路径
                $encryptedSegment = $segment . '.encrypted';

                // 加密切片文件
                $success = $this->encryptionService->encryptTSFile($segment, $encryptedSegment, $keyInfo);

                if ($success) {
                    // 用加密文件替换原文件
                    if (rename($encryptedSegment, $segment)) {
                        $encryptedSegments[] = $segment;
                        $successCount++;
                    } else {
                        Log::error('无法替换原始切片文件', [
                            'original' => $segment,
                            'encrypted' => $encryptedSegment
                        ]);
                        $failCount++;
                    }
                } else {
                    Log::error('切片文件加密失败', ['segment' => $segment]);
                    $failCount++;
                }

            } catch (\Exception $e) {
                Log::error('切片加密过程异常', [
                    'segment' => $segment,
                    'error' => $e->getMessage()
                ]);
                $failCount++;
            }
        }

        Log::info('HLS切片加密完成', [
            'total_segments' => count($segments),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'key_id' => $keyInfo['key_id']
        ]);

        return $encryptedSegments;
    }

    /**
     * 生成密钥获取URL
     *
     * 生成客户端获取解密密钥的URL地址
     * 这个URL会被写入m3u8播放列表中
     *
     * @param string $keyId 密钥ID
     * @return string 密钥获取URL
     */
    private function generateKeyUrl(string $keyId): string
    {
        // 优先使用环境变量配置的API基础URL
        $baseUrl = env('API_BASE_URL');

        // 如果环境变量为空，尝试从请求中获取（仅在Web请求时可用）
        if (empty($baseUrl)) {
            try {
                if (function_exists('request') && request()) {
                    $baseUrl = request()->domain();
                }
            } catch (\Exception $e) {
                // 队列处理时request()不可用，使用默认值
                $baseUrl = 'http://localhost:3000';
            }
        }

        // 确保baseUrl不为空且格式正确
        if (empty($baseUrl) || $baseUrl === 'http://' || $baseUrl === 'http://api') {
            $baseUrl = 'http://localhost:3000';
        }

        // 移除末尾的斜杠
        $baseUrl = rtrim($baseUrl, '/');

        return $baseUrl . '/api/v1/video/encryption/key/' . $keyId;
    }

    /**
     * 获取相对路径
     *
     * @param string $fullPath 完整路径
     * @return string 相对路径
     */
    private function getRelativePath(string $fullPath): string
    {
        $publicPath = $this->getRootPath() . 'public';
        return str_replace($publicPath, '', $fullPath);
    }
}
