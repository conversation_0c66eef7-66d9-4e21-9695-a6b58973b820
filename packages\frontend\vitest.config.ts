/**
 * 🧪 Vitest 测试配置
 *
 * 企业级视频平台的完整测试配置
 *
 * <AUTHOR> Video Platform Team
 * @version 2.0.0
 */

import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],

  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },

  test: {
    // 测试环境配置
    environment: 'jsdom',

    // 全局设置
    globals: true,

    // 设置文件
    setupFiles: ['./tests/setup.ts'],

    // 包含的测试文件
    include: [
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],

    // 排除的文件
    exclude: [
      'node_modules',
      'dist',
      '.nuxt',
      'coverage',
      '**/*.d.ts'
    ],

    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'tests/',
        'dist/',
        '**/*.d.ts',
        '**/*.config.{js,ts}',
        '**/types.ts',
        '**/constants.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },

    // 报告器配置
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results.json',
      html: './test-results.html'
    },

    // 测试超时
    testTimeout: 10000,
    hookTimeout: 10000,

    // 并发配置
    threads: true,
    maxThreads: 4,
    minThreads: 1,

    // 监听模式配置
    watch: {
      ignore: ['node_modules/**', 'dist/**', 'coverage/**']
    },

    // 性能配置
    isolate: true,
    pool: 'threads',

    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITE_APP_ENV: 'test',
      VITE_API_BASE_URL: 'http://localhost:3000',
      VITE_API_KEY_USER: 'test-api-key'
    },

    // 模拟配置
    deps: {
      inline: [
        'element-plus',
        '@element-plus/icons-vue'
      ]
    },

    // 序列化配置
    sequence: {
      shuffle: false,
      concurrent: true
    },

    // 重试配置
    retry: 2,

    // 日志级别
    logHeapUsage: true
  },

  // 定义全局变量
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
  },

  // 优化依赖
  optimizeDeps: {
    include: [
      'vue',
      '@vue/test-utils',
      'vitest'
    ]
  }
})
