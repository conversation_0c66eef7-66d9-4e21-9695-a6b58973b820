#!/usr/bin/env node

/**
 * 🔧 环境管理器
 * 
 * 一键切换开发环境和生产环境的完整解决方案
 * 
 * <AUTHOR> Video Platform Team
 * @version 2.0.0
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 环境配置
const ENVIRONMENTS = {
  development: {
    name: '开发环境',
    apiUrl: 'http://localhost:3000',
    frontendUrl: 'http://localhost:3002',
    adminUrl: 'http://localhost:3001',
    debug: true,
    sourceMap: true,
    minify: false
  },
  staging: {
    name: '测试环境',
    apiUrl: 'http://test.example.com:3000',
    frontendUrl: 'http://test.example.com:3002',
    adminUrl: 'http://test.example.com:3001',
    debug: true,
    sourceMap: true,
    minify: false
  },
  production: {
    name: '生产环境',
    apiUrl: 'https://api.example.com',
    frontendUrl: 'https://www.example.com',
    adminUrl: 'https://admin.example.com',
    debug: false,
    sourceMap: false,
    minify: true
  }
}

class EnvironmentManager {
  constructor() {
    this.rootDir = path.resolve(__dirname, '..')
    this.envFile = path.join(this.rootDir, '.env')
    this.envExampleFile = path.join(this.rootDir, '.env.example')
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(`
🔧 环境管理器 - 一键切换开发/生产环境

用法:
  node scripts/env-manager.js <command> [subcommand] [options]

命令:
  switch <env>     切换到指定环境 (development|staging|production)
  start [env]      启动指定环境的服务 (默认当前环境)
  stop             停止所有服务
  restart [env]    重启指定环境的服务 (默认当前环境)
  status           显示当前环境状态
  health           检查服务健康状态
  logs [service]   查看服务日志 (可指定具体服务)
  init             初始化环境配置
  backup           备份当前环境配置
  validate         验证环境配置
  help             显示帮助信息

示例:
  node scripts/env-manager.js switch development --restart
  node scripts/env-manager.js switch production --backup
  node scripts/env-manager.js start development
  node scripts/env-manager.js restart production
  node scripts/env-manager.js logs api
  node scripts/env-manager.js status
  node scripts/env-manager.js health

选项:
  --force         强制覆盖现有配置
  --backup        切换前自动备份
  --restart       切换后自动重启服务

快捷命令 (package.json):
  npm run env:dev              # 切换到开发环境
  npm run env:prod             # 切换到生产环境
  npm run env:status           # 查看状态
  npm run env:health           # 检查健康状态
  npm run start                # 启动服务
  npm run stop                 # 停止服务
  npm run restart              # 重启服务
  npm run logs                 # 查看日志
    `)
  }

  /**
   * 获取当前环境
   */
  getCurrentEnvironment() {
    if (!fs.existsSync(this.envFile)) {
      return null
    }

    const envContent = fs.readFileSync(this.envFile, 'utf8')
    const envMatch = envContent.match(/^APP_ENV=(.+)$/m)
    return envMatch ? envMatch[1] : 'development'
  }

  /**
   * 显示当前状态
   */
  showStatus() {
    const currentEnv = this.getCurrentEnvironment()
    
    console.log('\n📊 当前环境状态:')
    console.log('================')
    
    if (!currentEnv) {
      console.log('❌ 未找到环境配置文件')
      console.log('💡 运行 "node scripts/env-manager.js init" 初始化配置')
      return
    }

    const envConfig = ENVIRONMENTS[currentEnv]
    if (envConfig) {
      console.log(`🌍 当前环境: ${envConfig.name} (${currentEnv})`)
      console.log(`🔗 API地址: ${envConfig.apiUrl}`)
      console.log(`🖥️ 前端地址: ${envConfig.frontendUrl}`)
      console.log(`⚙️ 管理后台: ${envConfig.adminUrl}`)
      console.log(`🐛 调试模式: ${envConfig.debug ? '开启' : '关闭'}`)
      console.log(`🗺️ Source Map: ${envConfig.sourceMap ? '开启' : '关闭'}`)
    } else {
      console.log(`⚠️ 未知环境: ${currentEnv}`)
    }

    // 检查服务状态
    this.checkServicesStatus()
  }

  /**
   * 检查服务状态
   */
  checkServicesStatus() {
    console.log('\n🔍 服务状态检查:')
    console.log('================')

    try {
      // 检查Docker服务
      execSync('docker-compose ps', { stdio: 'pipe' })
      console.log('🐳 Docker服务: 运行中')
    } catch (error) {
      console.log('🐳 Docker服务: 未运行')
    }

    // 检查端口占用
    const ports = [3000, 3001, 3002, 3306, 6379]
    ports.forEach(port => {
      try {
        execSync(`netstat -an | findstr :${port}`, { stdio: 'pipe' })
        console.log(`🔌 端口 ${port}: 已占用`)
      } catch (error) {
        console.log(`🔌 端口 ${port}: 空闲`)
      }
    })
  }

  /**
   * 初始化环境配置
   */
  initEnvironment(force = false) {
    console.log('\n🚀 初始化环境配置...')

    if (fs.existsSync(this.envFile) && !force) {
      console.log('⚠️ 环境配置文件已存在')
      console.log('💡 使用 --force 参数强制覆盖')
      return
    }

    if (!fs.existsSync(this.envExampleFile)) {
      console.log('❌ 未找到 .env.example 模板文件')
      return
    }

    // 复制模板文件
    fs.copyFileSync(this.envExampleFile, this.envFile)
    console.log('✅ 环境配置文件已创建')

    // 设置为开发环境
    this.switchEnvironment('development', false)
  }

  /**
   * 切换环境
   */
  switchEnvironment(targetEnv, backup = false, autoRestart = false) {
    if (!ENVIRONMENTS[targetEnv]) {
      console.log(`❌ 无效的环境: ${targetEnv}`)
      console.log(`💡 可用环境: ${Object.keys(ENVIRONMENTS).join(', ')}`)
      return
    }

    const envConfig = ENVIRONMENTS[targetEnv]
    console.log(`\n🔄 切换到${envConfig.name}...`)

    // 备份当前配置
    if (backup && fs.existsSync(this.envFile)) {
      this.backupEnvironment()
    }

    // 确保环境文件存在
    if (!fs.existsSync(this.envFile)) {
      this.initEnvironment()
    }

    // 更新环境变量
    this.updateEnvironmentFile(targetEnv, envConfig)

    // 更新前端环境变量
    this.updateFrontendEnvironment(targetEnv, envConfig)

    // 更新Docker配置
    this.updateDockerConfiguration(targetEnv)

    console.log(`✅ 已切换到${envConfig.name}`)

    if (autoRestart) {
      console.log('\n🔄 自动重启服务...')
      this.restartServices(targetEnv)
    } else {
      console.log('\n📋 下一步操作:')
      console.log(`1. 重启服务: npm run env:restart:${targetEnv}`)
      console.log('2. 或手动重启: docker-compose down && docker-compose up -d')
      console.log('3. 检查状态: npm run env:status')
    }
  }

  /**
   * 更新环境文件
   */
  updateEnvironmentFile(env, config) {
    let envContent = fs.readFileSync(this.envFile, 'utf8')

    // 更新环境变量
    const updates = {
      'APP_ENV': env,
      'NODE_ENV': env === 'production' ? 'production' : 'development',
      'APP_DEBUG': config.debug.toString(),
      'VITE_API_BASE_URL': config.apiUrl,
      'VITE_FRONTEND_BASE_URL': config.frontendUrl,
      'VITE_ADMIN_BASE_URL': config.adminUrl,
      'VITE_SOURCE_MAP': config.sourceMap.toString(),
      'VITE_CONSOLE_LOG': config.debug.toString()
    }

    Object.entries(updates).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm')
      if (regex.test(envContent)) {
        envContent = envContent.replace(regex, `${key}=${value}`)
      } else {
        envContent += `\n${key}=${value}`
      }
    })

    fs.writeFileSync(this.envFile, envContent)
  }

  /**
   * 更新前端环境变量
   */
  updateFrontendEnvironment(env, config) {
    const frontendEnvFile = path.join(this.rootDir, 'packages/frontend/.env')
    
    const frontendEnvContent = `# 自动生成的前端环境配置
VITE_APP_ENV=${env}
VITE_API_BASE_URL=${config.apiUrl}
VITE_FRONTEND_BASE_URL=${config.frontendUrl}
VITE_ADMIN_BASE_URL=${config.adminUrl}
VITE_SOURCE_MAP=${config.sourceMap}
VITE_CONSOLE_LOG=${config.debug}
VITE_ENABLE_DEVTOOLS=${config.debug}
VITE_PERFORMANCE_MONITOR=${config.debug}
`

    fs.writeFileSync(frontendEnvFile, frontendEnvContent)

    // 同样更新管理后台
    const adminEnvFile = path.join(this.rootDir, 'packages/admin/.env')
    fs.writeFileSync(adminEnvFile, frontendEnvContent)
  }

  /**
   * 备份环境配置
   */
  backupEnvironment() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const backupFile = path.join(this.rootDir, `.env.backup.${timestamp}`)
    
    if (fs.existsSync(this.envFile)) {
      fs.copyFileSync(this.envFile, backupFile)
      console.log(`💾 环境配置已备份到: ${backupFile}`)
    }
  }

  /**
   * 更新Docker配置
   */
  updateDockerConfiguration(env) {
    const composeFile = path.join(this.rootDir, 'docker-compose.yml')
    const envComposeFile = path.join(this.rootDir, `docker-compose.${env}.yml`)

    if (fs.existsSync(envComposeFile)) {
      // 创建符号链接或复制文件
      if (fs.existsSync(composeFile)) {
        fs.unlinkSync(composeFile)
      }
      fs.copyFileSync(envComposeFile, composeFile)
      console.log(`📦 已更新Docker配置为${env}环境`)
    }
  }

  /**
   * 重启服务
   */
  restartServices(env) {
    try {
      console.log('🛑 停止现有服务...')
      execSync('docker-compose down', { stdio: 'inherit' })

      console.log('🚀 启动新服务...')
      const composeFile = `docker-compose.${env}.yml`
      execSync(`docker-compose -f ${composeFile} up -d`, { stdio: 'inherit' })

      console.log('✅ 服务重启完成')

      // 等待服务启动
      setTimeout(() => {
        this.checkServicesHealth()
      }, 10000)

    } catch (error) {
      console.error('❌ 服务重启失败:', error.message)
    }
  }

  /**
   * 检查服务健康状态
   */
  checkServicesHealth() {
    console.log('\n🔍 检查服务健康状态...')

    try {
      const result = execSync('docker-compose ps --format json', { encoding: 'utf8' })
      const services = JSON.parse(`[${result.trim().split('\n').join(',')}]`)

      services.forEach(service => {
        const status = service.State === 'running' ? '🟢' : '🔴'
        console.log(`${status} ${service.Service}: ${service.State}`)
      })

    } catch (error) {
      console.log('⚠️ 无法获取服务状态，请手动检查')
    }
  }

  /**
   * 验证环境配置
   */
  validateEnvironment() {
    console.log('\n🔍 验证环境配置...')

    const errors = []
    const warnings = []

    // 检查必需文件
    if (!fs.existsSync(this.envFile)) {
      errors.push('缺少 .env 文件')
    }

    // 检查Docker配置
    const dockerFiles = ['docker-compose.development.yml', 'docker-compose.production.yml']
    dockerFiles.forEach(file => {
      if (!fs.existsSync(path.join(this.rootDir, file))) {
        errors.push(`缺少 ${file} 文件`)
      }
    })

    // 检查包配置
    const packages = ['frontend', 'admin', 'api']
    packages.forEach(pkg => {
      const pkgPath = path.join(this.rootDir, 'packages', pkg)
      if (!fs.existsSync(pkgPath)) {
        errors.push(`缺少 packages/${pkg} 目录`)
      }
    })

    // 检查Docker是否运行
    try {
      execSync('docker --version', { stdio: 'pipe' })
      execSync('docker-compose --version', { stdio: 'pipe' })
    } catch (error) {
      errors.push('Docker 或 Docker Compose 未安装或未运行')
    }

    // 显示结果
    if (errors.length === 0) {
      console.log('✅ 环境配置验证通过')
      this.checkServicesHealth()
    } else {
      console.log('❌ 发现配置错误:')
      errors.forEach(error => console.log(`  - ${error}`))
    }

    if (warnings.length > 0) {
      console.log('⚠️ 警告:')
      warnings.forEach(warning => console.log(`  - ${warning}`))
    }
  }

  /**
   * 运行命令
   */
  run(args) {
    const command = args[0]
    const subCommand = args[1]
    const options = args.slice(2)

    switch (command) {
      case 'switch':
        const env = subCommand
        const backup = options.includes('--backup')
        const autoRestart = options.includes('--restart')
        if (!env) {
          console.log('❌ 请指定环境名称')
          this.showHelp()
          return
        }
        this.switchEnvironment(env, backup, autoRestart)
        break

      case 'restart':
        const restartEnv = subCommand || this.getCurrentEnvironment() || 'development'
        this.restartServices(restartEnv)
        break

      case 'health':
        this.checkServicesHealth()
        break

      case 'status':
        this.showStatus()
        break

      case 'init':
        const force = options.includes('--force')
        this.initEnvironment(force)
        break

      case 'backup':
        this.backupEnvironment()
        break

      case 'validate':
        this.validateEnvironment()
        break

      case 'logs':
        const service = subCommand || ''
        try {
          const cmd = service ? `docker-compose logs -f ${service}` : 'docker-compose logs -f'
          execSync(cmd, { stdio: 'inherit' })
        } catch (error) {
          console.error('❌ 无法获取日志:', error.message)
        }
        break

      case 'stop':
        try {
          console.log('🛑 停止所有服务...')
          execSync('docker-compose down', { stdio: 'inherit' })
          console.log('✅ 服务已停止')
        } catch (error) {
          console.error('❌ 停止服务失败:', error.message)
        }
        break

      case 'start':
        const startEnv = subCommand || this.getCurrentEnvironment() || 'development'
        try {
          console.log(`🚀 启动${ENVIRONMENTS[startEnv]?.name || startEnv}服务...`)
          const composeFile = `docker-compose.${startEnv}.yml`
          execSync(`docker-compose -f ${composeFile} up -d`, { stdio: 'inherit' })
          console.log('✅ 服务启动完成')
          setTimeout(() => this.checkServicesHealth(), 5000)
        } catch (error) {
          console.error('❌ 启动服务失败:', error.message)
        }
        break

      case 'help':
      default:
        this.showHelp()
        break
    }
  }
}

// 运行环境管理器
if (require.main === module) {
  const manager = new EnvironmentManager()
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    manager.showHelp()
  } else {
    manager.run(args)
  }
}

module.exports = EnvironmentManager
