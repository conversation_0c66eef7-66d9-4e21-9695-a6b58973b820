/**
 * 环境配置管理器
 * 
 * 统一管理所有环境变量，提供类型安全的配置访问
 * 支持开发、测试、生产环境一键切换
 * 
 * <AUTHOR> Video Platform Team
 * @version 2.0.0
 */

// ================================
// 环境类型定义
// ================================

export type Environment = 'development' | 'staging' | 'production'

export interface AppConfig {
  // 应用基础信息
  name: string
  title: string
  version: string
  env: Environment
  debug: boolean

  // 服务地址
  apiBaseUrl: string
  frontendBaseUrl: string
  adminBaseUrl: string
  wsUrl: string

  // API配置
  apiTimeout: number
  apiRetryCount: number
  apiKey: string

  // 上传配置
  upload: {
    maxSize: number
    chunkSize: number
    concurrent: number
    allowedTypes: string[]
  }

  // 视频配置
  video: {
    autoPlay: boolean
    preload: 'none' | 'metadata' | 'auto'
    qualityAuto: boolean
  }

  // 缓存配置
  cache: {
    enabled: boolean
    expireTime: number
  }

  // 功能开关
  features: {
    live: boolean
    vip: boolean
    comment: boolean
    share: boolean
    mock: boolean
  }

  // 开发工具
  devTools: {
    enabled: boolean
    sourceMap: boolean
    consoleLog: boolean
    performanceMonitor: boolean
  }

  // CDN配置
  cdn: {
    url: string
    staticUrl: string
  }

  // 第三方服务
  thirdParty: {
    analyticsId: string
    sentryDsn: string
  }

  // 安全配置
  security: {
    secureMode: boolean
    httpsOnly: boolean
  }
}

// ================================
// 环境变量获取函数
// ================================

const getEnvVar = (key: string, defaultValue: string = ''): string => {
  return import.meta.env[key] || defaultValue
}

// 智能API地址获取函数
const getApiBaseUrl = (): string => {
  const env = getEnvVar('VITE_APP_ENV', 'development')

  // 根据环境自动选择API地址
  if (env === 'production') {
    return getEnvVar('VITE_API_BASE_URL_PROD', getEnvVar('VITE_API_BASE_URL', 'http://localhost:3000'))
  } else if (env === 'staging') {
    return getEnvVar('VITE_API_BASE_URL_STAGING', getEnvVar('VITE_API_BASE_URL', 'http://localhost:3000'))
  } else {
    return getEnvVar('VITE_API_BASE_URL', 'http://localhost:3000')
  }
}

const getEnvBoolean = (key: string, defaultValue: boolean = false): boolean => {
  const value = getEnvVar(key)
  if (value === '') return defaultValue
  return value === 'true' || value === '1'
}

const getEnvNumber = (key: string, defaultValue: number = 0): number => {
  const value = getEnvVar(key)
  if (value === '') return defaultValue
  const num = parseInt(value, 10)
  return isNaN(num) ? defaultValue : num
}

const getEnvArray = (key: string, defaultValue: string[] = []): string[] => {
  const value = getEnvVar(key)
  if (value === '') return defaultValue
  return value.split(',').map(item => item.trim()).filter(Boolean)
}

// ================================
// 配置对象
// ================================

export const config: AppConfig = {
  // 应用基础信息
  name: getEnvVar('VITE_APP_NAME', '51吃瓜网'),
  title: getEnvVar('VITE_APP_TITLE', '51吃瓜网'),
  version: getEnvVar('VITE_APP_VERSION', '2.0.0'),
  env: getEnvVar('VITE_APP_ENV', 'development') as Environment,
  debug: getEnvBoolean('VITE_APP_DEBUG', false),

  // 服务地址 - 智能切换
  apiBaseUrl: getApiBaseUrl(),
  frontendBaseUrl: getEnvVar('VITE_FRONTEND_BASE_URL', 'http://localhost:3002'),
  adminBaseUrl: getEnvVar('VITE_ADMIN_BASE_URL', 'http://localhost:3001'),
  wsUrl: getEnvVar('VITE_WS_URL', 'ws://localhost:3000/ws'),

  // API配置
  apiTimeout: getEnvNumber('VITE_API_TIMEOUT', 30000),
  apiRetryCount: getEnvNumber('VITE_API_RETRY_COUNT', 3),
  apiKey: getEnvVar('VITE_API_KEY_USER', ''), // 移除硬编码，使用环境变量

  // 上传配置
  upload: {
    maxSize: getEnvNumber('VITE_UPLOAD_MAX_SIZE', 500),
    chunkSize: getEnvNumber('VITE_UPLOAD_CHUNK_SIZE', 2),
    concurrent: getEnvNumber('VITE_UPLOAD_CONCURRENT', 3),
    allowedTypes: getEnvArray('VITE_UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'mp4'])
  },

  // 视频配置
  video: {
    autoPlay: getEnvBoolean('VITE_VIDEO_AUTO_PLAY', false),
    preload: getEnvVar('VITE_VIDEO_PRELOAD', 'metadata') as 'none' | 'metadata' | 'auto',
    qualityAuto: getEnvBoolean('VITE_VIDEO_QUALITY_AUTO', true)
  },

  // 缓存配置
  cache: {
    enabled: getEnvBoolean('VITE_CACHE_ENABLED', true),
    expireTime: getEnvNumber('VITE_CACHE_EXPIRE_TIME', 300000)
  },

  // 功能开关
  features: {
    live: getEnvBoolean('VITE_FEATURE_LIVE', true),
    vip: getEnvBoolean('VITE_FEATURE_VIP', true),
    comment: getEnvBoolean('VITE_FEATURE_COMMENT', true),
    share: getEnvBoolean('VITE_FEATURE_SHARE', true),
    mock: getEnvBoolean('VITE_ENABLE_MOCK', false)
  },

  // 开发工具
  devTools: {
    enabled: getEnvBoolean('VITE_ENABLE_DEVTOOLS', false),
    sourceMap: getEnvBoolean('VITE_SOURCE_MAP', false),
    consoleLog: getEnvBoolean('VITE_CONSOLE_LOG', false),
    performanceMonitor: getEnvBoolean('VITE_PERFORMANCE_MONITOR', false)
  },

  // CDN配置
  cdn: {
    url: getEnvVar('VITE_CDN_URL', ''),
    staticUrl: getEnvVar('VITE_STATIC_URL', '')
  },

  // 第三方服务
  thirdParty: {
    analyticsId: getEnvVar('VITE_ANALYTICS_ID', ''),
    sentryDsn: getEnvVar('VITE_SENTRY_DSN', '')
  },

  // 安全配置
  security: {
    secureMode: getEnvBoolean('VITE_SECURE_MODE', false),
    httpsOnly: getEnvBoolean('VITE_HTTPS_ONLY', false)
  }
}

// ================================
// 环境检查函数
// ================================

export const isDevelopment = (): boolean => config.env === 'development'
export const isStaging = (): boolean => config.env === 'staging'
export const isProduction = (): boolean => config.env === 'production'

// ================================
// 配置验证
// ================================

export const validateConfig = (): void => {
  const errors: string[] = []

  // 验证必需的配置
  if (!config.apiBaseUrl) {
    errors.push('API基础URL未配置')
  }

  if (!config.name) {
    errors.push('应用名称未配置')
  }

  // 验证URL格式
  try {
    new URL(config.apiBaseUrl)
  } catch {
    errors.push('API基础URL格式无效')
  }

  if (errors.length > 0) {
    console.error('配置验证失败:', errors)
    throw new Error(`配置验证失败: ${errors.join(', ')}`)
  }
}

// ================================
// 导出默认配置
// ================================

export default config

// 在开发环境下打印配置信息
if (isDevelopment() && config.devTools.consoleLog) {
  console.log('🔧 当前环境配置:', config)
}
