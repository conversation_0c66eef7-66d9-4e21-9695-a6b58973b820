import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomePage.vue'),
    meta: {
      title: '51吃瓜网'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginPage.vue'),
    meta: {
      title: '登录',
      requiresGuest: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/RegisterPage.vue'),
    meta: {
      title: '注册',
      requiresGuest: true
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/ForgotPasswordPage.vue'),
    meta: {
      title: '找回密码',
      requiresGuest: true
    }
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: () => import('@/views/ResetPasswordPage.vue'),
    meta: {
      title: '重置密码',
      requiresGuest: true
    }
  },

  {
    path: '/short-videos',
    name: 'ShortVideos',
    component: () => import('@/views/TikTokStyle.vue'),
    meta: {
      title: '短视频'
    }
  },
  {
    path: '/live',
    name: 'Live',
    component: () => import('@/views/LivePage.vue'),
    meta: {
      title: '直播'
    }
  },
  {
    path: '/long-videos',
    name: 'LongVideos',
    component: () => import('@/views/LongVideos.vue'),
    meta: {
      title: '长视频'
    }
  },
  {
    path: '/vip',
    name: 'VIP',
    component: () => import('@/views/VipPage.vue'),
    meta: {
      title: '会员'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/ProfilePage.vue'),
    meta: {
      title: '我的',
      requiresAuth: true
    }
  },
  {
    path: '/video/:id',
    name: 'VideoDetail',
    component: () => import('@/views/VideoPlayer.vue'),
    meta: {
      title: '视频播放'
    }
  },
  {
    path: '/shaka-test',
    name: 'ShakaTest',
    component: () => import('@/views/ShakaTest.vue'),
    meta: {
      title: 'Shaka Player 测试'
    }
  },
  {
    path: '/upload',
    name: 'UploadPage',
    component: () => import('@/views/UploadPage.vue'),
    meta: {
      title: '上传视频',
      requiresAuth: true
    }
  },
  {
    path: '/my-videos',
    name: 'MyVideosPage',
    component: () => import('@/views/MyVideosPage.vue'),
    meta: {
      title: '我的作品',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 51吃瓜网`
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    console.log('🛡️ 路由守卫检查:', {
      path: to.path,
      requiresAuth: to.meta.requiresAuth,
      hasToken: !!token
    })

    if (!token) {
      console.log('❌ 路由守卫：没有token，跳转到登录页面')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    } else {
      console.log('✅ 路由守卫：有token，允许访问')
    }
  }

  // 检查是否需要游客状态（已登录用户不能访问登录/注册页面）
  if (to.meta.requiresGuest) {
    const token = localStorage.getItem('token')
    if (token) {
      next('/')
      return
    }
  }

  next()
})

export default router
