<?php
declare(strict_types=1);

namespace app\controller\admin;

use app\BaseController;
use think\Request;
use think\Response;
use think\facade\Db;
use think\facade\Validate;
use think\facade\Cache;
use app\service\ResponseService;
use app\service\ValidationService;

/**
 * 管理员认证控制器
 */
class Auth extends BaseController
{
    protected $responseService;
    protected $validationService;

    protected function initialize()
    {
        parent::initialize();
        // 暂时注释掉服务依赖，避免初始化问题
        // $this->responseService = new ResponseService();
        // $this->validationService = new ValidationService();
    }
    /**
     * 管理员登录
     */
    public function login(Request $request): Response
    {
        try {
            // 使用新的数据获取方法
            $data = $this->getRequestData(['username', 'password']);

            // 调试日志
            \think\facade\Log::info('管理员登录请求', [
                'data' => $data,
                'raw_input' => file_get_contents('php://input'),
                'post' => $request->post()
            ]);

            // 验证参数
            if (empty($data['username']) || empty($data['password'])) {
                return json([
                    'success' => false,
                    'message' => '用户名和密码不能为空',
                    'data' => null
                ]);
            }
            
            // 查找管理员
            $admin = Db::table('admins')
                ->where('username', $data['username'])
                ->where('status', 'active')
                ->find();

            // 防止时序攻击：无论用户是否存在都执行password_verify
            $isValidPassword = false;
            if ($admin) {
                $isValidPassword = password_verify($data['password'], $admin['password']);
            } else {
                // 即使用户不存在也执行一次password_verify以保持时序一致
                password_verify($data['password'], '$2y$10$dummy.hash.to.prevent.timing.attacks');
            }

            if (!$admin || !$isValidPassword) {
                return json([
                    'success' => false,
                    'message' => '用户名或密码错误',
                    'data' => null
                ]);
            }
            
            // 更新登录时间
            Db::table('admins')->where('id', $admin['id'])->update([
                'last_login_time' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 清理该用户的旧token（防止多个token同时存在）
            $this->clearUserTokens($admin['id']);

            // 生成新的token
            $token = 'admin_' . $admin['id'] . '_' . time() . '_' . md5(uniqid() . $admin['username']);

            // 统一的token存储逻辑
            $tokenData = [
                'id' => $admin['id'],
                'username' => $admin['username'],
                'email' => $admin['email'],
                'name' => $admin['name'],
                'role_id' => $admin['role_id'],
                'status' => $admin['status'],
                'login_time' => time(),
                'expires_at' => time() + 86400,
                'ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent')
            ];

            // 使用文件缓存存储token（因为Redis被禁用）
            $tokenStored = false;

            // 直接使用文件存储
            $tokenFile = runtime_path() . 'cache/admin_tokens/' . $token . '.json';
            $tokenDir = dirname($tokenFile);
            if (!is_dir($tokenDir)) {
                mkdir($tokenDir, 0755, true);
            }

            if (file_put_contents($tokenFile, json_encode($tokenData))) {
                $tokenStored = true;
                \think\facade\Log::info('管理员登录成功，token存储到文件缓存', [
                    'admin_id' => $admin['id'],
                    'username' => $admin['username'],
                    'token' => substr($token, 0, 20) . '...',
                    'token_file' => $tokenFile
                ]);
            } else {
                \think\facade\Log::error('文件缓存存储失败', [
                    'token_file' => $tokenFile,
                    'token_dir' => $tokenDir,
                    'dir_exists' => is_dir($tokenDir),
                    'dir_writable' => is_writable($tokenDir)
                ]);
            }

            if (!$tokenStored) {
                return json([
                    'success' => false,
                    'message' => '登录失败，请稍后重试',
                    'data' => null
                ]);
            }

            return json([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'admin' => [
                        'id' => $admin['id'],
                        'username' => $admin['username'],
                        'email' => $admin['email'],
                        'name' => $admin['name'],
                        'role_id' => $admin['role_id'],
                        'status' => $admin['status']
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '登录失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 验证token有效性（用于静默检查）
     */
    public function verify(Request $request): Response
    {
        try {
            // 从请求头获取token
            $token = $this->getTokenFromRequest($request);
            if (!$token) {
                return json([
                    'success' => false,
                    'code' => 401,
                    'message' => '未提供认证token',
                    'data' => null
                ]);
            }

            // 验证token
            $adminInfo = $this->validateToken($token);
            if (!$adminInfo) {
                return json([
                    'success' => false,
                    'code' => 401,
                    'message' => 'token无效或已过期',
                    'data' => null
                ]);
            }

            // 检查管理员状态
            $admin = Db::table('admins')
                ->field('id, status')
                ->where('id', $adminInfo['id'])
                ->find();

            if (!$admin || $admin['status'] !== 'active') {
                return json([
                    'success' => false,
                    'code' => 401,
                    'message' => '管理员账户已被禁用',
                    'data' => null
                ]);
            }

            return json([
                'success' => true,
                'code' => 200,
                'message' => 'token有效',
                'data' => ['valid' => true]
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'code' => 500,
                'message' => '验证失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取管理员信息
     */
    public function profile(Request $request): Response
    {
        try {
            // 从请求头获取token
            $token = $this->getTokenFromRequest($request);
            if (!$token) {
                return json([
                    'success' => false,
                    'message' => '未提供认证token',
                    'data' => null
                ]);
            }

            // 验证token并获取管理员信息
            $adminInfo = $this->validateToken($token);
            if (!$adminInfo) {
                return json([
                    'success' => false,
                    'message' => 'token无效或已过期',
                    'data' => null
                ]);
            }

            $admin = Db::table('admins')
                ->field('id, username, email, name, avatar, status, created_at, last_login_time')
                ->where('id', $adminInfo['id'])
                ->where('status', 'active')
                ->find();

            if (!$admin) {
                return json([
                    'success' => false,
                    'message' => '管理员不存在',
                    'data' => null
                ]);
            }

            return json([
                'success' => true,
                'message' => 'success',
                'data' => $admin
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取管理员信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 生成token
     */
    private function generateToken(int $adminId): string
    {
        return 'admin_' . $adminId . '_' . time() . '_' . md5(uniqid());
    }

    /**
     * 从请求中获取token
     */
    private function getTokenFromRequest(Request $request): ?string
    {
        $authorization = $request->header('Authorization');
        if ($authorization && strpos($authorization, 'Bearer ') === 0) {
            return substr($authorization, 7);
        }
        return null;
    }

    /**
     * 验证token
     */
    private function validateToken(string $token): ?array
    {
        try {
            // 首先尝试从缓存获取
            $adminInfo = Cache::get('admin_token_' . $token);
            if ($adminInfo) {
                return $adminInfo;
            }
        } catch (\Exception $e) {
            // 缓存失败，尝试从文件获取
        }

        // 尝试从文件缓存获取
        $tokenFile = runtime_path() . 'cache/admin_tokens/' . $token . '.json';
        if (file_exists($tokenFile)) {
            $tokenData = json_decode(file_get_contents($tokenFile), true);
            if ($tokenData && isset($tokenData['expires_at']) && $tokenData['expires_at'] > time()) {
                return $tokenData;
            } else {
                // 过期的token文件删除
                unlink($tokenFile);
            }
        }

        return null;
    }

    /**
     * 管理员登出
     */
    public function logout(Request $request): Response
    {
        try {
            $token = $this->getTokenFromRequest($request);
            if ($token) {
                // 删除token缓存
                Cache::delete('admin_token_' . $token);
            }

            return json([
                'success' => true,
                'message' => '登出成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '登出失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 清理用户的旧token
     */
    private function clearUserTokens(int $adminId): void
    {
        try {
            // 清理Redis中的旧token
            $pattern = 'admin_token_admin_' . $adminId . '_*';
            $keys = \think\facade\Cache::store('redis')->handler()->keys($pattern);
            if ($keys) {
                foreach ($keys as $key) {
                    // 移除缓存前缀
                    $cleanKey = str_replace('zhengshiban_dev_', '', $key);
                    \think\facade\Cache::delete($cleanKey);
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::warning('清理Redis token失败', ['error' => $e->getMessage()]);
        }

        try {
            // 清理文件缓存中的旧token
            $tokenDir = runtime_path() . 'cache/admin_tokens/';
            if (is_dir($tokenDir)) {
                $files = glob($tokenDir . 'admin_' . $adminId . '_*.json');
                foreach ($files as $file) {
                    unlink($file);
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::warning('清理文件token失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 测试数据接收
     */
    public function testData(Request $request): Response
    {
        try {
            // 获取原始输入
            $rawInput = file_get_contents('php://input');

            // 获取请求数据
            $data = $this->getRequestData();

            // 获取POST数据
            $postData = $request->post();

            // 获取所有参数
            $allParams = $request->param();

            return json([
                'success' => true,
                'message' => '数据接收测试',
                'data' => [
                    'raw_input' => $rawInput,
                    'parsed_data' => $data,
                    'post_data' => $postData,
                    'all_params' => $allParams,
                    'content_type' => $request->header('content-type'),
                    'method' => $request->method()
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '测试失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
