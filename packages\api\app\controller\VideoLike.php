<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Db;
use app\service\ResponseService;
use app\service\ValidationService;

/**
 * 视频点赞控制器
 * 
 * 处理视频点赞相关的所有操作
 */
class VideoLike
{
    protected ResponseService $responseService;
    protected ValidationService $validationService;

    public function __construct()
    {
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
    }

    /**
     * 点赞视频
     *
     * @param Request $request
     * @param int $id 视频ID
     * @return Response
     */
    public function like(Request $request, int $id): Response
    {
        try {
            // 从URL路径获取视频ID
            $videoId = $id;

            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            // 验证视频ID
            if ($videoId <= 0) {
                return $this->responseService->error('视频ID无效', 400);
            }

            // 检查视频是否存在
            $video = Db::table('videos')
                ->where('id', $videoId)
                ->where('status', 'published')
                ->find();

            if (!$video) {
                return $this->responseService->error('视频不存在', 404);
            }

            // 检查用户是否存在
            $user = Db::table('users')
                ->where('id', $userId)
                ->where('status', 1)
                ->find();

            if (!$user) {
                return $this->responseService->error('用户不存在', 404);
            }

            // 检查是否已经点赞
            $existingLike = Db::table('video_likes')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->find();

            if ($existingLike) {
                return $this->responseService->error('已经点赞过了', 400);
            }

            // 开始事务
            Db::startTrans();
            try {
                // 添加点赞记录
                Db::table('video_likes')->insert([
                    'video_id' => $videoId,
                    'user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // 更新视频点赞数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->inc('like_count', 1)
                    ->update();

                Db::commit();

                // 获取最新的点赞数
                $newLikeCount = Db::table('videos')
                    ->where('id', $videoId)
                    ->value('like_count');

                return $this->responseService->success([
                    'message' => '点赞成功',
                    'like_count' => $newLikeCount,
                    'is_liked' => true
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->responseService->error('点赞失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 取消点赞
     *
     * @param Request $request
     * @param int $id 视频ID
     * @return Response
     */
    public function unlike(Request $request, int $id): Response
    {
        try {
            // 从URL路径获取视频ID
            $videoId = $id;

            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            // 验证视频ID
            if ($videoId <= 0) {
                return $this->responseService->error('视频ID无效', 400);
            }

            // 检查点赞记录是否存在
            $existingLike = Db::table('video_likes')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->find();

            if (!$existingLike) {
                return $this->responseService->error('还没有点赞', 400);
            }

            // 开始事务
            Db::startTrans();
            try {
                // 删除点赞记录
                Db::table('video_likes')
                    ->where('video_id', $videoId)
                    ->where('user_id', $userId)
                    ->delete();

                // 更新视频点赞数
                Db::table('videos')
                    ->where('id', $videoId)
                    ->dec('like_count', 1)
                    ->update();

                Db::commit();

                // 获取最新的点赞数
                $newLikeCount = Db::table('videos')
                    ->where('id', $videoId)
                    ->value('like_count');

                return $this->responseService->success([
                    'message' => '取消点赞成功',
                    'like_count' => max(0, $newLikeCount), // 确保不为负数
                    'is_liked' => false
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->responseService->error('取消点赞失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 检查用户是否点赞了视频
     *
     * @param Request $request
     * @return Response
     */
    public function checkLike(Request $request): Response
    {
        try {
            $videoId = (int)$request->get('video_id');
            $userId = (int)$request->get('user_id');

            if (!$videoId || !$userId) {
                return $this->responseService->error('参数错误', 400);
            }

            // 检查点赞状态
            $isLiked = Db::table('video_likes')
                ->where('video_id', $videoId)
                ->where('user_id', $userId)
                ->count() > 0;

            // 获取视频点赞数
            $likeCount = Db::table('videos')
                ->where('id', $videoId)
                ->value('like_count') ?: 0;

            return $this->responseService->success([
                'is_liked' => $isLiked,
                'like_count' => $likeCount
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('检查点赞状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取视频的点赞列表
     *
     * @param Request $request
     * @return Response
     */
    public function getLikeList(Request $request): Response
    {
        try {
            $videoId = (int)$request->get('video_id');
            $page = (int)($request->get('page') ?? 1);
            $limit = (int)($request->get('limit') ?? 20);
            $offset = ($page - 1) * $limit;

            if (!$videoId) {
                return $this->responseService->error('视频ID不能为空', 400);
            }

            // 获取点赞列表
            $likes = Db::table('video_likes')
                ->alias('vl')
                ->join('users u', 'vl.user_id = u.id')
                ->where('vl.video_id', $videoId)
                ->field('u.id, u.username, u.nickname, u.avatar, vl.created_at')
                ->order('vl.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 获取总数
            $total = Db::table('video_likes')
                ->where('video_id', $videoId)
                ->count();

            return $this->responseService->success([
                'likes' => $likes,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('获取点赞列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取用户的点赞视频列表
     *
     * @param Request $request
     * @return Response
     */
    public function getUserLikes(Request $request): Response
    {
        try {
            $userId = (int)$request->get('user_id');
            $page = (int)($request->get('page') ?? 1);
            $limit = (int)($request->get('limit') ?? 20);
            $offset = ($page - 1) * $limit;

            if (!$userId) {
                return $this->responseService->error('用户ID不能为空', 400);
            }

            // 获取用户点赞的视频列表
            $videos = Db::table('video_likes')
                ->alias('vl')
                ->join('videos v', 'vl.video_id = v.id')
                ->join('users u', 'v.user_id = u.id')
                ->where('vl.user_id', $userId)
                ->where('v.status', 'published')
                ->field('v.id, v.title, v.description, v.thumbnail, v.duration, v.view_count, v.like_count, v.created_at, u.username, u.nickname, vl.created_at as liked_at')
                ->order('vl.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 获取总数
            $total = Db::table('video_likes')
                ->alias('vl')
                ->join('videos v', 'vl.video_id = v.id')
                ->where('vl.user_id', $userId)
                ->where('v.status', 'published')
                ->count();

            return $this->responseService->success([
                'videos' => $videos,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('获取用户点赞列表失败: ' . $e->getMessage(), 500);
        }
    }
}
