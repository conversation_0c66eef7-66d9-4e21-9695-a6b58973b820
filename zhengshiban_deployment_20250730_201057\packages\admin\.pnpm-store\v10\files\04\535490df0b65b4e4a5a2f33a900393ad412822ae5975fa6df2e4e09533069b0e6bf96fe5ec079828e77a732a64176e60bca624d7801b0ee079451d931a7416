{"version": 3, "file": "use-scrollbar.js", "sources": ["../../../../../../../packages/components/table/src/composables/use-scrollbar.ts"], "sourcesContent": ["import { ref } from 'vue'\nimport { isNumber } from '@element-plus/utils'\n\nexport const useScrollbar = () => {\n  const scrollBarRef = ref()\n\n  const scrollTo = (options: ScrollToOptions | number, yCoord?: number) => {\n    const scrollbar = scrollBarRef.value\n    if (scrollbar) {\n      scrollbar.scrollTo(options, yCoord)\n    }\n  }\n\n  const setScrollPosition = (position: 'Top' | 'Left', offset?: number) => {\n    const scrollbar = scrollBarRef.value\n    if (scrollbar && isNumber(offset) && ['Top', 'Left'].includes(position)) {\n      scrollbar[`setScroll${position}`](offset)\n    }\n  }\n\n  const setScrollTop = (top?: number) => setScrollPosition('Top', top)\n  const setScrollLeft = (left?: number) => setScrollPosition('Left', left)\n\n  return {\n    scrollBarRef,\n    scrollTo,\n    setScrollTop,\n    setScrollLeft,\n  }\n}\n"], "names": ["ref", "isNumber"], "mappings": ";;;;;;;AAEY,MAAC,YAAY,GAAG,MAAM;AAClC,EAAE,MAAM,YAAY,GAAGA,OAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,MAAM,KAAK;AACxC,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;AACzC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,QAAQ,EAAE,MAAM,KAAK;AAClD,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;AACzC,IAAI,IAAI,SAAS,IAAIC,cAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC7E,MAAM,SAAS,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAChD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC9D,EAAE,MAAM,aAAa,GAAG,CAAC,IAAI,KAAK,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAClE,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ;;;;"}