<template>
  <div class="test-container">
    <h1>管理后台测试页面</h1>
    <div class="test-section">
      <h2>系统状态</h2>
      <p>✅ Vue应用正常运行</p>
      <p>✅ 路由系统正常</p>
      <p>当前时间: {{ currentTime }}</p>
    </div>
    
    <div class="test-section">
      <h2>API测试</h2>
      <el-button @click="testApi" :loading="apiLoading">测试API连接</el-button>
      <div v-if="apiResult" class="api-result">
        <h3>API响应:</h3>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>登录测试</h2>
      <el-form :model="loginForm" style="max-width: 400px;">
        <el-form-item label="用户名">
          <el-input v-model="loginForm.username" placeholder="admin" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" placeholder="admin123" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testLogin" :loading="loginLoading">测试登录</el-button>
        </el-form-item>
      </el-form>
      <div v-if="loginResult" class="login-result">
        <h3>登录结果:</h3>
        <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>导航测试</h2>
      <el-button @click="$router.push('/login')">跳转到登录页</el-button>
      <el-button @click="$router.push('/dashboard')">跳转到仪表盘</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { adminAuthApi } from '@/api'

const currentTime = ref('')
const apiLoading = ref(false)
const apiResult = ref(null)
const loginLoading = ref(false)
const loginResult = ref(null)

const loginForm = ref({
  username: 'admin',
  password: 'admin123'
})

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 测试API连接
const testApi = async () => {
  apiLoading.value = true
  try {
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    const response = await fetch(`${apiBaseUrl}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'zhengshiban_admin_2024_secure_key_v1'
      },
      body: JSON.stringify({ test: true })
    })
    const data = await response.json()
    apiResult.value = data
    ElMessage.success('API连接成功')
  } catch (error) {
    apiResult.value = { error: error.message }
    ElMessage.error('API连接失败')
  } finally {
    apiLoading.value = false
  }
}

// 测试登录
const testLogin = async () => {
  loginLoading.value = true
  try {
    const response = await adminAuthApi.login(loginForm.value)
    loginResult.value = response
    ElMessage.success('登录测试成功')
  } catch (error) {
    loginResult.value = { error: error.message }
    ElMessage.error('登录测试失败')
  } finally {
    loginLoading.value = false
  }
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.api-result, .login-result {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
