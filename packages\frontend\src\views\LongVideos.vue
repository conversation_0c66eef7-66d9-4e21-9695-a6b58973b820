<template>
  <div class="long-videos">
    <!-- 顶部导航栏 -->
    <div class="home-nav">
      <div class="van-nav-bar">
        <div class="van-nav-bar__content">
          <!-- 左侧汉堡菜单 -->
          <div class="van-nav-bar__left">
            <div class="hamburger-menu" @click="toggleHamburgerMenu">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
              </svg>
            </div>
          </div>

          <!-- 中间分类标签 -->
          <div class="van-nav-bar__title">
            <div class="van-tabs">
              <div class="van-tabs__wrap">
                <div class="van-tabs__nav">
                  <!-- 固定的分类标签 -->
                  <div
                    class="van-tab"
                    :class="{ 'van-tab--active': currentCategory === 'all' }"
                    @click="filterByCategory('all')"
                  >
                    <span class="van-tab__text">全部</span>
                  </div>
                  <div
                    class="van-tab"
                    :class="{ 'van-tab--active': currentCategory === 'latest' }"
                    @click="filterByCategory('latest')"
                  >
                    <span class="van-tab__text">最新</span>
                  </div>
                  <div
                    class="van-tab"
                    :class="{ 'van-tab--active': currentCategory === 'hot' }"
                    @click="filterByCategory('hot')"
                  >
                    <span class="van-tab__text">最热</span>
                  </div>

                  <!-- 动态长视频分类 -->
                  <div
                    v-for="category in longVideoCategories"
                    :key="category.id"
                    class="van-tab"
                    :class="{ 'van-tab--active': currentCategory === `category_${category.id}` }"
                    @click="filterByCategory(`category_${category.id}`)"
                  >
                    <span class="van-tab__text">{{ category.name }}</span>
                  </div>
                  <div class="van-tabs__line" :style="getTabLineStyle()"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧功能按钮 -->
          <div class="van-nav-bar__right">
            <div class="search-btn" @click="toggleSearch">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </div>
            <div class="view-mode-toggle" @click="toggleViewMode">
              <svg v-if="viewMode === 'grid'" viewBox="0 0 24 24" fill="currentColor">
                <path d="M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 汉堡菜单弹窗 -->
    <div v-if="showHamburgerMenu" class="overlay" @click="closeHamburgerMenu"></div>
    <div v-if="showHamburgerMenu" class="options_box">
      <div class="triangle"></div>
      <ul>
        <a href="javascript:void(0);" @click="navigateToFavorites">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
          </svg>
          <span>收藏夹</span>
        </a>
        <a href="javascript:void(0);" @click="navigateToMessages">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
          </svg>
          <span>消息</span>
        </a>
        <a href="javascript:void(0);" @click="navigateToHistory">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
          </svg>
          <span>历史记录</span>
        </a>
        <a href="/scanCode">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M9.5 6.5v3h-3v-3h3M11 5H5v6h6V5zm-1.5 9.5v3h-3v-3h3M11 13H5v6h6v-6zm6.5-6.5v3h-3v-3h3M19 5h-6v6h6V5zm-6.5 9.5v3h-3v-3h3M13 13h-2v6h6v-2h-4v-4z"/>
          </svg>
          <span>扫一扫</span>
        </a>
        <a href="/navigation">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          <span>全部频道</span>
        </a>
        <a href="https://m.wyav.tv" target="_blank">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <span>午夜版</span>
        </a>
      </ul>
    </div>

    <!-- 搜索栏 -->
    <div v-if="showSearch" class="search-bar">
      <div class="search-container">
        <input
          ref="searchInput"
          v-model="searchKeyword"
          type="text"
          placeholder="搜索电影、电视剧、综艺..."
          @keyup.enter="performSearch"
        />
        <button @click="performSearch">搜索</button>
        <button @click="toggleSearch">取消</button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 面包屑导航 -->
      <div class="breadcrumb" v-if="searchKeyword || currentCategory !== 'all'">
        <span class="breadcrumb-item" @click="goHome">首页</span>
        <span class="breadcrumb-separator">></span>
        <span class="breadcrumb-item" @click="resetToAll">长视频</span>
        <span v-if="currentCategory !== 'all'" class="breadcrumb-separator">></span>
        <span v-if="currentCategory !== 'all'" class="breadcrumb-item active">{{ getCategoryName(currentCategory) }}</span>
        <span v-if="searchKeyword" class="breadcrumb-separator">></span>
        <span v-if="searchKeyword" class="breadcrumb-item active">搜索: {{ searchKeyword }}</span>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error">
        <p>{{ error }}</p>
        <button @click="retryLoad">重试</button>
      </div>

      <!-- 视频列表区域 -->
      <div v-else class="video-section">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="video-grid">
          <div
            v-for="video in displayVideos"
            :key="video.id"
            class="video-card"
            @click="playVideo(video)"
          >
            <div class="video-poster">
              <img
                :src="getImageUrl(video.cover_image || video.thumbnail, video.cover_image_backup)"
                :data-backup="video.cover_image_backup ? getImageUrl(video.cover_image_backup) : ''"
                :alt="video.title"
                @error="handleImageError"
                loading="lazy"
              />
              <div class="video-overlay">
                <div class="play-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="video-info">
              <h3 class="video-title">{{ video.title }}</h3>
              <div class="video-meta">
                <span class="view-count">{{ formatViewCount(video.view_count) }}次观看</span>
                <span class="upload-time">{{ formatDate(video.created_at) }}</span>
              </div>
              <div class="video-actions">
                <button
                  class="action-btn like-btn"
                  :class="{ active: video.isLiked }"
                  @click.stop="toggleLike(video)"
                  :disabled="video.isLiking"
                >
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                  <span>{{ formatCount(video.like_count || 0) }}</span>
                </button>
                <button
                  class="action-btn favorite-btn"
                  :class="{ active: video.isFavorited }"
                  @click.stop="toggleFavorite(video)"
                  :disabled="video.isFavoriting"
                >
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
                  </svg>
                  <span>收藏</span>
                </button>
                <button
                  class="action-btn share-btn"
                  @click.stop="shareVideo(video)"
                >
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
                  </svg>
                  <span>分享</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="video-list">
          <div
            v-for="video in displayVideos"
            :key="video.id"
            class="video-item"
            @click="playVideo(video)"
          >
            <img
              :src="getImageUrl(video.cover_image || video.thumbnail, video.cover_image_backup)"
              :data-backup="video.cover_image_backup ? getImageUrl(video.cover_image_backup) : ''"
              :alt="video.title"
              class="video-thumb"
              @error="handleImageError"
            />
            <div class="video-details">
              <h3 class="video-title">{{ video.title }}</h3>
              <p class="video-meta">{{ formatViewCount(video.view_count) }}次观看 · {{ formatDate(video.created_at) }}</p>
              <p class="video-desc">{{ video.description }}</p>
            </div>
          </div>
        </div>

        <!-- 自动加载更多 -->
        <div v-if="hasMore && !loading" class="auto-load-more" ref="loadMoreTrigger">
          <div v-if="loadingMore" class="loading-more">
            <div class="loading-spinner"></div>
            <p>正在加载更多视频...</p>
          </div>
          <div v-else class="load-more-hint">
            <p>滚动到底部自动加载更多</p>
          </div>
        </div>

        <!-- 无更多内容 -->
        <div v-if="!hasMore && displayVideos.length > 0" class="no-more">
          <p>没有更多内容了</p>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && !error && displayVideos.length === 0" class="empty">
          <p>暂无视频内容</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { videoApi } from '@/api/video'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const error = ref('')
const allVideos = ref<any[]>([])
const showSearch = ref(false)
const searchKeyword = ref('')
const currentCategory = ref('all')
const viewMode = ref<'grid' | 'list'>('grid')
const sortBy = ref('latest')
const currentPage = ref(1)
const totalCount = ref(0)
const pageSize = ref(24)
const hasMore = ref(true)
const searchInput = ref<HTMLInputElement>()
const showHamburgerMenu = ref(false)
const longVideoCategories = ref<any[]>([]) // 长视频分类
const loadMoreTrigger = ref<HTMLElement>() // 自动加载触发器

// 计算属性
const displayVideos = computed(() => {
  let filtered = allVideos.value

  // 如果有搜索关键词，进行搜索过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()
    filtered = filtered.filter(video => 
      video.title?.toLowerCase().includes(keyword) ||
      video.description?.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 方法
const getTabLineStyle = () => {
  const categories = ['all', 'recommend', 'movie', 'tv', 'variety', 'anime', 'sports', 'documentary', 'playlist', 'midnight']
  const activeIndex = categories.indexOf(currentCategory.value)
  if (activeIndex === -1) return { transform: 'translateX(0px)' }
  const offset = activeIndex * 60 + 30
  return { transform: `translateX(${offset}px) translateX(-50%)` }
}

const toggleHamburgerMenu = () => {
  showHamburgerMenu.value = !showHamburgerMenu.value
}

const closeHamburgerMenu = () => {
  showHamburgerMenu.value = false
}

const toggleSearch = () => {
  showSearch.value = !showSearch.value
  if (showSearch.value) {
    setTimeout(() => searchInput.value?.focus(), 100)
  }
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}

const filterByCategory = (category: string) => {
  currentCategory.value = category
  currentPage.value = 1
  searchKeyword.value = ''
  loadData()
}

const playVideo = (video: any) => {
  router.push(`/video/${video.id}`)
}

// 加载长视频分类
const loadLongVideoCategories = async () => {
  try {
    console.log('🔄 开始加载长视频分类...')
    const response = await videoApi.getCategories()
    console.log('📡 分类API响应:', response)

    if (response.success === true) {
      console.log('📋 所有分类数据:', response.data)
      // 过滤出长视频分类
      longVideoCategories.value = response.data.filter((cat: any) => {
        console.log(`🔍 检查分类: ${cat.name}, video_type: ${cat.video_type}`)
        return cat.video_type === 'long'
      })
      console.log('✅ 长视频分类加载成功:', longVideoCategories.value)
    } else {
      console.error('❌ 分类API返回错误:', response)
    }
  } catch (error) {
    console.error('❌ 加载长视频分类失败:', error)
  }
}

const performSearch = () => {
  if (searchKeyword.value.trim()) {
    currentPage.value = 1
    loadData()
  }
}

const navigateToFavorites = () => {
  closeHamburgerMenu()
  console.log('导航到收藏夹')
}

const navigateToMessages = () => {
  closeHamburgerMenu()
  console.log('导航到消息')
}

const navigateToHistory = () => {
  closeHamburgerMenu()
  console.log('导航到历史记录')
}



const goHome = () => {
  router.push('/')
}

const resetToAll = () => {
  currentCategory.value = 'all'
  searchKeyword.value = ''
  currentPage.value = 1
  loadData()
}

const getCategoryName = (category: string) => {
  const names: Record<string, string> = {
    all: '全部',
    recommend: '推荐',
    movie: '电影',
    tv: '电视剧',
    variety: '综艺',
    anime: '动漫',
    sports: '体育',
    documentary: '纪录片',
    playlist: '片单',
    midnight: '午夜版'
  }
  return names[category] || category
}

const formatViewCount = (count: number) => {
  if (!count) return '0'
  if (count >= 100000000) return `${(count / 100000000).toFixed(1)}亿`
  if (count >= 10000) return `${(count / 10000).toFixed(1)}万`
  return count.toString()
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  return date.toLocaleDateString()
}

// 获取图片URL - 支持双备份机制
const getImageUrl = (imagePath: string, backupPath?: string) => {
  console.log('封面图片路径:', imagePath, '备份路径:', backupPath) // 调试日志

  // 尝试主封面图
  if (imagePath) {
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath
    }
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`
    return `${apiBaseUrl}${normalizedPath}`
  }

  // 尝试备份封面图
  if (backupPath) {
    if (backupPath.startsWith('http://') || backupPath.startsWith('https://')) {
      return backupPath
    }
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    const normalizedPath = backupPath.startsWith('/') ? backupPath : `/${backupPath}`
    return `${apiBaseUrl}${normalizedPath}`
  }

  // 都没有则使用默认图片
  console.log('封面图片路径为空，使用默认图片')
  return '/default-cover.svg'
}

// 处理图片加载错误 - 尝试备份图片
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  const originalSrc = img.src

  // 如果当前显示的是主图片，尝试备份图片
  if (img.dataset.backup && img.src !== img.dataset.backup) {
    console.log('主封面图加载失败，尝试备份封面图:', img.dataset.backup)
    img.src = img.dataset.backup
    return
  }

  // 如果备份图片也失败，使用默认图片
  if (!img.src.includes('default-cover.svg')) {
    console.log('备份封面图也加载失败，使用默认图片')
    img.src = '/default-cover.svg'
  }
}

const loadData = async () => {
  try {
    loading.value = true
    error.value = ''
    currentPage.value = 1
    hasMore.value = true

    const params: any = {
      video_type: 'long',
      sort: sortBy.value,
      limit: pageSize.value,
      page: 1
    }

    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }

    // 根据不同的标签设置不同的排序和过滤
    if (currentCategory.value === 'latest') {
      params.sort = 'latest'
    } else if (currentCategory.value === 'hot') {
      params.sort = 'popular'
    }

    // 如果选择了特定分类，添加分类过滤
    if (currentCategory.value.startsWith('category_')) {
      const categoryId = currentCategory.value.replace('category_', '')
      params.category_id = parseInt(categoryId)
    }

    const response = await videoApi.getVideoList(params)

    if (response.code === 200 && response.data?.data) {
      const videoList = response.data.data
      if (videoList.length > 0) {
        const processedVideos = videoList.map((video: any) => {
          console.log('原始视频数据:', video) // 调试日志
          return {
            ...video,
            // 确保封面图片字段正确映射
            cover_image: video.cover_image || video.thumbnail,
            is_vip: Math.random() > 0.7,
            is_new: Math.random() > 0.8,
            is_hot: Math.random() > 0.9,
            quality: ['HD', '4K', '蓝光'][Math.floor(Math.random() * 3)],
            rating: video.rating || (Math.random() * 3 + 7).toFixed(1),
            year: video.year || new Date(video.created_at).getFullYear(),
            region: video.region || ['大陆', '香港', '台湾', '日本', '韩国', '美国'][Math.floor(Math.random() * 6)],
            watch_progress: Math.random() > 0.7 ? Math.floor(Math.random() * 100) : null,
            // 交互状态
            isLiked: video.is_liked || false,
            isFavorited: video.is_favorited || false,
            isLiking: false,
            isFavoriting: false
          }
        })

        allVideos.value = processedVideos
        hasMore.value = videoList.length === pageSize.value
      } else {
        allVideos.value = []
        hasMore.value = false
      }
      totalCount.value = response.data.total || 0
    } else {
      throw new Error(response.message || '加载失败')
    }
  } catch (err: any) {
    error.value = err.message || '加载视频失败'
    console.error('加载视频失败:', err)
  } finally {
    loading.value = false
  }
}

const loadMore = async () => {
  if (loadingMore.value || !hasMore.value) return

  try {
    loadingMore.value = true
    currentPage.value++

    const params: any = {
      video_type: 'long',
      sort: sortBy.value,
      limit: pageSize.value,
      page: currentPage.value
    }

    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }

    if (currentCategory.value !== 'all' && currentCategory.value !== 'recommend') {
      const categoryMap: Record<string, string> = {
        'movie': 'movie',
        'tv': 'tv',
        'variety': 'variety',
        'anime': 'anime',
        'sports': 'sports',
        'documentary': 'documentary',
        'playlist': 'playlist',
        'midnight': 'adult'
      }
      if (categoryMap[currentCategory.value]) {
        params.category = categoryMap[currentCategory.value]
      }
    }

    if (currentCategory.value === 'recommend') {
      params.sort = 'view_count'
      params.order = 'desc'
    }

    const response = await videoApi.getVideoList(params)

    if (response.code === 200 && response.data?.data) {
      const videoList = response.data.data
      if (videoList.length > 0) {
        const processedVideos = videoList.map((video: any) => ({
          ...video,
          is_vip: Math.random() > 0.7,
          is_new: Math.random() > 0.8,
          is_hot: Math.random() > 0.9,
          quality: ['HD', '4K', '蓝光'][Math.floor(Math.random() * 3)],
          rating: video.rating || (Math.random() * 3 + 7).toFixed(1),
          year: video.year || new Date(video.created_at).getFullYear(),
          region: video.region || ['大陆', '香港', '台湾', '日本', '韩国', '美国'][Math.floor(Math.random() * 6)],
          watch_progress: Math.random() > 0.7 ? Math.floor(Math.random() * 100) : null,
          // 交互状态
          isLiked: video.is_liked || false,
          isFavorited: video.is_favorited || false,
          isLiking: false,
          isFavoriting: false
        }))

        allVideos.value.push(...processedVideos)
        hasMore.value = videoList.length === pageSize.value
      } else {
        hasMore.value = false
      }
    } else {
      hasMore.value = false
    }
  } catch (err: any) {
    console.error('加载更多失败:', err)
    hasMore.value = false
  } finally {
    loadingMore.value = false
  }
}

const retryLoad = () => {
  loadData()
}

// 自动加载更多的 Intersection Observer
let observer: IntersectionObserver | null = null

const setupAutoLoad = () => {
  if (!loadMoreTrigger.value) return

  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting && hasMore.value && !loadingMore.value && !loading.value) {
        console.log('🔄 触发自动加载更多')
        loadMore()
      }
    },
    {
      rootMargin: '100px' // 提前100px开始加载
    }
  )

  observer.observe(loadMoreTrigger.value)
}

const cleanupAutoLoad = () => {
  if (observer) {
    observer.disconnect()
    observer = null
  }
}

// 生命周期
onMounted(async () => {
  // 先加载长视频分类
  await loadLongVideoCategories()
  // 然后加载视频数据
  await loadData()
  // 设置自动加载
  setTimeout(() => {
    setupAutoLoad()
  }, 100) // 延迟一点确保DOM已渲染
})

onUnmounted(() => {
  cleanupAutoLoad()
})

// 视频交互功能
const toggleLike = async (video: any) => {
  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再点赞')
    router.push('/login')
    return
  }

  // 防止重复点击
  if (video.isLiking) return
  video.isLiking = true

  try {
    const wasLiked = video.isLiked
    const originalLikes = video.like_count

    // 乐观更新UI
    video.isLiked = !wasLiked
    video.like_count += video.isLiked ? 1 : -1

    // 调用API
    if (video.isLiked) {
      const response = await videoApi.like(video.id)
      if (response.code === 200) {
        video.like_count = response.data.likeCount || video.like_count
        ElMessage.success('点赞成功')
      } else {
        throw new Error(response.message || '点赞失败')
      }
    } else {
      const response = await videoApi.unlike(video.id)
      if (response.code === 200) {
        video.like_count = response.data.likeCount || video.like_count
        ElMessage.success('取消点赞成功')
      } else {
        throw new Error(response.message || '取消点赞失败')
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    // 回滚UI状态
    video.isLiked = !video.isLiked
    video.like_count += video.isLiked ? 1 : -1
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    video.isLiking = false
  }
}

const toggleFavorite = async (video: any) => {
  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再收藏')
    router.push('/login')
    return
  }

  // 防止重复点击
  if (video.isFavoriting) return
  video.isFavoriting = true

  try {
    const wasFavorited = video.isFavorited

    // 乐观更新UI
    video.isFavorited = !wasFavorited

    // 调用API
    if (video.isFavorited) {
      const response = await videoApi.favorite(video.id)
      if (response.code === 200) {
        ElMessage.success('收藏成功')
      } else {
        throw new Error(response.message || '收藏失败')
      }
    } else {
      const response = await videoApi.unfavorite(video.id)
      if (response.code === 200) {
        ElMessage.success('取消收藏成功')
      } else {
        throw new Error(response.message || '取消收藏失败')
      }
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    // 回滚UI状态
    video.isFavorited = !video.isFavorited
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    video.isFavoriting = false
  }
}

const shareVideo = (video: any) => {
  // 检查是否支持Web Share API
  if (navigator.share) {
    navigator.share({
      title: video.title,
      text: video.description,
      url: window.location.href
    }).catch(err => {
      console.log('分享失败:', err)
      fallbackShare(video)
    })
  } else {
    fallbackShare(video)
  }
}

const fallbackShare = (video: any) => {
  // 复制链接到剪贴板
  const url = window.location.href
  if (navigator.clipboard) {
    navigator.clipboard.writeText(url).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败')
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = url
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('链接已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}

const formatCount = (count: number): string => {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M'
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K'
  }
  return count.toString()
}
</script>

<style scoped>
.long-videos {
  min-height: 100vh;
  background: #000000;
  color: #ffffff;
}

/* 新的导航栏样式 */
.home-nav {
  height: 5px;
}

.van-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 48px;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.van-nav-bar__content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

.van-nav-bar__left {
  flex: none;
}

.hamburger-menu {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
}

.hamburger-menu:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
}

.hamburger-menu svg {
  width: 20px;
  height: 20px;
}

.van-nav-bar__title {
  flex: 1;
  text-align: center;
  overflow: hidden;
}

.van-tabs {
  width: 100%;
}

.van-tabs__wrap {
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.van-tabs__wrap::-webkit-scrollbar {
  display: none;
}

.van-tabs__nav {
  display: flex;
  position: relative;
  background: transparent;
}

.van-tab {
  flex: none;
  padding: 0 16px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.van-tab:hover {
  color: rgba(255, 255, 255, 0.9);
}

.van-tab--active {
  color: #ff6b6b !important;
  font-weight: 600;
}

.van-tabs__line {
  position: absolute;
  bottom: 0;
  height: 2px;
  background: #ff6b6b;
  border-radius: 1px;
  transition: all 0.3s ease;
  width: 20px;
}

.van-nav-bar__right {
  flex: none;
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-btn, .view-mode-toggle {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
}

.search-btn:hover, .view-mode-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
}

.search-btn svg, .view-mode-toggle svg {
  width: 18px;
  height: 18px;
}

/* 汉堡菜单弹窗样式 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.options_box {
  position: fixed;
  top: 60px;
  right: 16px;
  background: rgba(20, 20, 20, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1002;
  min-width: 180px;
  overflow: hidden;
}

.triangle {
  position: absolute;
  top: -6px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(20, 20, 20, 0.95);
}

.options_box ul {
  list-style: none;
  padding: 8px 0;
  margin: 0;
}

.options_box a {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.options_box a:last-child {
  border-bottom: none;
}

.options_box a:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #ff6b6b;
}

.options_box svg {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.options_box span {
  font-size: 14px;
  font-weight: 500;
}

/* 搜索栏样式 */
.search-bar {
  position: fixed;
  top: 48px;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 999;
}

.search-container {
  display: flex;
  gap: 12px;
  max-width: 600px;
  margin: 0 auto;
}

.search-container input {
  flex: 1;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
}

.search-container input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-container button {
  padding: 12px 20px;
  background: #ff6b6b;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-container button:hover {
  background: #ff5252;
  transform: translateY(-1px);
}

/* 主要内容区域 */
.main-content {
  padding-top: 10px;
  min-height: calc(100vh - 10px);
}

/* 面包屑导航 */
.breadcrumb {
  padding: 8px 16px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.breadcrumb-item {
  cursor: pointer;
  transition: color 0.3s ease;
}

.breadcrumb-item:hover {
  color: #ff6b6b;
}

.breadcrumb-item.active {
  color: #ff6b6b;
}

.breadcrumb-separator {
  margin: 0 8px;
}

/* 加载和错误状态 */
.loading, .error, .empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 107, 107, 0.2);
  border-top: 3px solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error button {
  padding: 12px 24px;
  background: #ff6b6b;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.error button:hover {
  background: #ff5252;
  transform: translateY(-1px);
}

/* 自动加载更多样式 */
.auto-load-more {
  padding: 40px 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-more .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.load-more-hint p {
  margin: 0;
  font-size: 14px;
  opacity: 0.7;
}

/* 视频网格 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px;
}

.video-card {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  transform: translateY(0);
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 107, 107, 0.3);
}

.video-poster {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  overflow: hidden;
  background: #1a1a1a;
}

.video-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .video-poster img {
  transform: scale(1.05);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

.video-card:hover .video-overlay {
  opacity: 1;
}

.play-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 107, 107, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  transform: scale(0.8);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.video-card:hover .play-icon {
  transform: scale(1);
  background: rgba(255, 107, 107, 1);
}

/* 强制重置非悬停状态 */
.video-card:not(:hover) .video-overlay {
  opacity: 0 !important;
}

.video-card:not(:hover) .play-icon {
  transform: scale(0.8) !important;
}

.video-info {
  padding: 12px;
}

.video-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 6px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

/* 视频列表 */
.video-list {
  padding: 20px;
}

.video-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.video-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 107, 107, 0.3);
}

.video-thumb {
  width: 160px;
  height: 90px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.video-details {
  flex: 1;
}

.video-details .video-title {
  font-size: 18px;
  margin-bottom: 8px;
}

.video-details .video-meta {
  margin-bottom: 8px;
}

.video-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 加载更多和无更多内容 */
.load-more, .no-more {
  text-align: center;
  padding: 40px 20px;
}

.no-more p {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

/* 视频交互按钮样式 */
.video-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

.action-btn.like-btn.active {
  background: rgba(255, 45, 85, 0.2);
  color: #ff2d55;
  border: 1px solid rgba(255, 45, 85, 0.3);
}

.action-btn.favorite-btn.active {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.action-btn.share-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

/* ================================
   响应式设计优化
   ================================ */

/* 超小屏幕 (手机竖屏) - 320px ~ 575px */
@media (max-width: 575.98px) {
  .van-nav-bar__content {
    padding: 0 8px;
  }

  .van-tab {
    padding: 0 8px;
    font-size: 12px;
    min-width: 60px;
  }

  .hamburger-menu, .search-btn, .view-mode-toggle {
    width: 28px;
    height: 28px;
  }

  .hamburger-menu svg, .search-btn svg, .view-mode-toggle svg {
    width: 16px;
    height: 16px;
  }

  .video-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    padding: 8px;
  }

  .video-card {
    border-radius: 8px;
  }

  .video-poster {
    aspect-ratio: 3/4;
  }

  .video-info {
    padding: 8px;
  }

  .video-title {
    font-size: 12px;
    -webkit-line-clamp: 2;
  }

  .video-meta {
    font-size: 10px;
  }

  .video-actions {
    gap: 6px;
    margin-top: 6px;
    padding-top: 6px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 10px;
    border-radius: 12px;
  }

  .action-btn svg {
    width: 12px;
    height: 12px;
  }

  .video-item {
    flex-direction: column;
    gap: 8px;
    padding: 12px;
  }

  .video-thumb {
    width: 100%;
    height: 100px;
  }

  .video-details .video-title {
    font-size: 14px;
  }

  .breadcrumb {
    padding: 8px 12px;
    font-size: 11px;
  }

  .search-container {
    flex-direction: column;
    gap: 8px;
  }

  .search-container input {
    padding: 10px 12px;
    font-size: 14px;
  }

  .search-container button {
    padding: 10px 16px;
    font-size: 13px;
  }

  .options_box {
    right: 8px;
    min-width: 160px;
  }

  .options_box a {
    padding: 10px 12px;
  }

  .options_box svg {
    width: 18px;
    height: 18px;
    margin-right: 10px;
  }

  .options_box span {
    font-size: 13px;
  }
}

/* 小屏幕 (手机横屏) - 576px ~ 767px */
@media (min-width: 576px) and (max-width: 767.98px) {
  .van-nav-bar__content {
    padding: 0 10px;
  }

  .van-tab {
    padding: 0 10px;
    font-size: 13px;
  }

  .video-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 10px;
  }

  .video-info {
    padding: 10px;
  }

  .video-title {
    font-size: 13px;
  }

  .video-actions {
    gap: 8px;
  }

  .action-btn {
    padding: 5px 10px;
    font-size: 11px;
  }

  .action-btn svg {
    width: 14px;
    height: 14px;
  }

  .video-item {
    flex-direction: row;
    gap: 12px;
  }

  .video-thumb {
    width: 140px;
    height: 80px;
  }

  .breadcrumb {
    padding: 10px 14px;
    font-size: 12px;
  }
}

/* 中等屏幕 (平板竖屏) - 768px ~ 991px */
@media (min-width: 768px) and (max-width: 991.98px) {
  .van-nav-bar__content {
    padding: 0 12px;
  }

  .van-tab {
    padding: 0 12px;
    font-size: 13px;
  }

  .video-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 12px;
  }

  .video-info {
    padding: 12px;
  }

  .video-title {
    font-size: 14px;
  }

  .video-item {
    flex-direction: row;
    gap: 14px;
  }

  .video-thumb {
    width: 160px;
    height: 90px;
  }

  .breadcrumb {
    padding: 12px 16px;
    font-size: 12px;
  }

  .search-container {
    flex-direction: row;
  }
}

/* 大屏幕 (平板横屏/小桌面) - 992px ~ 1199px */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .video-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 14px;
    padding: 14px;
  }

  .video-card:hover {
    transform: translateY(-3px);
  }

  .play-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

/* 超大屏幕 (桌面) - 1200px+ */
@media (min-width: 1200px) {
  .video-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 16px;
    padding: 16px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .video-card:hover {
    transform: translateY(-5px);
  }

  .play-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .main-content {
    max-width: 1400px;
    margin: 0 auto;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .video-card:hover {
    transform: none;
    box-shadow: none;
    border-color: rgba(255, 255, 255, 0.05);
  }

  .video-card:hover .video-poster img {
    transform: none;
  }

  .video-card:hover .video-overlay {
    opacity: 0;
  }

  .video-card:active {
    transform: scale(0.98);
  }

  .action-btn:hover {
    transform: none;
  }

  .action-btn:active {
    transform: scale(0.95);
  }

  .hamburger-menu:hover,
  .search-btn:hover,
  .view-mode-toggle:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .van-nav-bar {
    height: 40px;
  }

  .van-tab {
    height: 40px;
  }

  .main-content {
    padding-top: 5px;
  }

  .video-grid {
    padding: 8px;
    gap: 8px;
  }

  .video-info {
    padding: 8px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-resolution: 2dppx) {
  .video-poster img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
</style>
