<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Log;
use think\facade\Config;
use think\facade\Cache;
use app\service\DatabaseService;

/**
 * 视频加密服务
 * 
 * 提供HLS视频切片的AES-128加密功能，包括：
 * - 加密密钥的生成和管理
 * - 视频切片的加密处理
 * - 密钥的定时轮换机制
 * - 密钥获取的安全接口
 * - 加密状态的监控和日志
 */
class VideoEncryptionService
{
    /**
     * 加密算法类型
     */
    private string $encryptionMethod = 'AES-128-CBC';

    /**
     * 密钥长度（字节）
     */
    private int $keyLength = 16;

    /**
     * IV长度（字节）
     */
    private int $ivLength = 16;

    /**
     * 密钥轮换间隔（秒）
     * 默认24小时轮换一次
     */
    private int $keyRotationInterval;

    /**
     * 密钥存储前缀
     */
    private string $keyPrefix = 'video_encryption_key_';

    /**
     * 数据库管理服务
     */
    private DatabaseService $dbManager;

    public function __construct()
    {
        // 从配置中获取密钥轮换间隔，默认24小时
        $this->keyRotationInterval = (int) Config::get('video.encryption.key_rotation_interval', 86400);

        // 初始化数据库管理服务
        $this->dbManager = new DatabaseService();

        Log::info('视频加密服务初始化', [
            'encryption_method' => $this->encryptionMethod,
            'key_length' => $this->keyLength,
            'rotation_interval' => $this->keyRotationInterval
        ]);
    }

    /**
     * 为视频生成加密密钥
     * 
     * 为指定视频生成唯一的加密密钥，并存储到数据库和缓存中
     * 密钥包含密钥值、IV向量、创建时间等信息
     * 
     * @param int $videoId 视频ID
     * @return array 密钥信息
     */
    public function generateEncryptionKey(int $videoId): array
    {
        // 生成随机密钥和IV向量
        $key = $this->generateRandomKey();
        $iv = $this->generateRandomIV();
        $keyId = $this->generateKeyId($videoId);
        
        $keyInfo = [
            'key_id' => $keyId,
            'video_id' => $videoId,
            'encryption_key' => base64_encode($key),
            'iv' => base64_encode($iv),
            'algorithm' => $this->encryptionMethod, // 使用正确的字段名
            'key_uri' => '/api/video/encryption/key/' . $keyId, // 添加密钥URI
            'is_active' => 1, // 使用正确的字段名
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', time() + $this->keyRotationInterval)
        ];

        // 存储到数据库
        $this->storeKeyToDatabase($keyInfo);
        
        // 存储到缓存（用于快速访问）
        $this->storeKeyToCache($keyInfo);
        
        Log::info('为视频生成加密密钥', [
            'video_id' => $videoId,
            'key_id' => $keyId,
            'expires_at' => $keyInfo['expires_at']
        ]);

        return $keyInfo;
    }

    /**
     * 加密HLS切片文件
     * 
     * 使用AES-128算法对视频切片进行加密
     * 生成加密后的TS文件和对应的密钥文件
     * 
     * @param string $inputFile 输入的TS文件路径
     * @param string $outputFile 输出的加密TS文件路径
     * @param array $keyInfo 加密密钥信息
     * @return bool 加密是否成功
     */
    public function encryptTSFile(string $inputFile, string $outputFile, array $keyInfo): bool
    {
        try {
            // 检查输入文件是否存在
            if (!file_exists($inputFile)) {
                throw new \Exception('输入文件不存在: ' . $inputFile);
            }

            // 读取原始文件内容
            $originalContent = file_get_contents($inputFile);
            if ($originalContent === false) {
                throw new \Exception('无法读取输入文件: ' . $inputFile);
            }

            // 解码密钥和IV
            $key = base64_decode($keyInfo['encryption_key']);
            $iv = base64_decode($keyInfo['iv']);

            // 使用AES-128-CBC加密内容
            $encryptedContent = openssl_encrypt(
                $originalContent,
                $this->encryptionMethod,
                $key,
                OPENSSL_RAW_DATA,
                $iv
            );

            if ($encryptedContent === false) {
                throw new \Exception('文件加密失败');
            }

            // 写入加密后的文件
            $result = file_put_contents($outputFile, $encryptedContent);
            if ($result === false) {
                throw new \Exception('无法写入加密文件: ' . $outputFile);
            }

            Log::info('TS文件加密成功', [
                'input_file' => basename($inputFile),
                'output_file' => basename($outputFile),
                'original_size' => strlen($originalContent),
                'encrypted_size' => strlen($encryptedContent),
                'key_id' => $keyInfo['key_id']
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('TS文件加密失败', [
                'input_file' => $inputFile,
                'output_file' => $outputFile,
                'error' => $e->getMessage(),
                'key_id' => $keyInfo['key_id'] ?? 'unknown'
            ]);

            return false;
        }
    }

    /**
     * 生成HLS加密播放列表
     * 
     * 修改m3u8播放列表，添加加密相关的标签
     * 包含密钥获取URL和加密方法信息
     * 
     * @param string $playlistPath 播放列表文件路径
     * @param array $keyInfo 加密密钥信息
     * @param string $keyUrl 密钥获取URL
     * @return bool 是否成功
     */
    public function generateEncryptedPlaylist(string $playlistPath, array $keyInfo, string $keyUrl): bool
    {
        try {
            // 读取原始播放列表
            $content = file_get_contents($playlistPath);
            if ($content === false) {
                throw new \Exception('无法读取播放列表文件: ' . $playlistPath);
            }

            $lines = explode("\n", $content);
            $newLines = [];
            $keyTagAdded = false;

            foreach ($lines as $line) {
                $line = trim($line);
                
                // 在第一个切片前添加加密标签
                if (!$keyTagAdded && strpos($line, '.ts') !== false) {
                    // 添加EXT-X-KEY标签
                    $keyTag = sprintf(
                        '#EXT-X-KEY:METHOD=AES-128,URI="%s",IV=0x%s',
                        $keyUrl,
                        bin2hex(base64_decode($keyInfo['iv']))
                    );
                    $newLines[] = $keyTag;
                    $keyTagAdded = true;
                }
                
                $newLines[] = $line;
            }

            // 写入修改后的播放列表
            $newContent = implode("\n", $newLines);
            $result = file_put_contents($playlistPath, $newContent);
            
            if ($result === false) {
                throw new \Exception('无法写入播放列表文件: ' . $playlistPath);
            }

            Log::info('生成加密播放列表成功', [
                'playlist_path' => basename($playlistPath),
                'key_url' => $keyUrl,
                'key_id' => $keyInfo['key_id']
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('生成加密播放列表失败', [
                'playlist_path' => $playlistPath,
                'error' => $e->getMessage(),
                'key_id' => $keyInfo['key_id'] ?? 'unknown'
            ]);

            return false;
        }
    }

    /**
     * 获取视频的加密密钥
     * 
     * 根据密钥ID获取对应的加密密钥信息
     * 首先从缓存中查找，如果没有则从数据库中获取
     * 
     * @param string $keyId 密钥ID
     * @return array|null 密钥信息
     */
    public function getEncryptionKey(string $keyId): ?array
    {
        // 先从缓存中获取
        $cacheKey = $this->keyPrefix . $keyId;
        $keyInfo = Cache::get($cacheKey);
        
        if ($keyInfo) {
            Log::debug('从缓存获取加密密钥', ['key_id' => $keyId]);
            return $keyInfo;
        }

        // 从数据库中获取
        $keyInfo = $this->dbManager->table('video_encryption_keys')
            ->where('key_id', $keyId)
            ->where('is_active', 1)
            ->find();

        if ($keyInfo) {
            // 存储到缓存
            $this->storeKeyToCache($keyInfo);
            
            Log::debug('从数据库获取加密密钥', ['key_id' => $keyId]);
            return $keyInfo;
        }

        Log::warning('未找到加密密钥', ['key_id' => $keyId]);
        return null;
    }

    /**
     * 轮换过期的加密密钥
     * 
     * 定期执行的任务，用于轮换过期的加密密钥
     * 将过期的密钥标记为inactive，并为相关视频生成新密钥
     * 
     * @return array 轮换结果统计
     */
    public function rotateExpiredKeys(): array
    {
        $stats = [
            'expired_keys' => 0,
            'rotated_keys' => 0,
            'errors' => 0
        ];

        try {
            // 查找过期的密钥
            $expiredKeys = $this->dbManager->table('video_encryption_keys')
                ->where('expires_at', '<', date('Y-m-d H:i:s'))
                ->where('status', 'active')
                ->select();

            $stats['expired_keys'] = count($expiredKeys);

            foreach ($expiredKeys as $expiredKey) {
                try {
                    // 标记旧密钥为过期
                    $this->dbManager->writeTable('video_encryption_keys')
                        ->where('id', $expiredKey['id'])
                        ->update([
                            'status' => 'expired',
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    // 为视频生成新密钥
                    $this->generateEncryptionKey($expiredKey['video_id']);
                    
                    // 清除缓存中的旧密钥
                    Cache::delete($this->keyPrefix . $expiredKey['key_id']);
                    
                    $stats['rotated_keys']++;
                    
                    Log::info('密钥轮换成功', [
                        'old_key_id' => $expiredKey['key_id'],
                        'video_id' => $expiredKey['video_id']
                    ]);

                } catch (\Exception $e) {
                    $stats['errors']++;
                    Log::error('密钥轮换失败', [
                        'key_id' => $expiredKey['key_id'],
                        'video_id' => $expiredKey['video_id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('密钥轮换任务完成', $stats);
            return $stats;

        } catch (\Exception $e) {
            Log::error('密钥轮换任务失败', [
                'error' => $e->getMessage(),
                'stats' => $stats
            ]);
            
            return $stats;
        }
    }

    /**
     * 生成随机密钥
     * 
     * @return string 16字节的随机密钥
     */
    private function generateRandomKey(): string
    {
        return random_bytes($this->keyLength);
    }

    /**
     * 生成随机IV向量
     * 
     * @return string 16字节的随机IV
     */
    private function generateRandomIV(): string
    {
        return random_bytes($this->ivLength);
    }

    /**
     * 生成密钥ID
     * 
     * @param int $videoId 视频ID
     * @return string 密钥ID
     */
    private function generateKeyId(int $videoId): string
    {
        return 'key_' . $videoId . '_' . time() . '_' . bin2hex(random_bytes(4));
    }

    /**
     * 将密钥存储到数据库
     * 
     * @param array $keyInfo 密钥信息
     * @return void
     */
    private function storeKeyToDatabase(array $keyInfo): void
    {
        $this->dbManager->writeTable('video_encryption_keys')->insert($keyInfo);
    }

    /**
     * 将密钥存储到缓存
     * 
     * @param array $keyInfo 密钥信息
     * @return void
     */
    private function storeKeyToCache(array $keyInfo): void
    {
        $cacheKey = $this->keyPrefix . $keyInfo['key_id'];
        $ttl = strtotime($keyInfo['expires_at']) - time();
        Cache::set($cacheKey, $keyInfo, $ttl);
    }
}
