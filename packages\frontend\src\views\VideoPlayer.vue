<template>
  <div class="video-player-page">
    <!-- 顶部导航栏 -->
    <div class="header-nav">
      <button class="back-btn" @click="goBack">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
      </button>
      <div class="header-info">
        <span class="video-title">{{ videoData?.title || '加载中...' }}</span>
      </div>
      <div class="header-spacer"></div>
    </div>

    <!-- 视频播放器容器 -->
    <div class="player-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>视频加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-overlay">
        <div class="error-content">
          <div class="error-icon">⚠️</div>
          <h3>播放失败</h3>
          <p>{{ error }}</p>
          <div class="error-actions">
            <button @click="retryLoad" class="retry-btn">重试</button>
            <button @click="tryAlternativeSource" class="alternative-btn" v-if="hasAlternativeSource">
              尝试其他播放源
            </button>
          </div>
          <!-- 显示调试信息 -->
          <div v-if="debugInfo" class="debug-info">
            <details>
              <summary>调试信息</summary>
              <pre>{{ debugInfo }}</pre>
            </details>
          </div>
        </div>
      </div>

      <!-- Video.js播放器组件 -->
      <VideoPlayerComponent
        v-if="!loading && !error && videoUrl"
        ref="videoPlayerRef"
        :src="videoUrl"
        :poster="videoPoster"
        :autoplay="false"
        :controls="true"
        :fluid="true"
        :responsive="true"
        :skip-ad="playerConfig.skipAd"
        :ad-duration="playerConfig.adDuration"
        :auto-skip="playerConfig.autoSkip"
        :smart-detection="playerConfig.smartDetection"
        :ad-keywords="playerConfig.adKeywords"
        :max-ad-duration="playerConfig.maxAdDuration"
        :video-ad-info="videoAdInfo"
        @ready="onPlayerReady"
        @play="onPlay"
        @pause="onPause"
        @ended="onEnded"
        @error="onVideoError"
        @timeupdate="onTimeUpdate"
        @loadstart="onLoadStart"
        @loadeddata="onLoadedMetadata"
        @canplay="onCanPlay"
        @volumechange="onVolumeChange"
      />
    </div>

    <!-- 视频信息区域 -->
    <div class="video-info-section">
      <!-- 视频标题和基本信息 -->
      <div class="video-header">
        <h1 class="video-title">{{ videoData?.title }}</h1>

      </div>

      <!-- 视频描述 -->
      <div class="video-description" v-if="videoData?.description">
        <p>{{ videoData.description }}</p>
      </div>



      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <button class="action-btn" @click="toggleLike">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M1 21h4V9H1v12zm22-11c0-1.1-.9-2-2-2h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 1 7.59 7.59C7.22 7.95 7 8.45 7 9v10c0 1.1.9 2 2 2h9c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73v-2z"/>
          </svg>
          <span>{{ videoData?.likes || '80' }}</span>
        </button>

        <button class="action-btn" @click="toggleFavorite">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z"/>
          </svg>
          <span>{{ videoData?.favorites || '27' }}</span>
        </button>

        <button class="action-btn" @click="toggleCollect">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
          </svg>
          <span>{{ videoData?.collections || '15' }}</span>
        </button>

        <button class="action-btn" @click="shareVideo">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>
          </svg>
          <span>分享</span>
        </button>
      </div>
    </div>


    <!-- 相关视频推荐 -->
    <div class="related-content">
      <h3>相关推荐</h3>
      <div class="related-videos">
        <div
          v-for="video in relatedVideos"
          :key="video.id"
          class="video-card"
          @click="playRelatedVideo(video)"
        >
          <div class="video-poster">
            <img
              :src="getImageUrl(video.thumbnail || video.cover_image, video.cover_image_backup)"
              :data-backup="video.cover_image_backup ? getImageUrl(video.cover_image_backup) : ''"
              :alt="video.title"
              @error="handleImageError"
              loading="lazy"
            />
            <div class="video-overlay">
              <div class="play-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
            <div class="video-duration">{{ video.duration }}</div>
          </div>
          <div class="video-info">
            <h3 class="video-title">{{ video.title }}</h3>
            <div class="video-meta">
              <span class="view-count">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
                {{ video.views }}
              </span>
              <span class="upload-time">{{ video.uploadTime || '刚刚' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { videoApi } from '@/api/video'
import VideoPlayerComponent from '@/components/VideoPlayer.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const error = ref('')
const videoData = ref<any>(null)
const videoUrl = ref('')
const videoPoster = ref('')
const debugInfo = ref('')
const hasAlternativeSource = ref(false)
const currentSourceIndex = ref(0)
const availableSources = ref<string[]>([])

const relatedVideos = ref<any[]>([])

// 视频广告信息
const videoAdInfo = ref<any>(null)

// 播放器配置
const playerConfig = ref({
  skipAd: true,
  adDuration: 17,
  showSkipButton: true,
  skipButtonDelay: 3,
  autoSkip: true,
  smartDetection: true,
  adKeywords: '澳门,新葡京,娱乐城,赌场,博彩',
  maxAdDuration: 30
})

// 视频播放器引用
const videoPlayerRef = ref<any>()



// 返回上一页
const goBack = () => {
  router.back()
}

// 获取播放器配置
const loadPlayerConfig = async () => {
  try {
    // 使用默认配置，避免API请求失败
    playerConfig.value = {
      skipAd: true,
      adDuration: 17,
      showSkipButton: true,
      skipButtonDelay: 3,
      autoSkip: true,
      smartDetection: true,
      adKeywords: '澳门,新葡京,娱乐城,赌场,博彩',
      maxAdDuration: 30
    }
    console.log('📺 使用默认播放器配置:', playerConfig.value)
  } catch (error) {
    console.warn('⚠️ 播放器配置加载失败，使用默认配置:', error)
  }
}

// 加载视频数据
const loadVideoData = async () => {
  try {
    loading.value = true
    error.value = ''

    const videoId = route.params.id as string
    console.log('正在加载视频ID:', videoId)

    const response = await videoApi.getVideoDetail(videoId)
    console.log('视频详情API响应:', response)

    if (response.success && response.data) {
      const data = response.data
      console.log('后端数据:', data)

      // 解析视频播放URL
      const playUrl = parseVideoUrl(data)
      if (!playUrl) {
        throw new Error('无法获取视频播放地址')
      }

      videoData.value = {
        id: data.id,
        title: data.title,
        description: data.description,
        likes: data.likes || 80,
        favorites: data.favorites || 27,
        collections: data.collections || 15
      }

      videoUrl.value = playUrl
      videoPoster.value = data.cover_image || '/uploads/covers/2025/07/cover_1753201118_9530.jpg'

      // 提取视频广告信息
      if (data.has_ads !== undefined) {
        videoAdInfo.value = {
          has_ads: data.has_ads,
          ad_segments: data.ad_segments || [],
          ad_confidence_score: data.ad_confidence_score || 0,
          total_ad_duration: data.total_ad_duration || 0
        }
        console.log('📺 视频广告信息:', videoAdInfo.value)
      } else {
        videoAdInfo.value = null
        console.log('⚠️ 后端未提供广告信息，将使用前端检测')
      }

      console.log('处理后的视频数据:', videoData.value)
      console.log('视频播放URL:', videoUrl.value)

      // 加载相关推荐视频
      loadRelatedVideos()

    } else {
      throw new Error(response.message || '获取视频信息失败')
    }
  } catch (err: any) {
    console.error('加载视频失败:', err)
    error.value = err.message || '加载视频失败'
  } finally {
    loading.value = false
  }
}

// 解析视频播放URL - 支持多种格式
const parseVideoUrl = (data: any): string => {
  console.log('🎬 开始解析视频播放URL，可用字段:', {
    video_url: data.video_url,
    vod_play_url: data.vod_play_url,
    hls_url: data.hls_url,
    dash_url: data.dash_url,
    source_url: data.source_url,
    play_url: data.play_url,
    play_urls: data.play_urls
  })

  // 收集所有可能的播放源
  const allSources: string[] = []

  // 1. 首先检查 play_urls 数组（采集视频的播放地址）
  if (data.play_urls && Array.isArray(data.play_urls) && data.play_urls.length > 0) {
    console.log('📺 发现采集视频播放地址:', data.play_urls)
    for (const playUrl of data.play_urls) {
      if (playUrl.play_url && typeof playUrl.play_url === 'string') {
        allSources.push(playUrl.play_url.trim())
      }
    }
  }

  // 2. 检查其他字段的播放地址
  const urlFields = [
    { field: 'vod_play_url', value: data.vod_play_url },
    { field: 'video_url', value: data.video_url },
    { field: 'hls_url', value: data.hls_url },
    { field: 'source_url', value: data.source_url },
    { field: 'play_url', value: data.play_url }
  ]

  for (const { field, value } of urlFields) {
    if (value && typeof value === 'string' && value.trim()) {
      let url = value.trim()

      // 处理特殊格式的URL（如：HD$url 或 集名$url）
      if (url.includes('$')) {
        const parts = url.split('$')
        if (parts.length >= 2) {
          url = parts[1].trim()
        }
      }

      // 处理多个播放源（用#分隔）
      if (url.includes('#')) {
        const urls = url.split('#')
        for (const u of urls) {
          if (u.includes('$')) {
            const parts = u.split('$')
            if (parts.length >= 2) {
              allSources.push(parts[1].trim())
            }
          } else if (u.trim()) {
            allSources.push(u.trim())
          }
        }
      } else {
        allSources.push(url)
      }
    }
  }

  // 去重并过滤有效URL，支持相对路径
  availableSources.value = [...new Set(allSources)].filter(url =>
    url && (url.startsWith('http') || url.startsWith('//') || url.startsWith('/'))
  )

  console.log('🎯 找到的所有播放源:', availableSources.value)

  // 设置调试信息
  debugInfo.value = JSON.stringify({
    originalData: data,
    foundSources: availableSources.value,
    currentIndex: currentSourceIndex.value
  }, null, 2)

  // 检查是否有备用播放源
  hasAlternativeSource.value = availableSources.value.length > 1

  if (availableSources.value.length > 0) {
    let selectedUrl = availableSources.value[currentSourceIndex.value] || availableSources.value[0]

    // 将相对路径转换为完整URL
    if (selectedUrl.startsWith('/') && !selectedUrl.startsWith('//')) {
      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
      selectedUrl = baseUrl + selectedUrl
    }

    console.log(`✅ 选择播放源 [${currentSourceIndex.value}]:`, selectedUrl)
    return selectedUrl
  }

  console.error('❌ 未找到有效的视频播放URL')
  return ''
}

// 重试加载
const retryLoad = () => {
  error.value = ''
  currentSourceIndex.value = 0
  loadVideoData()
}

// 尝试备用播放源
const tryAlternativeSource = () => {
  if (currentSourceIndex.value < availableSources.value.length - 1) {
    currentSourceIndex.value++
    console.log(`🔄 尝试备用播放源 [${currentSourceIndex.value}]:`, availableSources.value[currentSourceIndex.value])

    error.value = ''
    videoUrl.value = availableSources.value[currentSourceIndex.value]

    // 更新调试信息
    debugInfo.value = JSON.stringify({
      originalData: videoData.value,
      foundSources: availableSources.value,
      currentIndex: currentSourceIndex.value,
      currentUrl: videoUrl.value
    }, null, 2)
  } else {
    console.log('❌ 没有更多备用播放源')
    error.value = '所有播放源都无法播放'
  }
}

// 视频播放器事件处理
const onLoadStart = () => {
  console.log('📺 视频开始加载')
}

const onLoadedMetadata = () => {
  console.log('📺 视频元数据加载完成')
}

const onCanPlay = () => {
  console.log('📺 视频可以播放')
}

const onPlay = () => {
  console.log('▶️ 视频开始播放')
}

const onPause = () => {
  console.log('⏸️ 视频暂停')
}

const onEnded = () => {
  console.log('🏁 视频播放结束')
}

const onPlayerReady = () => {
  console.log('📺 Video.js播放器就绪')
}

const onVideoError = (errorMessage: string) => {
  console.error('❌ 视频播放错误:', errorMessage)

  // 如果有备用播放源，自动尝试下一个
  if (hasAlternativeSource.value && currentSourceIndex.value < availableSources.value.length - 1) {
    console.log('🔄 自动尝试下一个播放源')
    tryAlternativeSource()
  } else {
    // 提供更详细的错误信息
    let detailedError = errorMessage
    if (videoUrl.value) {
      if (videoUrl.value.includes('.m3u8')) {
        detailedError += '\n\n这是一个HLS流媒体文件，请检查网络连接或尝试其他播放源。'
      } else if (videoUrl.value.includes('.mp4')) {
        detailedError += '\n\n这是一个MP4视频文件，请检查文件是否存在或网络连接。'
      }
    }
    error.value = detailedError
  }
}

const onTimeUpdate = () => {
  // 可以在这里处理播放进度更新
}

const onVolumeChange = () => {
  // 可以在这里处理音量变化
}

// 操作按钮事件
const toggleLike = () => {
  console.log('点赞操作')
}

const toggleFavorite = () => {
  console.log('收藏操作')
}

const toggleCollect = () => {
  console.log('收藏操作')
}

const shareVideo = () => {
  console.log('分享视频')
}



// 格式化播放量
const formatViewCount = (count: number): string => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + '千'
  }
  return count.toString()
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (seconds <= 0) return '00:00'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
}

// 格式化上传时间
const formatUploadTime = (dateString: string): string => {
  if (!dateString) return '刚刚'

  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }
}



// 获取图片URL - 支持双备份机制
const getImageUrl = (imagePath: string, backupPath?: string) => {
  console.log('封面图片路径:', imagePath, '备份路径:', backupPath) // 调试日志

  // 尝试主封面图
  if (imagePath) {
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath
    }
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`
    return `${apiBaseUrl}${normalizedPath}`
  }

  // 尝试备份封面图
  if (backupPath) {
    if (backupPath.startsWith('http://') || backupPath.startsWith('https://')) {
      return backupPath
    }
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    const normalizedPath = backupPath.startsWith('/') ? backupPath : `/${backupPath}`
    return `${apiBaseUrl}${normalizedPath}`
  }

  // 都没有则使用默认图片
  console.log('封面图片路径为空，使用默认图片')
  return '/default-cover.svg'
}

// 处理图片加载错误 - 尝试备份图片
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  const originalSrc = img.src

  // 如果当前显示的是主图片，尝试备份图片
  if (img.dataset.backup && img.src !== img.dataset.backup) {
    console.log('主封面图加载失败，尝试备份封面图:', img.dataset.backup)
    img.src = img.dataset.backup
    return
  }

  // 如果备份图片也失败，使用默认图片
  if (!img.src.includes('default-cover.svg')) {
    console.log('备份封面图也加载失败，使用默认图片')
    img.src = '/default-cover.svg'
  }
}

// 播放相关视频
const playRelatedVideo = (video: any) => {
  console.log('🎬 点击播放相关视频:', video.id, video.title)

  // 如果是当前视频，不需要跳转
  if (video.id.toString() === route.params.id) {
    console.log('⚠️ 点击的是当前视频，无需跳转')
    return
  }

  // 跳转到新视频
  router.push(`/video/${video.id}`)
}

// 加载相关推荐视频
const loadRelatedVideos = async () => {
  try {
    console.log('🔍 开始加载相关推荐视频')

    // 启用真实API调用获取相关推荐视频
    console.log('🔧 启用真实API调用模式')

    // 使用推荐视频API，只获取同分类的长视频
    const response = await videoApi.getRecommended({
      page: 1,
      pageSize: 6, // 获取6个推荐视频
      videoType: 'long', // 明确指定获取长视频
      categoryId: videoData.value?.category_id, // 传递当前视频的分类ID
      excludeVideoId: route.params.id // 排除当前视频
    })

    console.log('📺 推荐视频API响应:', response)
    console.log('📺 响应数据类型:', typeof response.data)
    console.log('📺 响应数据内容:', response.data)
    console.log('📺 是否为数组:', Array.isArray(response.data))

    // 检查响应数据格式
    let videos: any[] = []
    if (response.success && response.data) {
      // 格式1: { success: true, data: { videos: [...] } }
      if (response.data.videos && Array.isArray(response.data.videos)) {
        videos = response.data.videos
        console.log('📺 使用格式1，videos数组长度:', videos.length)
      }
      // 格式2: { success: true, data: [...] } (直接是视频数组)
      else if (Array.isArray(response.data)) {
        videos = response.data
        console.log('📺 使用格式2，videos数组长度:', videos.length)
      }
      else {
        console.log('📺 数据格式不匹配，response.data:', response.data)
      }
    } else {
      console.log('📺 响应检查失败 - success:', response.success, 'data:', response.data)
    }

    if (videos.length > 0) {
      // 格式化视频数据
      relatedVideos.value = videos.map((video: any) => ({
        id: video.id,
        title: video.title,
        thumbnail: video.cover_image || video.poster || '/default-cover.svg',
        views: formatViewCount(video.view_count || video.views || 0),
        duration: formatDuration(video.duration || 0),
        description: video.description,
        uploadTime: formatUploadTime(video.created_at || video.upload_time),
        category: video.category_name || video.category || '其他'
      }))

      console.log('✅ 相关视频加载成功:', relatedVideos.value.length, '个视频')
    } else {
      console.warn('⚠️ 推荐视频API返回数据为空')
      // 尝试获取所有视频作为推荐
      await loadAllVideosAsRecommended()
    }
  } catch (err: any) {
    console.error('❌ 加载相关视频失败:', err)
    // 尝试获取所有视频作为推荐
    await loadAllVideosAsRecommended()
  }
}

// 当推荐API失败时，尝试获取所有视频作为推荐
const loadAllVideosAsRecommended = async () => {
  try {
    console.log('🔄 尝试获取所有视频作为推荐')

    // 使用视频列表API获取视频
    const response = await videoApi.getList({
      page: 1,
      pageSize: 6
    })

    console.log('📺 视频列表API响应:', response)

    if (response.success && response.data && response.data.videos && response.data.videos.length > 0) {
      // 格式化视频数据
      relatedVideos.value = response.data.videos.map((video: any) => ({
        id: video.id,
        title: video.title,
        thumbnail: video.cover_image || video.poster || '/default-cover.svg',
        views: formatViewCount(video.view_count || video.views || 0),
        duration: formatDuration(video.duration || 0),
        description: video.description,
        uploadTime: formatUploadTime(video.created_at || video.upload_time),
        category: video.category_name || video.category || '其他'
      }))

      console.log('✅ 使用视频列表作为推荐成功:', relatedVideos.value.length, '个视频')
    } else {
      console.warn('⚠️ 视频列表API也返回空数据，使用备用数据')
      setFallbackRelatedVideos()
    }
  } catch (error) {
    console.error('❌ 获取视频列表也失败，使用备用数据:', error)
    setFallbackRelatedVideos()
  }
}

// 备用相关视频数据（API失败时使用）
const setFallbackRelatedVideos = () => {
  console.warn('🔄 所有API都失败，使用最少的备用数据')
  relatedVideos.value = [
    {
      id: 999,
      title: '暂无推荐视频 - 请稍后重试',
      thumbnail: '/default-cover.svg',
      views: '0',
      duration: '0:00',
      uploadTime: '刚刚',
      category: '暂无'
    }
  ]
  console.log('✅ 备用数据设置完成')
}



// 监听路由参数变化
watch(() => route.params.id, (newId, oldId) => {
  if (newId && newId !== oldId) {
    console.log('🔄 路由参数变化，重新加载视频:', oldId, '->', newId)
    // 重置状态
    loading.value = true
    error.value = ''
    videoData.value = null
    videoUrl.value = ''
    relatedVideos.value = []

    // 重新加载数据
    loadVideoData()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(async () => {
  await loadPlayerConfig()
  loadVideoData()
})
</script>

<style scoped>
.video-player-page {
  background: #000;
  color: #fff;
  min-height: 100vh;
  overflow-x: hidden;
}

/* 顶部导航栏 */
.header-nav {
  position: relative;
  background: rgba(0,0,0,0.9);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
}

.back-btn {
  background: none !important;
  border: none !important;
  color: #fff !important;
  padding: 8px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  width: 36px !important;
  height: 36px !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
}

.back-btn:hover {
  background: rgba(255,255,255,0.1) !important;
}

.header-info {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  text-align: center !important;
  padding: 0 12px !important;
  margin: 0 !important;
}

.header-spacer {
  width: 36px !important;
  height: 36px !important;
  flex-shrink: 0 !important;
}

.header-info .video-title {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: rgba(255,255,255,0.8);
}

/* 播放器容器 */
.player-container {
  position: relative;
  width: 100%;
  height: 56.25vw; /* 16:9 aspect ratio */
  max-height: 70vh;
  min-height: 200px;
  background: #000;
  overflow: hidden;
}

/* VideoPlayer组件样式 */
.player-container :deep(.video-player-container) {
  width: 100%;
  height: 100%;
}

.player-container :deep(.video-js) {
  width: 100%;
  height: 100%;
}

.player-container :deep(.vjs-tech) {
  object-fit: contain;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255,255,255,0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-content {
  text-align: center;
  padding: 40px;
  max-width: 500px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  color: white;
}

.error-content h3 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
}

.error-content p {
  margin: 0 0 24px 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  white-space: pre-line;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.retry-btn, .alternative-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn {
  background: #ff6b6b;
}

.retry-btn:hover {
  background: #ff5252;
  transform: translateY(-1px);
}

.alternative-btn {
  background: #4CAF50;
}

.alternative-btn:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.debug-info {
  margin-top: 20px;
  text-align: left;
}

.debug-info details {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  padding: 10px;
}

.debug-info summary {
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 10px;
}

.debug-info pre {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 视频信息区域 */
.video-info-section {
  padding: 20px 16px;
}

.video-header {
  margin-bottom: 20px;
}

.video-header .video-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  line-height: 1.4;
}





/* 操作按钮区域 */
.video-description {
  margin-bottom: 20px;
}

.video-description p {
  color: rgba(255,255,255,0.8);
  line-height: 1.6;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  margin-bottom: 20px;
}

.action-btn {
  background: none;
  border: none;
  color: rgba(255,255,255,0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: rgba(255,255,255,0.1);
  color: #fff;
}

.action-btn span {
  font-size: 12px;
}

/* 剧集选择区域 */
.episodes-section {
  padding: 0 16px 20px;
}

.episodes-header {
  margin-bottom: 16px;
}

.episodes-header h3 {
  font-size: 18px;
  font-weight: 600;
}

.episode-numbers {
  display: flex;
  gap: 12px;
  align-items: center;
  overflow-x: auto;
  padding-bottom: 8px;
}

.episode-numbers::-webkit-scrollbar {
  display: none;
}

.episode-btn {
  background: rgba(255,255,255,0.1);
  color: rgba(255,255,255,0.8);
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  min-width: 40px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.episode-btn:hover {
  background: rgba(255,255,255,0.2);
}

.episode-btn.active {
  background: #ff6b35;
  color: #fff;
}

.more-episodes {
  background: none;
  border: none;
  color: rgba(255,255,255,0.6);
  cursor: pointer;
  font-size: 14px;
  padding: 8px 12px;
  flex-shrink: 0;
}

/* 相关内容区域 */
.related-content {
  padding: 0 16px 20px;
}

.related-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.related-videos {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.video-card {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  transform: translateY(0);
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 107, 107, 0.3);
}

.video-poster {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
  background: #1a1a1a;
}

.video-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .video-poster img {
  transform: scale(1.05);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-card:hover .video-overlay {
  opacity: 1;
}

.play-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  transform: scale(1);
  transition: transform 0.2s ease;
}

.video-card:hover .play-icon {
  transform: scale(1.1);
}

.play-icon svg {
  width: 24px;
  height: 24px;
  margin-left: 2px;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.video-info {
  padding: 12px;
}

.video-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  color: #999;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ff6b6b;
}

.view-count svg {
  opacity: 0.8;
}

.upload-time {
  color: #666;
  font-size: 12px;
}

/* 响应式设计 */
/* 桌面端适配 */
@media (min-width: 769px) {
  .video-player-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  .player-container {
    height: 60vh;
    max-height: 600px;
    margin-bottom: 20px;
  }

  .header-nav {
    position: relative;
    background: rgba(0,0,0,0.9);
    padding: 16px 20px;
  }

  .video-info-section {
    padding: 20px;
  }

  .episodes-section {
    padding: 0 20px 20px;
  }

  .related-content {
    padding: 0 20px 20px;
  }

  .related-videos {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 0;
  }
}

/* 平板端适配 */
@media (max-width: 768px) {
  .video-player-page {
    overflow-x: hidden;
  }

  .header-nav {
    position: relative;
    background: rgba(0,0,0,0.9);
  }

  .player-container {
    height: 56.25vw;
    max-height: 60vh;
    min-height: 200px;
  }

  .related-videos {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
    padding: 16px 0;
  }

  .action-buttons {
    padding: 12px 0;
  }

  .action-btn {
    font-size: 11px;
    padding: 6px;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .video-player-page {
    overflow-x: hidden;
  }

  .header-nav {
    padding: 8px 12px;
    position: relative;
    background: rgba(0,0,0,0.95);
  }

  .player-container {
    height: 56.25vw;
    max-height: 50vh;
    min-height: 180px;
  }

  .video-info-section {
    padding: 16px 12px;
  }

  .episodes-section {
    padding: 0 12px 16px;
  }

  .related-content {
    padding: 0 12px 16px;
  }

  .video-header .video-title {
    font-size: 18px;
  }

  .related-videos {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 10px;
    padding: 12px 0;
  }

  .action-buttons {
    padding: 16px 0;
    gap: 8px;
  }

  .action-btn {
    font-size: 12px;
    padding: 8px 4px;
  }
}
</style>