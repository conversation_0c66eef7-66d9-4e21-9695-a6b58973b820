<?php
declare(strict_types=1);

namespace app\job;

use think\queue\Job;
use think\facade\Log;
use think\facade\Db;
use app\service\VideoProcessingService;
use app\service\CloudStorageService;
use app\service\VideoStatusService;
use app\service\VideoPathService;
use app\exception\VideoProcessingException;

/**
 * 视频处理任务
 * 
 * 异步处理视频切片、转码、上传等操作
 */
class VideoProcessJob
{
    /**
     * 执行任务
     * 
     * @param Job $job 任务对象
     * @param array $data 任务数据
     * @return void
     */
    public function fire(Job $job, array $data): void
    {
        $videoId = $data['video_id'] ?? 0;
        $filePath = $data['file_path'] ?? '';
        $options = $data['options'] ?? [];

        // 初始化服务
        $statusService = new VideoStatusService();
        $pathService = new VideoPathService();

        Log::info('开始执行视频处理任务', [
            'job_id' => $job->getJobId(),
            'video_id' => $videoId,
            'file_path' => $filePath,
            'attempts' => $job->attempts()
        ]);

        try {
            // 创建处理状态记录
            $this->createProcessingStatus($videoId, $job->getJobId());

            // 更新视频状态为处理中
            $statusService->updateVideoStatus($videoId, VideoStatusService::STATUS_PUBLISHED);
            $statusService->updateProcessingStatus($videoId, VideoStatusService::STEP_TRANSCODE, 'processing', 10, '开始视频处理');

            // 1. 视频处理（切片、转码）
            $this->updateProcessingStatus($videoId, 'processing', 30, '正在进行视频切片和转码');
            $videoProcessingService = new VideoProcessingService();
            $processResult = $videoProcessingService->processVideo($videoId, $filePath);

            // 2. 上传到云存储
            if ($options['upload_to_cloud'] ?? true) {
                $this->updateProcessingStatus($videoId, 'processing', 60, '正在上传到云存储');
                $cloudResult = $this->uploadToCloud($processResult, $options);
                $processResult['cloud_result'] = $cloudResult;
            }

            // 3. 更新数据库记录
            $this->updateProcessingStatus($videoId, 'processing', 80, '正在更新数据库记录');
            $this->updateVideoRecord($videoId, $processResult);

            // 4. 清理临时文件
            if ($options['cleanup_temp'] ?? true) {
                $this->updateProcessingStatus($videoId, 'processing', 90, '正在清理临时文件');
                $this->cleanupTempFiles($filePath, $processResult);
            }

            // 5. 更新视频状态为完成
            $this->updateProcessingStatus($videoId, 'completed', 100, '视频处理完成');
            $this->updateVideoStatus($videoId, 'completed', '视频处理完成');

            Log::info('视频处理任务完成', [
                'job_id' => $job->getJobId(),
                'video_id' => $videoId,
                'process_result' => $processResult
            ]);

            // 删除任务
            $job->delete();

        } catch (\Exception $e) {
            Log::error('视频处理任务失败', [
                'job_id' => $job->getJobId(),
                'video_id' => $videoId,
                'file_path' => $filePath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'attempts' => $job->attempts()
            ]);

            // 更新视频状态为失败
            $this->updateProcessingStatus($videoId, 'failed', 0, $e->getMessage());
            $this->updateVideoStatus($videoId, 'failed', $e->getMessage());

            // 判断是否需要重试
            if ($job->attempts() >= 3) {
                Log::error('视频处理任务达到最大重试次数，放弃处理', [
                    'job_id' => $job->getJobId(),
                    'video_id' => $videoId,
                    'attempts' => $job->attempts()
                ]);
                $job->delete();
            } else {
                // 延迟重试
                $delay = pow(2, $job->attempts()) * 60; // 指数退避：2分钟、4分钟、8分钟
                $job->release($delay);
                Log::info('视频处理任务将在' . $delay . '秒后重试', [
                    'job_id' => $job->getJobId(),
                    'video_id' => $videoId,
                    'delay' => $delay
                ]);
            }
        }
    }

    /**
     * 上传到云存储
     * 
     * @param array $processResult 处理结果
     * @param array $options 选项
     * @return array 上传结果
     */
    private function uploadToCloud(array $processResult, array $options): array
    {
        $cloudStorageService = new CloudStorageService();
        $uploadResults = [];

        Log::info('开始上传文件到云存储', [
            'video_id' => $processResult['video_id']
        ]);

        try {
            // 上传HLS文件
            if (isset($processResult['hls_result'])) {
                $hlsFiles = [];
                
                // 上传播放列表
                $playlistPath = app()->getRootPath() . 'public' . $processResult['hls_result']['playlist'];
                if (file_exists($playlistPath)) {
                    $remotePath = 'hls/' . $processResult['video_id'] . '/playlist.m3u8';
                    $hlsFiles[] = ['local' => $playlistPath, 'remote' => $remotePath];
                }

                // 上传切片文件
                foreach ($processResult['hls_result']['segments'] as $segment) {
                    $segmentPath = app()->getRootPath() . 'public' . $segment;
                    if (file_exists($segmentPath)) {
                        $remotePath = 'hls/' . $processResult['video_id'] . '/' . basename($segment);
                        $hlsFiles[] = ['local' => $segmentPath, 'remote' => $remotePath];
                    }
                }

                if (!empty($hlsFiles)) {
                    $uploadResults['hls'] = $cloudStorageService->batchUpload($hlsFiles);
                }
            }

            // 上传转码文件
            if (isset($processResult['transcode_results'])) {
                $transcodeFiles = [];
                
                foreach ($processResult['transcode_results'] as $quality => $result) {
                    $localPath = app()->getRootPath() . 'public' . $result['path'];
                    if (file_exists($localPath)) {
                        $remotePath = 'videos/' . $processResult['video_id'] . '/video_' . $quality . '.mp4';
                        $transcodeFiles[] = ['local' => $localPath, 'remote' => $remotePath];
                    }
                }

                if (!empty($transcodeFiles)) {
                    $uploadResults['transcode'] = $cloudStorageService->batchUpload($transcodeFiles);
                }
            }

            // 上传缩略图
            if (isset($processResult['thumbnail'])) {
                $thumbnailPath = app()->getRootPath() . 'public' . $processResult['thumbnail'];
                if (file_exists($thumbnailPath)) {
                    $remotePath = 'thumbnails/' . $processResult['video_id'] . '/thumbnail.jpg';
                    $uploadResults['thumbnail'] = $cloudStorageService->upload($thumbnailPath, $remotePath);
                }
            }

            Log::info('云存储上传完成', [
                'video_id' => $processResult['video_id'],
                'upload_results' => $uploadResults
            ]);

            return $uploadResults;

        } catch (\Exception $e) {
            Log::error('云存储上传失败', [
                'video_id' => $processResult['video_id'],
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 更新视频记录
     * 
     * @param int $videoId 视频ID
     * @param array $processResult 处理结果
     * @return void
     */
    private function updateVideoRecord(int $videoId, array $processResult): void
    {
        $updateData = [
            'status' => 'published',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 更新视频信息
        if (isset($processResult['video_info'])) {
            $videoInfo = $processResult['video_info'];
            $updateData['duration'] = $videoInfo['duration'];
            $updateData['width'] = $videoInfo['width'];
            $updateData['height'] = $videoInfo['height'];
        }

        // 更新缩略图
        if (isset($processResult['thumbnail'])) {
            $updateData['cover_image'] = $processResult['thumbnail'];
        }

        // 更新HLS路径
        if (isset($processResult['hls_result']['playlist'])) {
            $updateData['hls_url'] = $processResult['hls_result']['playlist'];
        }

        // 更新转码结果
        if (isset($processResult['transcode_results'])) {
            $updateData['qualities'] = json_encode($processResult['transcode_results']);
        }

        // 更新云存储信息
        if (isset($processResult['cloud_result'])) {
            $updateData['cloud_storage'] = json_encode($processResult['cloud_result']);
        }

        Db::table('videos')
            ->where('id', $videoId)
            ->update($updateData);

        Log::info('视频记录更新完成', [
            'video_id' => $videoId,
            'update_data' => $updateData
        ]);
    }

    /**
     * 更新视频状态
     *
     * @param int $videoId 视频ID
     * @param string $status 状态
     * @param string $message 消息
     * @return void
     */
    private function updateVideoStatus(int $videoId, string $status, string $message = ''): void
    {
        // 映射处理状态到videos表的有效状态
        $statusMapping = [
            'processing' => 'published',  // 处理中时保持已发布状态
            'completed' => 'published',   // 完成后设为已发布
            'failed' => 'published',      // 失败时保持已发布状态，等待重试
            'pending' => 'published'      // 待处理时保持已发布状态
        ];

        $videoStatus = $statusMapping[$status] ?? 'published';

        $updateData = [
            'status' => $videoStatus,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 注意：videos表中没有process_message和error_message字段
        // 这些信息存储在video_processing_status表中

        Db::table('videos')
            ->where('id', $videoId)
            ->update($updateData);

        Log::info('更新视频状态', [
            'video_id' => $videoId,
            'original_status' => $status,
            'mapped_status' => $videoStatus,
            'message' => $message
        ]);
    }

    /**
     * 清理临时文件
     * 
     * @param string $originalFile 原始文件路径
     * @param array $processResult 处理结果
     * @return void
     */
    private function cleanupTempFiles(string $originalFile, array $processResult): void
    {
        $filesToDelete = [$originalFile];

        // 添加处理过程中的临时文件
        if (isset($processResult['temp_files'])) {
            $filesToDelete = array_merge($filesToDelete, $processResult['temp_files']);
        }

        foreach ($filesToDelete as $file) {
            if (file_exists($file)) {
                unlink($file);
                Log::debug('删除临时文件', ['file' => $file]);
            }
        }

        Log::info('临时文件清理完成', [
            'video_id' => $processResult['video_id'],
            'deleted_files' => count($filesToDelete)
        ]);
    }

    /**
     * 创建处理状态记录
     *
     * @param int $videoId 视频ID
     * @param string $jobId 任务ID
     * @return void
     */
    private function createProcessingStatus(int $videoId, string $jobId): void
    {
        // 删除可能存在的旧记录
        Db::table('video_processing_status')
            ->where('video_id', $videoId)
            ->delete();

        // 创建新记录
        Db::table('video_processing_status')
            ->insert([
                'video_id' => $videoId,
                'process_type' => 'transcode',
                'status' => 'pending',
                'progress' => 0,
                'current_step' => '准备开始处理',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 更新处理状态
     *
     * @param int $videoId 视频ID
     * @param string $status 状态
     * @param int $progress 进度
     * @param string $step 当前步骤
     * @param int|null $estimatedTime 预计剩余时间
     * @return void
     */
    private function updateProcessingStatus(int $videoId, string $status, int $progress = 0, string $step = '', ?int $estimatedTime = null): void
    {
        // 验证状态值是否有效
        $validStatuses = ['pending', 'processing', 'completed', 'failed'];
        if (!in_array($status, $validStatuses)) {
            Log::warning('无效的处理状态值', [
                'video_id' => $videoId,
                'invalid_status' => $status,
                'valid_statuses' => $validStatuses
            ]);
            $status = 'failed'; // 默认设为失败状态
        }

        $updateData = [
            'status' => $status,
            'progress' => $progress,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($step) {
            if ($status === 'failed') {
                $updateData['error_message'] = $step; // 错误信息存储在error_message中
                $updateData['message'] = '视频转码失败: ' . substr($step, 0, 100); // 简短消息
            } else {
                $updateData['message'] = $step; // 正常消息存储在message中
            }
        }

        if ($status === 'processing' && $progress === 10) {
            $updateData['started_at'] = date('Y-m-d H:i:s');
        }

        if ($status === 'completed') {
            $updateData['completed_at'] = date('Y-m-d H:i:s');
            $updateData['progress'] = 100;
        }

        if ($status === 'failed') {
            $updateData['completed_at'] = date('Y-m-d H:i:s');
        }

        try {
            $result = Db::table('video_processing_status')
                ->where('video_id', $videoId)
                ->where('process_type', 'transcode')
                ->update($updateData);

            if ($result === 0) {
                Log::warning('未找到要更新的处理状态记录', [
                    'video_id' => $videoId,
                    'process_type' => 'transcode'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('更新处理状态失败', [
                'video_id' => $videoId,
                'status' => $status,
                'error' => $e->getMessage(),
                'update_data' => $updateData
            ]);
            throw $e;
        }

        Log::info('更新视频处理状态', [
            'video_id' => $videoId,
            'status' => $status,
            'progress' => $progress,
            'step' => $step
        ]);
    }

    /**
     * 任务失败处理
     *
     * @param Job $job 任务对象
     * @param \Exception $exception 异常
     * @return void
     */
    public function failed(Job $job, \Exception $exception): void
    {
        Log::error('视频处理任务最终失败', [
            'job_id' => $job->getJobId(),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
