<template>
  <div class="live-page">
    <!-- 顶部导航 -->
    <div class="top-navigation">
      <div class="nav-left">
        <div class="hamburger-menu">
          <div class="hamburger-line"></div>
          <div class="hamburger-line"></div>
          <div class="hamburger-line"></div>
        </div>
      </div>
      
      <div class="nav-center">
        <h2 class="page-title">直播</h2>
      </div>
      
      <div class="nav-right">
        <div class="search-icon">🔍</div>
      </div>
    </div>

    <!-- 直播分类 -->
    <div class="live-categories">
      <div 
        v-for="category in categories" 
        :key="category.id"
        class="category-item"
        :class="{ active: activeCategory === category.id }"
        @click="activeCategory = category.id"
      >
        {{ category.name }}
      </div>
    </div>

    <!-- 直播列表 -->
    <div class="live-list">
      <div 
        v-for="live in filteredLives" 
        :key="live.id"
        class="live-card"
        @click="enterLive(live)"
      >
        <div class="live-thumbnail">
          <img
            :src="live.thumbnail"
            :alt="live.title"
            @error="handleImageError($event, live.id, 'live')"
            loading="lazy"
          >
          <div class="live-badge">直播中</div>
          <div class="viewer-count">{{ formatViewers(live.viewers) }}人观看</div>
        </div>
        
        <div class="live-info">
          <div class="streamer-info">
            <img
              :src="live.streamerAvatar"
              :alt="live.streamerName"
              class="streamer-avatar"
              @error="handleImageError($event, live.id, 'avatar')"
              loading="lazy"
            >
            <div class="streamer-details">
              <h3 class="live-title">{{ live.title }}</h3>
              <p class="streamer-name">{{ live.streamerName }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'


const activeCategory = ref('all')

// 直播分类
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'entertainment', name: '娱乐' },
  { id: 'game', name: '游戏' },
  { id: 'music', name: '音乐' },
  { id: 'dance', name: '舞蹈' },
  { id: 'chat', name: '聊天' }
])

// 生成虚拟图片URL的函数 - 使用多个备用服务确保可靠性
const generateThumbnailUrl = (id: number, type: 'live' | 'avatar') => {
  const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8', 'F7DC6F', 'BB8FCE', '85C1E9']
  const color = colors[id % colors.length]

  if (type === 'live') {
    // 直播间缩略图 - 使用多个备用服务
    const themes = ['游戏直播', '音乐表演', '聊天互动', '才艺展示', '户外探险', '美食制作', '学习分享', '运动健身']
    const theme = themes[id % themes.length]

    // 多个图片服务备选方案
    const services = [
      `https://picsum.photos/320/180?random=${id}&blur=1`,
      `https://source.unsplash.com/320x180/?live,streaming,${theme}&sig=${id}`,
      `https://via.placeholder.com/320x180/${color}/FFFFFF?text=${encodeURIComponent(theme)}`,
      `https://dummyimage.com/320x180/${color}/ffffff&text=${encodeURIComponent(theme)}`
    ]

    return services[id % services.length]
  } else {
    // 主播头像 - 使用多个备用服务
    const avatarServices = [
      `https://picsum.photos/80/80?random=${id + 1000}&face`,
      `https://source.unsplash.com/80x80/?portrait,face&sig=${id + 1000}`,
      `https://via.placeholder.com/80x80/${color}/FFFFFF?text=${encodeURIComponent('主播' + id)}`,
      `https://dummyimage.com/80x80/${color}/ffffff&text=${encodeURIComponent('主播' + id)}`
    ]

    return avatarServices[id % avatarServices.length]
  }
}

// 模拟直播数据
const lives = ref([
  {
    id: 1,
    title: '深夜聊天室 - 一起聊聊生活',
    thumbnail: generateThumbnailUrl(1, 'live'),
    streamerName: '主播小美',
    streamerAvatar: generateThumbnailUrl(1, 'avatar'),
    viewers: 1234,
    category: 'chat'
  },
  {
    id: 2,
    title: '王者荣耀上分局 - 带你飞',
    thumbnail: generateThumbnailUrl(2, 'live'),
    streamerName: '游戏大神',
    streamerAvatar: generateThumbnailUrl(2, 'avatar'),
    viewers: 5678,
    category: 'game'
  },
  {
    id: 3,
    title: '唱歌给你听 - 点歌台',
    thumbnail: generateThumbnailUrl(3, 'live'),
    streamerName: '歌手小雨',
    streamerAvatar: generateThumbnailUrl(3, 'avatar'),
    viewers: 2345,
    category: 'music'
  },
  {
    id: 4,
    title: '舞蹈教学 - 学会这支舞',
    thumbnail: generateThumbnailUrl(4, 'live'),
    streamerName: '舞蹈老师',
    streamerAvatar: generateThumbnailUrl(4, 'avatar'),
    viewers: 3456,
    category: 'dance'
  },
  {
    id: 5,
    title: '搞笑段子分享 - 笑到肚子疼',
    thumbnail: generateThumbnailUrl(5, 'live'),
    streamerName: '段子手',
    streamerAvatar: generateThumbnailUrl(5, 'avatar'),
    viewers: 4567,
    category: 'entertainment'
  },
  {
    id: 6,
    title: '美食制作直播 - 教你做菜',
    thumbnail: generateThumbnailUrl(6, 'live'),
    streamerName: '美食达人',
    streamerAvatar: generateThumbnailUrl(6, 'avatar'),
    viewers: 1890,
    category: 'entertainment'
  }
])

// 过滤直播列表
const filteredLives = computed(() => {
  if (activeCategory.value === 'all') {
    return lives.value
  }
  return lives.value.filter(live => live.category === activeCategory.value)
})

const enterLive = (live: any) => {
  console.log('进入直播间:', live.title)
  // 这里可以跳转到直播间页面
}

const formatViewers = (viewers: number) => {
  if (viewers >= 10000) {
    return `${(viewers / 10000).toFixed(1)}万`
  }
  return viewers.toLocaleString()
}

// 图片加载错误处理
const handleImageError = (event: Event, liveId: number, type: 'live' | 'avatar') => {
  const img = event.target as HTMLImageElement
  const currentSrc = img.src

  // 获取所有可用的图片服务
  const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8', 'F7DC6F', 'BB8FCE', '85C1E9']
  const color = colors[liveId % colors.length]

  let fallbackUrls: string[] = []

  if (type === 'live') {
    const themes = ['游戏直播', '音乐表演', '聊天互动', '才艺展示', '户外探险', '美食制作', '学习分享', '运动健身']
    const theme = themes[liveId % themes.length]

    fallbackUrls = [
      `https://picsum.photos/320/180?random=${liveId}&blur=1`,
      `https://source.unsplash.com/320x180/?live,streaming,${theme}&sig=${liveId}`,
      `https://via.placeholder.com/320x180/${color}/FFFFFF?text=${encodeURIComponent(theme)}`,
      `https://dummyimage.com/320x180/${color}/ffffff&text=${encodeURIComponent(theme)}`,
      // 最终备用方案 - 本地生成的纯色图片
      `data:image/svg+xml;base64,${btoa(`<svg width="320" height="180" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#${color}"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="16">${theme}</text></svg>`)}`
    ]
  } else {
    fallbackUrls = [
      `https://picsum.photos/80/80?random=${liveId + 1000}&face`,
      `https://source.unsplash.com/80x80/?portrait,face&sig=${liveId + 1000}`,
      `https://via.placeholder.com/80x80/${color}/FFFFFF?text=${encodeURIComponent('主播' + liveId)}`,
      `https://dummyimage.com/80x80/${color}/ffffff&text=${encodeURIComponent('主播' + liveId)}`,
      // 最终备用方案
      `data:image/svg+xml;base64,${btoa(`<svg width="80" height="80" xmlns="http://www.w3.org/2000/svg"><circle cx="40" cy="40" r="40" fill="#${color}"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="12">主播${liveId}</text></svg>`)}`
    ]
  }

  // 找到下一个可用的URL
  const currentIndex = fallbackUrls.findIndex(url => url === currentSrc)
  const nextIndex = currentIndex + 1

  if (nextIndex < fallbackUrls.length) {
    console.log(`图片加载失败，尝试备用方案 ${nextIndex + 1}:`, fallbackUrls[nextIndex])
    img.src = fallbackUrls[nextIndex]
  } else {
    console.warn('所有图片服务都不可用，使用最终备用方案')
    // 如果所有服务都失败，使用最后一个SVG备用方案
    img.src = fallbackUrls[fallbackUrls.length - 1]
  }
}
</script>

<style scoped>
.live-page {
  min-height: 100vh;
  background: #000000;
  color: #ffffff;
  padding-bottom: 60px;
}

/* 顶部导航 */
.top-navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  z-index: 1000;
}

.nav-left, .nav-right {
  width: 50px;
  display: flex;
  justify-content: center;
}

.hamburger-menu {
  display: flex;
  flex-direction: column;
  gap: 3px;
  cursor: pointer;
}

.hamburger-line {
  width: 18px;
  height: 2px;
  background: #ffffff;
  border-radius: 1px;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.page-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.search-icon {
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
}

/* 直播分类 */
.live-categories {
  position: fixed;
  top: 50px;
  left: 0;
  right: 0;
  height: 50px;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  padding: 0 15px;
  gap: 20px;
  overflow-x: auto;
  z-index: 999;
}

.category-item {
  white-space: nowrap;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #cccccc;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #ff4444;
  color: #ffffff;
}

.category-item:hover {
  background: rgba(255, 68, 68, 0.3);
}

/* 直播列表 */
.live-list {
  padding: 110px 15px 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.live-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.live-card:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.live-card:active {
  transform: scale(0.98);
}

.live-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.live-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.live-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #ff4444;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.viewer-count {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.live-info {
  padding: 12px;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.streamer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
}

.streamer-details {
  flex: 1;
  min-width: 0;
}

.live-title {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.streamer-name {
  font-size: 0.8rem;
  color: #cccccc;
  margin: 0;
}

/* ================================
   响应式设计优化
   ================================ */

/* 超小屏幕 (手机竖屏) - 320px ~ 575px */
@media (max-width: 575.98px) {
  .top-navigation {
    height: 44px;
    padding: 0 10px;
  }

  .nav-left, .nav-right {
    width: 40px;
  }

  .hamburger-line {
    width: 16px;
    height: 2px;
  }

  .page-title {
    font-size: 1rem;
  }

  .search-icon {
    font-size: 1rem;
  }

  .live-categories {
    top: 44px;
    height: 44px;
    padding: 0 8px;
    gap: 12px;
  }

  .category-item {
    padding: 6px 12px;
    font-size: 0.8rem;
    border-radius: 16px;
  }

  .live-list {
    grid-template-columns: 1fr;
    padding: 98px 8px 20px;
    gap: 10px;
  }

  .live-card {
    border-radius: 8px;
  }

  .live-thumbnail {
    height: 120px;
  }

  .live-badge {
    top: 6px;
    left: 6px;
    padding: 3px 6px;
    font-size: 0.65rem;
  }

  .viewer-count {
    bottom: 6px;
    right: 6px;
    padding: 3px 6px;
    font-size: 0.7rem;
  }

  .live-info {
    padding: 8px;
  }

  .streamer-info {
    gap: 8px;
  }

  .streamer-avatar {
    width: 32px;
    height: 32px;
  }

  .live-title {
    font-size: 0.8rem;
    -webkit-line-clamp: 2;
  }

  .streamer-name {
    font-size: 0.7rem;
  }
}

/* 小屏幕 (手机横屏) - 576px ~ 767px */
@media (min-width: 576px) and (max-width: 767.98px) {
  .top-navigation {
    height: 46px;
    padding: 0 12px;
  }

  .nav-left, .nav-right {
    width: 45px;
  }

  .page-title {
    font-size: 1.1rem;
  }

  .live-categories {
    top: 46px;
    height: 46px;
    padding: 0 10px;
    gap: 15px;
  }

  .category-item {
    padding: 7px 14px;
    font-size: 0.85rem;
  }

  .live-list {
    grid-template-columns: repeat(2, 1fr);
    padding: 102px 10px 20px;
    gap: 12px;
  }

  .live-thumbnail {
    height: 140px;
  }

  .live-info {
    padding: 10px;
  }

  .streamer-avatar {
    width: 36px;
    height: 36px;
  }

  .live-title {
    font-size: 0.85rem;
  }

  .streamer-name {
    font-size: 0.75rem;
  }
}

/* 中等屏幕 (平板竖屏) - 768px ~ 991px */
@media (min-width: 768px) and (max-width: 991.98px) {
  .top-navigation {
    height: 48px;
    padding: 0 15px;
  }

  .live-categories {
    top: 48px;
    height: 48px;
    padding: 0 12px;
    gap: 18px;
  }

  .live-list {
    grid-template-columns: repeat(3, 1fr);
    padding: 106px 12px 20px;
    gap: 14px;
  }

  .live-thumbnail {
    height: 150px;
  }

  .live-info {
    padding: 12px;
  }

  .streamer-avatar {
    width: 38px;
    height: 38px;
  }

  .live-title {
    font-size: 0.9rem;
  }

  .streamer-name {
    font-size: 0.8rem;
  }
}

/* 大屏幕 (平板横屏/小桌面) - 992px ~ 1199px */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .live-list {
    grid-template-columns: repeat(4, 1fr);
    padding: 110px 15px 20px;
    gap: 16px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .live-thumbnail {
    height: 160px;
  }

  .live-card:hover {
    transform: translateY(-4px);
  }
}

/* 超大屏幕 (桌面) - 1200px+ */
@media (min-width: 1200px) {
  .live-list {
    grid-template-columns: repeat(5, 1fr);
    padding: 110px 20px 20px;
    gap: 20px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .live-thumbnail {
    height: 180px;
  }

  .live-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  .live-info {
    padding: 14px;
  }

  .streamer-avatar {
    width: 42px;
    height: 42px;
  }

  .live-title {
    font-size: 1rem;
  }

  .streamer-name {
    font-size: 0.85rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .live-card:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.05);
    box-shadow: none;
  }

  .live-card:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.15);
  }

  .category-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .category-item:active {
    transform: scale(0.95);
  }

  .hamburger-menu:active,
  .search-icon:active {
    transform: scale(0.9);
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .top-navigation {
    height: 40px;
  }

  .live-categories {
    top: 40px;
    height: 40px;
  }

  .live-list {
    padding: 90px 10px 20px;
    gap: 10px;
  }

  .live-thumbnail {
    height: 100px;
  }

  .live-info {
    padding: 8px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-resolution: 2dppx) {
  .live-thumbnail img,
  .streamer-avatar {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .live-card,
  .category-item,
  .hamburger-menu,
  .search-icon {
    transition: none;
  }

  .live-card:hover,
  .live-card:active {
    transform: none;
  }
}
</style>
