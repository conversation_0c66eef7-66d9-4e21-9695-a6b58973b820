/**
 * 移动端适配样式 - [v20250722]
 * 
 * 提供响应式设计、触摸优化、性能优化等移动端特性
 * 
 * <AUTHOR> Video Platform Team
 * @version 1.0
 */

/* ================================
   基础移动端适配
   ================================ */

/* 防止iOS Safari缩放 */
html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* 移动端触摸优化 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许文本选择的元素 */
input, textarea, [contenteditable] {
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto;
}

/* 移动端滚动优化 */
.scroll-smooth {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* ================================
   响应式断点系统 (优化版)
   ================================ */

/* 超小屏幕 (手机竖屏) - 320px ~ 575px */
@media (max-width: 575.98px) {
  .container {
    padding: 0 12px;
    max-width: 100%;
  }

  /* 字体大小优化 */
  .text-xs { font-size: 11px; line-height: 1.4; }
  .text-sm { font-size: 13px; line-height: 1.4; }
  .text-base { font-size: 15px; line-height: 1.5; }
  .text-lg { font-size: 17px; line-height: 1.5; }
  .text-xl { font-size: 19px; line-height: 1.4; }
  .text-2xl { font-size: 22px; line-height: 1.3; }

  /* 间距优化 */
  .p-sm { padding: 8px; }
  .p-md { padding: 12px; }
  .p-lg { padding: 16px; }
  .m-sm { margin: 8px; }
  .m-md { margin: 12px; }
  .m-lg { margin: 16px; }

  /* 网格系统优化 */
  .grid-cols-1 { grid-template-columns: 1fr; }
  .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .gap-sm { gap: 8px; }
  .gap-md { gap: 12px; }

  /* 按钮优化 */
  .btn {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 15px;
  }

  /* 输入框优化 */
  input, textarea, select {
    min-height: 44px;
    padding: 12px;
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 小屏幕 (手机横屏) - 576px ~ 767px */
@media (min-width: 576px) and (max-width: 767.98px) {
  .container {
    padding: 0 16px;
    max-width: 100%;
  }

  .text-base { font-size: 16px; }
  .text-lg { font-size: 18px; }
  .text-xl { font-size: 20px; }

  .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }

  .btn {
    min-height: 42px;
    padding: 10px 16px;
  }
}

/* 中等屏幕 (平板竖屏) - 768px ~ 991px */
@media (min-width: 768px) and (max-width: 991.98px) {
  .container {
    padding: 0 24px;
    max-width: 100%;
  }

  .text-base { font-size: 16px; }
  .text-lg { font-size: 18px; }
  .text-xl { font-size: 22px; }
  .text-2xl { font-size: 26px; }

  .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

  .btn {
    min-height: 40px;
    padding: 10px 20px;
  }
}

/* 大屏幕 (平板横屏/小桌面) - 992px ~ 1199px */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .container {
    padding: 0 32px;
    max-width: 1140px;
    margin: 0 auto;
  }

  .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
}

/* 超大屏幕 (桌面) - 1200px+ */
@media (min-width: 1200px) {
  .container {
    padding: 0 40px;
    max-width: 1320px;
    margin: 0 auto;
  }

  .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

/* ================================
   触摸友好的交互元素
   ================================ */

/* 按钮最小触摸区域 44px */
.btn, button, .clickable {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 触摸反馈 */
.btn:active, button:active, .clickable:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 链接触摸区域扩展 */
.link-touch {
  position: relative;
}

.link-touch::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  z-index: 1;
}

/* ================================
   移动端布局工具类
   ================================ */

/* Flexbox工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

/* 间距工具类 */
.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }
.p-6 { padding: 24px; }

.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }
.m-6 { margin: 24px; }

/* 宽度工具类 */
.w-full { width: 100%; }
.w-1/2 { width: 50%; }
.w-1/3 { width: 33.333333%; }
.w-2/3 { width: 66.666667%; }
.w-1/4 { width: 25%; }
.w-3/4 { width: 75%; }

/* 高度工具类 */
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-auto { height: auto; }

/* ================================
   移动端特定组件样式
   ================================ */

/* 移动端导航栏 */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #000000;
  border-top: 1px solid #333333;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666666;
  text-decoration: none;
  font-size: 12px;
  min-width: 44px;
  min-height: 44px;
  transition: color 0.2s ease;
}

.mobile-nav-item.active {
  color: #1989fa;
}

.mobile-nav-item:active {
  transform: scale(0.95);
}

/* 移动端头部 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  z-index: 1000;
  padding-top: env(safe-area-inset-top);
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* ================================
   性能优化
   ================================ */

/* GPU加速 */
.gpu-accelerated {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
}

/* 减少重绘 */
.no-repaint {
  will-change: auto;
  transform: translateZ(0);
}

/* 滚动性能优化 */
.scroll-optimized {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* ================================
   暗色模式适配
   ================================ */

@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .auto-dark-border {
    border-color: #333333;
  }
  
  .auto-dark-text {
    color: #cccccc;
  }
}

/* ================================
   可访问性优化
   ================================ */

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .btn, button {
    border: 2px solid currentColor;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 焦点可见性 */
.focus-visible:focus-visible {
  outline: 2px solid #1989fa;
  outline-offset: 2px;
}

/* ================================
   响应式显示工具类
   ================================ */

/* 移动端显示/隐藏 */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }
}

/* 特定断点显示/隐藏 */
.hidden-xs { display: none; }
.hidden-sm { display: block; }
.hidden-md { display: block; }
.hidden-lg { display: block; }

@media (min-width: 576px) {
  .hidden-xs { display: block; }
  .hidden-sm { display: none; }
}

@media (min-width: 768px) {
  .hidden-sm { display: block; }
  .hidden-md { display: none; }
}

@media (min-width: 992px) {
  .hidden-md { display: block; }
  .hidden-lg { display: none; }
}

/* ================================
   响应式网格系统
   ================================ */

.responsive-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 576px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 992px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1200px) {
  .responsive-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* ================================
   工具类
   ================================ */

/* 隐藏元素但保持可访问性 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 截断文本 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式文本对齐 */
.text-center-mobile {
  text-align: center;
}

@media (min-width: 768px) {
  .text-center-mobile {
    text-align: left;
  }
}

/* ================================
   响应式间距工具类
   ================================ */

/* 响应式内边距 */
.p-responsive {
  padding: 8px;
}

@media (min-width: 576px) {
  .p-responsive {
    padding: 12px;
  }
}

@media (min-width: 768px) {
  .p-responsive {
    padding: 16px;
  }
}

@media (min-width: 992px) {
  .p-responsive {
    padding: 20px;
  }
}

/* 响应式外边距 */
.m-responsive {
  margin: 8px;
}

@media (min-width: 576px) {
  .m-responsive {
    margin: 12px;
  }
}

@media (min-width: 768px) {
  .m-responsive {
    margin: 16px;
  }
}

@media (min-width: 992px) {
  .m-responsive {
    margin: 20px;
  }
}

/* 响应式间隙 */
.gap-responsive {
  gap: 8px;
}

@media (min-width: 576px) {
  .gap-responsive {
    gap: 12px;
  }
}

@media (min-width: 768px) {
  .gap-responsive {
    gap: 16px;
  }
}

@media (min-width: 992px) {
  .gap-responsive {
    gap: 20px;
  }
}

/* ================================
   响应式字体大小工具类
   ================================ */

.text-responsive-sm {
  font-size: 12px;
}

@media (min-width: 576px) {
  .text-responsive-sm {
    font-size: 13px;
  }
}

@media (min-width: 768px) {
  .text-responsive-sm {
    font-size: 14px;
  }
}

.text-responsive-base {
  font-size: 14px;
}

@media (min-width: 576px) {
  .text-responsive-base {
    font-size: 15px;
  }
}

@media (min-width: 768px) {
  .text-responsive-base {
    font-size: 16px;
  }
}

.text-responsive-lg {
  font-size: 16px;
}

@media (min-width: 576px) {
  .text-responsive-lg {
    font-size: 17px;
  }
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 18px;
  }
}

@media (min-width: 992px) {
  .text-responsive-lg {
    font-size: 20px;
  }
}

.text-responsive-xl {
  font-size: 18px;
}

@media (min-width: 576px) {
  .text-responsive-xl {
    font-size: 20px;
  }
}

@media (min-width: 768px) {
  .text-responsive-xl {
    font-size: 22px;
  }
}

@media (min-width: 992px) {
  .text-responsive-xl {
    font-size: 24px;
  }
}

/* ================================
   响应式容器工具类
   ================================ */

.container-responsive {
  width: 100%;
  padding: 0 12px;
  margin: 0 auto;
}

@media (min-width: 576px) {
  .container-responsive {
    padding: 0 16px;
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    padding: 0 20px;
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container-responsive {
    padding: 0 24px;
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container-responsive {
    padding: 0 32px;
    max-width: 1140px;
  }
}

/* ================================
   响应式按钮工具类
   ================================ */

.btn-responsive {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  min-height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

@media (min-width: 576px) {
  .btn-responsive {
    padding: 10px 20px;
    font-size: 15px;
    min-height: 42px;
  }
}

@media (min-width: 768px) {
  .btn-responsive {
    padding: 12px 24px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 8px;
  }
}

@media (min-width: 992px) {
  .btn-responsive {
    padding: 14px 28px;
    min-height: 48px;
  }
}

/* ================================
   响应式卡片工具类
   ================================ */

.card-responsive {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@media (min-width: 576px) {
  .card-responsive {
    padding: 16px;
    border-radius: 10px;
  }
}

@media (min-width: 768px) {
  .card-responsive {
    padding: 20px;
    border-radius: 12px;
  }
}

@media (min-width: 992px) {
  .card-responsive {
    padding: 24px;
    border-radius: 16px;
  }
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
