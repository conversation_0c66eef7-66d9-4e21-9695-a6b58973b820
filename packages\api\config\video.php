<?php

return [
    // FFmpeg可执行文件路径
    'ffmpeg_path' => env('FFMPEG_PATH', '/usr/bin/ffmpeg'),
    
    // FFprobe可执行文件路径
    'ffprobe_path' => env('FFPROBE_PATH', '/usr/bin/ffprobe'),
    
    // 视频处理临时目录
    'temp_dir' => env('VIDEO_TEMP_DIR', app()->getRootPath() . 'runtime/video_temp'),
    
    // 视频输出目录
    'output_dir' => env('VIDEO_OUTPUT_DIR', app()->getRootPath() . 'public/storage/videos'),
    
    // 支持的视频格式
    'supported_formats' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'],
    
    // HLS切片配置
    'hls' => [
        'segment_time' => env('HLS_SEGMENT_TIME', 10), // 切片时长（秒）
        'playlist_type' => env('HLS_PLAYLIST_TYPE', 'vod'), // 播放列表类型
        'segment_format' => env('HLS_SEGMENT_FORMAT', 'ts'), // 切片格式
    ],
    
    // 转码质量配置（优化速度）
    'quality_settings' => [
        '1080p' => [
            'width' => 1920,
            'height' => 1080,
            'bitrate' => '4000k',  // 降低码率以提高速度
            'audio_bitrate' => '128k',  // 降低音频码率
            'preset' => 'fast',  // 使用更快的预设
            'crf' => 25,  // 稍微提高CRF以提高速度
            'tune' => 'fastdecode'  // 优化解码速度
        ],
        '720p' => [
            'width' => 1280,
            'height' => 720,
            'bitrate' => '2500k',  // 降低码率
            'audio_bitrate' => '128k',
            'preset' => 'fast',
            'crf' => 25,
            'tune' => 'fastdecode'
        ],
        '480p' => [
            'width' => 854,
            'height' => 480,
            'bitrate' => '1200k',  // 降低码率
            'audio_bitrate' => '96k',
            'preset' => 'fast',
            'crf' => 27,
            'tune' => 'fastdecode'
        ],
        '360p' => [
            'width' => 640,
            'height' => 360,
            'bitrate' => '700k',  // 降低码率
            'audio_bitrate' => '64k',
            'preset' => 'fast',
            'crf' => 29,
            'tune' => 'fastdecode'
        ]
    ],
    
    // 缩略图配置
    'thumbnail' => [
        'time' => env('THUMBNAIL_TIME', '00:00:01'), // 截取时间点
        'width' => env('THUMBNAIL_WIDTH', 320),
        'height' => env('THUMBNAIL_HEIGHT', 240),
        'format' => env('THUMBNAIL_FORMAT', 'jpg')
    ],
    
    // 视频加密配置
    'encryption' => [
        // 基本加密设置
        'enabled' => env('VIDEO_ENCRYPTION_ENABLED', true),
        'method' => env('VIDEO_ENCRYPTION_METHOD', 'AES-128-CBC'),
        'key_length' => env('VIDEO_ENCRYPTION_KEY_LENGTH', 16), // AES-128需要16字节密钥
        'iv_length' => env('VIDEO_ENCRYPTION_IV_LENGTH', 16),   // AES-128需要16字节IV

        // 密钥轮换设置
        'key_rotation_interval' => env('VIDEO_KEY_ROTATION_INTERVAL', 86400), // 24小时
        'key_rotation_enabled' => env('VIDEO_KEY_ROTATION_ENABLED', true),
        'max_key_age' => env('VIDEO_MAX_KEY_AGE', 604800), // 7天后强制过期

        // 密钥服务设置
        'key_server_url' => env('VIDEO_KEY_SERVER_URL', '/api/video/encryption/key'),
        'key_cache_ttl' => env('VIDEO_KEY_CACHE_TTL', 3600), // 密钥缓存1小时
        'allow_key_caching' => env('VIDEO_ALLOW_KEY_CACHING', true),

        // 安全设置
        'rate_limit_per_minute' => env('VIDEO_KEY_RATE_LIMIT', 100), // 每分钟最多100次密钥请求
        'require_referer_check' => env('VIDEO_REQUIRE_REFERER_CHECK', false),
        'allowed_domains' => env('VIDEO_ALLOWED_DOMAINS', ''), // 允许的域名列表，逗号分隔

        // 加密质量级别
        'encrypt_qualities' => explode(',', env('VIDEO_ENCRYPT_QUALITIES', '720p,480p,360p')),
        'encrypt_hls_only' => env('VIDEO_ENCRYPT_HLS_ONLY', true), // 仅加密HLS，不加密MP4

        // 日志和监控
        'log_key_access' => env('VIDEO_LOG_KEY_ACCESS', true),
        'log_retention_days' => env('VIDEO_LOG_RETENTION_DAYS', 90),
        'enable_access_stats' => env('VIDEO_ENABLE_ACCESS_STATS', true)
    ],
    
    // 处理队列配置
    'queue' => [
        'enabled' => env('VIDEO_QUEUE_ENABLED', true),
        'connection' => env('VIDEO_QUEUE_CONNECTION', 'redis'),
        'queue_name' => env('VIDEO_QUEUE_NAME', 'video_processing'),
        'max_attempts' => env('VIDEO_QUEUE_MAX_ATTEMPTS', 3),
        'timeout' => env('VIDEO_QUEUE_TIMEOUT', 3600) // 1小时
    ],
    
    // 性能配置
    'performance' => [
        'max_concurrent_jobs' => env('VIDEO_MAX_CONCURRENT_JOBS', 2),
        'memory_limit' => env('VIDEO_MEMORY_LIMIT', '2G'),
        'max_execution_time' => env('VIDEO_MAX_EXECUTION_TIME', 7200) // 2小时
    ],
    
    // 文件大小限制
    'file_limits' => [
        'max_file_size' => env('VIDEO_MAX_FILE_SIZE', 2 * 1024 * 1024 * 1024), // 2GB
        'min_file_size' => env('VIDEO_MIN_FILE_SIZE', 1024), // 1KB
        'max_duration' => env('VIDEO_MAX_DURATION', 7200) // 2小时
    ],
    
    // 清理配置
    'cleanup' => [
        'temp_files_ttl' => env('VIDEO_TEMP_FILES_TTL', 86400), // 24小时
        'failed_jobs_ttl' => env('VIDEO_FAILED_JOBS_TTL', 604800), // 7天
        'auto_cleanup' => env('VIDEO_AUTO_CLEANUP', true)
    ]
];
