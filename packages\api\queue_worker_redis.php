<?php
/**
 * Redis队列工作进程启动脚本
 * 使用ThinkPHP队列系统处理视频任务
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\Log;
use think\worker\Server;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

echo "🚀 启动Redis队列工作进程...\n";
echo "📅 启动时间: " . date('Y-m-d H:i:s') . "\n";
echo "🔄 监听队列: video_processing (Redis)\n";
echo "📝 日志级别: INFO\n";
echo "----------------------------------------\n";

$processedCount = 0;
$errorCount = 0;

// 主循环
while (true) {
    try {
        // 使用ThinkPHP队列系统处理任务
        $command = 'php think queue:work --queue=video_processing --sleep=3 --tries=3 --timeout=300';
        
        echo "🔄 执行队列命令: $command\n";
        
        // 执行队列处理命令
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            foreach ($output as $line) {
                echo "📝 " . $line . "\n";
                if (strpos($line, 'Processing:') !== false) {
                    $processedCount++;
                } elseif (strpos($line, 'Failed:') !== false) {
                    $errorCount++;
                }
            }
        } else {
            echo "❌ 队列命令执行失败，返回码: $returnCode\n";
            foreach ($output as $line) {
                echo "📝 " . $line . "\n";
            }
            $errorCount++;
        }
        
        // 显示统计信息
        echo "📊 统计: 已处理 $processedCount 个, 错误 $errorCount 个\n";
        
        // 短暂休息
        sleep(5);
        
    } catch (\Exception $e) {
        echo "❌ 队列工作进程异常: " . $e->getMessage() . "\n";
        Log::error('队列工作进程异常', [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        
        $errorCount++;
        sleep(10); // 出错后等待更长时间
    }
}
