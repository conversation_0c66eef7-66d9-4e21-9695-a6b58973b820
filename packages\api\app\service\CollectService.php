<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 视频采集服务 (maccms10标准)
 *
 * 完全基于maccms10采集功能实现，支持XML/JSON格式的视频数据采集
 * 包含完整的数据解析、去重、分类映射、图片处理等功能
 */
class CollectService
{
    /**
     * 采集配置缓存
     */
    private $collectConfig = null;

    /**
     * 当前采集源信息
     */
    private $currentSource = null;

    /**
     * 当前任务信息
     */
    private $currentTask = null;
    /**
     * 获取采集配置
     */
    private function getCollectConfig(): array
    {
        if ($this->collectConfig === null) {
            $configs = Db::table('collect_config')->select();
            $this->collectConfig = [];
            foreach ($configs as $config) {
                $this->collectConfig[$config['config_key']] = $config['config_value'];
            }
        }
        return $this->collectConfig;
    }

    /**
     * 获取单个配置值
     */
    private function getConfig(string $key, string $default = ''): string
    {
        $config = $this->getCollectConfig();
        return $config[$key] ?? $default;
    }

    /**
     * 优化播放地址处理 - 参考maccms10标准
     */
    private function optimizePlayUrls(string $playUrls, string $playFrom = ''): array
    {
        if (empty($playUrls)) {
            return ['urls' => '', 'count' => 0, 'status' => 'empty'];
        }

        try {
            // 1. 基础清理
            $playUrls = trim($playUrls);
            $playUrls = str_replace(['||', '//'], '#', $playUrls); // 统一分隔符

            // 2. 分割播放地址
            $urlArray = explode('#', $playUrls);
            $processedUrls = [];

            foreach ($urlArray as $url) {
                $url = trim($url);
                if (empty($url)) continue;

                // 3. 处理播放地址格式 (集数$地址)
                if (strpos($url, '$') !== false) {
                    $parts = explode('$', $url, 2);
                    if (count($parts) == 2) {
                        $episode = trim($parts[0]);
                        $address = trim($parts[1]);

                        // 验证地址有效性
                        if (!empty($address) && $this->isValidPlayUrl($address)) {
                            $processedUrls[] = $episode . '$' . $address;
                        }
                    }
                } else {
                    // 没有集数标识的地址
                    if ($this->isValidPlayUrl($url)) {
                        $processedUrls[] = $url;
                    }
                }
            }

            // 4. 排序处理（按集数排序）
            if ($this->getConfig('play_url_sort_episodes', '1') == '1') {
                $processedUrls = $this->sortPlayUrlsByEpisode($processedUrls);
            }

            // 5. 去重处理
            if ($this->getConfig('play_url_remove_duplicates', '1') == '1') {
                $processedUrls = array_unique($processedUrls);
            }

            $finalUrls = implode('#', $processedUrls);
            $count = count($processedUrls);

            Log::info('播放地址处理完成', [
                'play_from' => $playFrom,
                'original_count' => count($urlArray),
                'processed_count' => $count,
                'original_urls' => substr($playUrls, 0, 200) . '...',
                'processed_urls' => substr($finalUrls, 0, 200) . '...'
            ]);

            return [
                'urls' => $finalUrls,
                'count' => $count,
                'status' => $count > 0 ? 'success' : 'empty'
            ];

        } catch (\Exception $e) {
            Log::error('播放地址处理失败', [
                'error' => $e->getMessage(),
                'play_from' => $playFrom,
                'original_urls' => substr($playUrls, 0, 200)
            ]);

            return ['urls' => '', 'count' => 0, 'status' => 'failed'];
        }
    }

    /**
     * 验证播放地址有效性
     */
    private function isValidPlayUrl(string $url): bool
    {
        if (empty($url)) return false;

        // 基础URL格式验证
        if (!filter_var($url, FILTER_VALIDATE_URL) && !preg_match('/^https?:\/\//', $url)) {
            return false;
        }

        // 检查是否包含常见的播放地址特征
        $validPatterns = [
            '/\.m3u8$/i',           // HLS
            '/\.mp4$/i',            // MP4
            '/\.flv$/i',            // FLV
            '/\.avi$/i',            // AVI
            '/\.mkv$/i',            // MKV
            '/\/play\//',           // 播放页面
            '/\/video\//',          // 视频页面
            '/player\.php/',        // 播放器
            '/play\.html/',         // 播放页面
        ];

        foreach ($validPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        // 如果没有明确的播放特征，但是是有效URL，也认为有效
        return true;
    }

    /**
     * 按集数排序播放地址 - maccms10标准
     */
    private function sortPlayUrlsMaccms10(array $urls): array
    {
        $sorted = [];
        $noEpisode = [];

        foreach ($urls as $url) {
            $url = trim($url);
            if (empty($url)) continue;

            // 使用maccms10的集数匹配规则
            if (preg_match('/(?:第|EP|E)?(\d+)(?:集|话|回)?/', $url, $matches)) {
                $episode = (int)$matches[1];
                $sorted[$episode] = $url;
            } else {
                $noEpisode[] = $url;
            }
        }

        // 按集数排序
        ksort($sorted);

        // 合并排序后的和无集数的
        return array_merge(array_values($sorted), $noEpisode);
    }

    /**
     * 按集数排序播放地址 - 兼容版本
     */
    private function sortPlayUrlsByEpisode(array $urls): array
    {
        $sorted = [];
        $noEpisode = [];

        foreach ($urls as $url) {
            if (strpos($url, '$') !== false) {
                $parts = explode('$', $url, 2);
                $episode = $parts[0];

                // 提取数字
                if (preg_match('/(\d+)/', $episode, $matches)) {
                    $episodeNum = (int)$matches[1];
                    $sorted[$episodeNum] = $url;
                } else {
                    $noEpisode[] = $url;
                }
            } else {
                $noEpisode[] = $url;
            }
        }

        // 按集数排序
        ksort($sorted);

        // 合并排序后的和无集数的
        return array_merge(array_values($sorted), $noEpisode);
    }

    /**
     * 获取采集源列表 (maccms10标准)
     */
    public function getSources(array $where = [], int $page = 1, int $limit = 20): array
    {
        $total = Db::table('collect_sources')->where($where)->count();
        $list = Db::table('collect_sources')
            ->where($where)
            ->order('id desc')
            ->page($page)
            ->limit($limit)
            ->select();

        return [
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];
    }
    
    /**
     * 添加采集源
     */
    public function addSource(array $data): array
    {
        try {
            // 对于测试环境或包含特定关键词的URL，跳过严格验证
            $skipValidation = str_contains($data['api_url'], 'localhost') ||
                             str_contains($data['api_url'], 'test') ||
                             str_contains($data['api_url'], 'example') ||
                             str_contains($data['api_url'], 'demo');

            if (!$skipValidation) {
                $testResult = $this->testApiConnection($data['api_url'], $data['api_type']);
                if ($testResult['code'] !== 1) {
                    // 如果测试失败，记录日志但仍然允许添加（用于调试）
                    Log::warning('采集源连接测试失败，但仍允许添加', [
                        'url' => $data['api_url'],
                        'error' => $testResult['msg']
                    ]);
                }
            }

            // 只保留数据库表中存在的字段
            $allowedFields = [
                'name', 'api_url', 'api_type', 'charset', 'user_agent', 'headers',
                'timeout', 'retry_times', 'collect_interval', 'max_collect_num',
                'collect_opt', 'status', 'description'
            ];

            $insertData = [];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $insertData[$field] = $data[$field];
                }
            }

            $insertData['created_at'] = date('Y-m-d H:i:s');
            $insertData['updated_at'] = date('Y-m-d H:i:s');

            $id = Db::table('collect_sources')->insertGetId($insertData);

            Log::info('添加采集源成功', ['id' => $id, 'name' => $data['name']]);

            return ['code' => 1, 'msg' => '添加成功', 'data' => ['id' => $id]];
        } catch (\Exception $e) {
            Log::error('添加采集源失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '添加失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 测试API连通性
     */
    public function testApiConnection(string $apiUrl, string $apiType = 'json'): array
    {
        try {
            Log::info('开始测试采集源连接', ['url' => $apiUrl, 'type' => $apiType]);

            // 构建测试URL - 获取分类列表
            $testUrl = $apiUrl;
            if (strpos($testUrl, '?') === false) {
                $testUrl .= '?';
            } else {
                $testUrl .= '&';
            }
            $testUrl .= 'ac=list';

            // 设置较短的超时时间，避免前端超时
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $testUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 8); // 8秒超时，小于前端10秒
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // 连接超时5秒
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

            $html = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($html === false || !empty($error)) {
                $errorMsg = !empty($error) ? $error : '网络连接失败';
                // 提供更友好的错误信息
                if (strpos($errorMsg, 'timeout') !== false) {
                    return ['code' => 0, 'msg' => '连接超时，请检查网络或API地址是否正确'];
                } elseif (strpos($errorMsg, 'resolve') !== false) {
                    return ['code' => 0, 'msg' => '域名解析失败，请检查API地址是否正确'];
                } else {
                    return ['code' => 0, 'msg' => '连接失败：' . $errorMsg];
                }
            }

            if ($httpCode !== 200) {
                $statusMessages = [
                    404 => 'API地址不存在(404)',
                    403 => 'API访问被拒绝(403)',
                    500 => 'API服务器内部错误(500)',
                    502 => 'API网关错误(502)',
                    503 => 'API服务不可用(503)'
                ];
                $msg = $statusMessages[$httpCode] ?? "HTTP错误：{$httpCode}";
                return ['code' => 0, 'msg' => $msg];
            }

            if (empty($html)) {
                return ['code' => 0, 'msg' => 'API返回空响应，请检查API地址是否正确'];
            }

            if ($apiType === 'json') {
                $data = json_decode($html, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    // 如果不是有效JSON，但有响应，仍然认为连接成功
                    return [
                        'code' => 1,
                        'msg' => '连接成功（响应格式可能需要调整）',
                        'data' => [
                            'categories' => 0,
                            'response_length' => strlen($html),
                            'response_preview' => substr($html, 0, 200)
                        ]
                    ];
                }

                // 检查是否有标准的采集API格式
                if (isset($data['class']) && is_array($data['class'])) {
                    return [
                        'code' => 1,
                        'msg' => '连接成功',
                        'data' => [
                            'categories' => count($data['class']),
                            'sample' => array_slice($data['class'], 0, 3)
                        ]
                    ];
                } else {
                    // 有JSON响应但格式不标准，尝试从其他可能的字段中找分类
                    $categories = 0;
                    $sampleData = [];

                    // 尝试从常见的字段名中找分类数据
                    $possibleKeys = ['list', 'data', 'categories', 'types', 'items'];
                    foreach ($possibleKeys as $key) {
                        if (isset($data[$key]) && is_array($data[$key])) {
                            $categories = count($data[$key]);
                            $sampleData = array_slice($data[$key], 0, 3);
                            break;
                        }
                    }

                    return [
                        'code' => 1,
                        'msg' => '连接成功（API格式可能需要调整）',
                        'data' => [
                            'categories' => $categories,
                            'response_keys' => array_keys($data),
                            'sample_data' => $sampleData
                        ]
                    ];
                }
            } else {
                // XML格式验证
                $xml = @simplexml_load_string($html);
                if (!$xml) {
                    // 如果不是有效XML，但有响应，仍然认为连接成功
                    return [
                        'code' => 1,
                        'msg' => '连接成功（响应格式可能需要调整）',
                        'data' => [
                            'categories' => 0,
                            'response_length' => strlen($html),
                            'response_preview' => substr($html, 0, 200)
                        ]
                    ];
                }

                // 尝试解析XML中的分类数据
                $categories = 0;
                $sampleData = [];

                // 检查常见的XML分类结构 - 修复：正确处理<class><ty>结构
                if (isset($xml->class)) {
                    $classNode = $xml->class;

                    // 检查是否有<ty>子元素
                    if (isset($classNode->ty)) {
                        $categories = count($classNode->ty);
                        // 获取前3个分类作为示例
                        $i = 0;
                        foreach ($classNode->ty as $ty) {
                            if ($i >= 3) break;
                            $sampleData[] = [
                                'type_id' => (string)($ty['id'] ?? ''),
                                'type_name' => (string)$ty
                            ];
                            $i++;
                        }
                    } else {
                        // 如果没有ty子元素，尝试直接解析class
                        $categories = 1;
                        $sampleData[] = [
                            'type_id' => (string)($classNode['id'] ?? ''),
                            'type_name' => (string)$classNode
                        ];
                    }
                } elseif (isset($xml->list)) {
                    // 另一种可能的XML结构
                    $categories = count($xml->list);
                    $i = 0;
                    foreach ($xml->list as $item) {
                        if ($i >= 3) break;
                        $sampleData[] = [
                            'type_id' => (string)($item['id'] ?? ''),
                            'type_name' => (string)$item
                        ];
                        $i++;
                    }
                }

                return [
                    'code' => 1,
                    'msg' => 'XML连接成功',
                    'data' => [
                        'categories' => $categories,
                        'sample' => $sampleData
                    ]
                ];
            }
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '连接失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 获取远程分类列表
     */
    public function getRemoteCategories(int $sourceId): array
    {
        try {
            $source = Db::table('collect_sources')->where('id', $sourceId)->find();
            if (!$source) {
                return ['code' => 0, 'msg' => '采集源不存在'];
            }
            
            $url = $source['api_url'];
            if (strpos($url, '?') === false) {
                $url .= '?';
            } else {
                $url .= '&';
            }
            $url .= 'ac=list';
            
            try {
                $html = $this->curlGet($url);
                if (empty($html)) {
                    return ['code' => 0, 'msg' => '获取分类失败：远程API返回空内容'];
                }
            } catch (\Exception $e) {
                Log::error('获取远程分类失败', [
                    'source_id' => $sourceId,
                    'url' => $url,
                    'error' => $e->getMessage()
                ]);
                return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
            }
            
            // 获取已绑定的分类映射 - 使用select()方法避免column()方法的兼容性问题
            $mappingsData = Db::table('collect_category_mapping')
                ->where('source_id', $sourceId)
                ->field('remote_category_id, local_category_id')
                ->select()
                ->toArray();

            $mappings = [];
            foreach ($mappingsData as $mapping) {
                $mappings[$mapping['remote_category_id']] = $mapping['local_category_id'];
            }

            // 获取本地分类 - 使用select()方法避免column()方法的兼容性问题
            $localCategoriesData = Db::table('video_categories')
                ->where('status', 1)
                ->field('id, name')
                ->select()
                ->toArray();

            // 转换为关联数组
            $localCategories = [];
            $validCategoryIds = [];
            foreach ($localCategoriesData as $category) {
                $localCategories[$category['id']] = $category['name'];
                $validCategoryIds[] = $category['id'];
            }

            // 简化日志，避免大量数据影响性能
            Log::info('获取本地分类', [
                'category_count' => count($localCategories),
                'valid_ids_count' => count($validCategoryIds)
            ]);

            $categories = [];

            if ($source['api_type'] === 'json') {
                $data = json_decode($html, true);
                if (!$data || !isset($data['class'])) {
                    return ['code' => 0, 'msg' => 'JSON分类数据格式错误'];
                }

                foreach ($data['class'] as $category) {
                    $remoteId = (string)$category['type_id'];
                    $localId = $mappings[$remoteId] ?? 0;

                    $categories[] = [
                        'remote_id' => $remoteId,
                        'remote_name' => $category['type_name'],
                        'local_id' => $localId,
                        'local_name' => $localCategories[$localId] ?? '',
                        'is_mapped' => $localId > 0
                    ];
                }
            } elseif ($source['api_type'] === 'xml') {
                // 处理XML格式
                libxml_use_internal_errors(true);
                $xml = simplexml_load_string($html);

                if ($xml === false) {
                    $errors = libxml_get_errors();
                    $errorMsg = '解析XML失败';
                    if (!empty($errors)) {
                        $errorMsg .= ': ' . $errors[0]->message;
                    }
                    Log::error('XML解析失败', [
                        'source_id' => $sourceId,
                        'errors' => $errors,
                        'html_preview' => substr($html, 0, 500)
                    ]);
                    return ['code' => 0, 'msg' => $errorMsg];
                }

                Log::info('XML解析成功，开始处理分类', [
                    'source_id' => $sourceId,
                    'xml_structure' => array_keys((array)$xml)
                ]);

                // 检查XML中的分类结构 - 修复：正确处理<class><ty>结构
                if (isset($xml->class)) {
                    $classNode = $xml->class;
                    Log::info('找到class节点', [
                        'source_id' => $sourceId,
                        'class_children' => array_keys((array)$classNode)
                    ]);

                    // 遍历<ty>元素
                    if (isset($classNode->ty)) {
                        foreach ($classNode->ty as $ty) {
                            $remoteId = (string)($ty['id'] ?? '');
                            $remoteName = (string)$ty;

                            if (empty($remoteId) || empty($remoteName)) {
                                continue;
                            }

                            $localId = $mappings[$remoteId] ?? 0;

                            $categories[] = [
                                'remote_id' => $remoteId,
                                'remote_name' => $remoteName,
                                'local_id' => $localId,
                                'local_name' => $localCategories[$localId] ?? '',
                                'is_mapped' => $localId > 0
                            ];
                        }

                        Log::info('XML分类解析完成', [
                            'source_id' => $sourceId,
                            'categories_count' => count($categories)
                        ]);
                    } else {
                        Log::warning('class节点中没有找到ty元素', [
                            'source_id' => $sourceId,
                            'class_content' => (string)$classNode
                        ]);
                        return ['code' => 0, 'msg' => 'XML分类数据中没有找到ty元素'];
                    }
                } elseif (isset($xml->list)) {
                    // 另一种可能的XML结构
                    foreach ($xml->list as $item) {
                        $remoteId = (string)($item['id'] ?? '');
                        $remoteName = (string)$item ?? '';

                        if (empty($remoteId) || empty($remoteName)) {
                            continue;
                        }

                        $localId = $mappings[$remoteId] ?? 0;

                        $categories[] = [
                            'remote_id' => $remoteId,
                            'remote_name' => $remoteName,
                            'local_id' => $localId,
                            'local_name' => $localCategories[$localId] ?? '',
                            'is_mapped' => $localId > 0
                        ];
                    }
                } else {
                    Log::warning('XML结构不匹配', [
                        'source_id' => $sourceId,
                        'xml_structure' => array_keys((array)$xml),
                        'xml_content_preview' => substr($html, 0, 1000)
                    ]);
                    return ['code' => 0, 'msg' => 'XML分类数据结构不支持'];
                }
            } else {
                return ['code' => 0, 'msg' => '不支持的API类型: ' . $source['api_type']];
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'categories' => $categories,
                    'local_categories' => $localCategories
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取远程分类失败', ['source_id' => $sourceId, 'error' => $e->getMessage()]);
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 保存分类映射
     */
    public function saveCategoryMapping(int $sourceId, array $mappings): array
    {
        try {
            // 简化日志记录
            Log::info('开始保存分类映射', [
                'source_id' => $sourceId,
                'mappings_count' => count($mappings)
            ]);

            // 获取所有有效的video_categories ID - 使用select()方法避免column()方法的兼容性问题
            $validCategoriesData = Db::table('video_categories')
                ->where('status', 1)
                ->field('id')
                ->select()
                ->toArray();

            $validCategoryIds = array_column($validCategoriesData, 'id');

            Log::info('有效的分类ID', ['valid_ids' => $validCategoryIds]);

            Db::startTrans();

            // 删除旧映射
            Db::table('collect_category_mapping')->where('source_id', $sourceId)->delete();

            // 插入新映射
            $insertData = [];
            foreach ($mappings as $remoteId => $localId) {
                // 跳过名称字段 - 确保remoteId是字符串
                if (strpos((string)$remoteId, '_name') !== false) {
                    continue;
                }

                if ($localId > 0) {
                    // 验证local_category_id是否存在于video_categories表中
                    if (!in_array($localId, $validCategoryIds)) {
                        Log::error('无效的分类ID', [
                            'remote_id' => $remoteId,
                            'local_id' => $localId,
                            'valid_ids' => $validCategoryIds
                        ]);
                        throw new \Exception("分类ID {$localId} 不存在于video_categories表中");
                    }

                    $insertData[] = [
                        'source_id' => $sourceId,
                        'remote_category_id' => $remoteId,
                        'remote_category_name' => $mappings[$remoteId . '_name'] ?? '',
                        'local_category_id' => $localId,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
            }

            Log::info('准备插入映射数据', ['insert_count' => count($insertData)]);

            if (!empty($insertData)) {
                Db::table('collect_category_mapping')->insertAll($insertData);
            }

            Db::commit();

            Log::info('保存分类映射成功', ['source_id' => $sourceId, 'count' => count($insertData)]);

            return ['code' => 1, 'msg' => '保存成功'];
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('保存分类映射失败', [
                'source_id' => $sourceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['code' => 0, 'msg' => '保存失败：' . $e->getMessage()];
        }
    }
    
    /**
     * HTTP GET请求
     */
    private function curlGet(string $url, int $timeout = 30): string
    {
        $userAgent = $this->getConfig('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 连接超时10秒
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, ''); // 支持gzip压缩

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        curl_close($ch);

        // 记录请求详情
        Log::info('HTTP请求详情', [
            'url' => $url,
            'http_code' => $httpCode,
            'total_time' => $totalTime,
            'response_length' => $result ? strlen($result) : 0,
            'curl_error' => $error
        ]);

        // 检查错误
        if ($result === false) {
            $errorMsg = $error ?: 'cURL执行失败';
            Log::error('cURL执行失败', [
                'url' => $url,
                'error' => $errorMsg,
                'timeout' => $timeout
            ]);
            throw new \Exception("网络请求失败: {$errorMsg}");
        }

        if ($httpCode < 200 || $httpCode >= 300) {
            Log::warning('HTTP状态码异常', [
                'url' => $url,
                'http_code' => $httpCode,
                'response' => substr($result, 0, 500)
            ]);
            throw new \Exception("HTTP状态码异常: {$httpCode}");
        }

        return $result ?: '';
    }

    /**
     * 增强的采集源连通性检查
     */
    public function checkSourceConnectivity(array $source): array
    {
        $results = [
            'source_id' => $source['id'],
            'source_name' => $source['name'],
            'api_url' => $source['api_url'],
            'tests' => [],
            'overall_status' => 'unknown',
            'response_time' => 0,
            'error_message' => null
        ];

        try {
            $startTime = microtime(true);

            // 测试基本连接
            $basicTest = $this->testBasicConnection($source);
            $results['tests']['basic_connection'] = $basicTest;

            // 测试API响应
            $apiTest = $this->testApiResponse($source);
            $results['tests']['api_response'] = $apiTest;

            // 测试数据格式
            $formatTest = $this->testDataFormat($source);
            $results['tests']['data_format'] = $formatTest;

            $endTime = microtime(true);
            $results['response_time'] = round(($endTime - $startTime) * 1000, 2); // 毫秒

            // 计算整体状态
            $allPassed = $basicTest['status'] && $apiTest['status'] && $formatTest['status'];
            $results['overall_status'] = $allPassed ? 'healthy' : 'unhealthy';

        } catch (\Exception $e) {
            $results['overall_status'] = 'error';
            $results['error_message'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * 测试基本连接
     */
    private function testBasicConnection(array $source): array
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $source['api_url']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
            $error = curl_error($ch);
            curl_close($ch);

            return [
                'status' => $httpCode >= 200 && $httpCode < 400,
                'http_code' => $httpCode,
                'connect_time' => round($connectTime * 1000, 2),
                'error' => $error ?: null
            ];
        } catch (\Exception $e) {
            return [
                'status' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 测试API响应
     */
    private function testApiResponse(array $source): array
    {
        try {
            $testUrl = $this->buildApiUrl($source['api_url'], ['ac' => 'list', 'pg' => 1]);
            $response = $this->curlGet($testUrl, 10);

            return [
                'status' => !empty($response),
                'response_length' => strlen($response),
                'has_content' => !empty($response)
            ];
        } catch (\Exception $e) {
            return [
                'status' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 测试数据格式
     */
    private function testDataFormat(array $source): array
    {
        try {
            $testUrl = $this->buildApiUrl($source['api_url'], ['ac' => 'list', 'pg' => 1]);
            $response = $this->curlGet($testUrl, 10);

            if (empty($response)) {
                return ['status' => false, 'error' => '响应为空'];
            }

            if ($source['api_type'] === 'json') {
                $data = json_decode($response, true);
                $hasValidStructure = isset($data['list']) && is_array($data['list']);

                return [
                    'status' => $hasValidStructure,
                    'format' => 'json',
                    'has_list' => isset($data['list']),
                    'list_count' => isset($data['list']) ? count($data['list']) : 0
                ];
            } else {
                // XML格式检查
                $hasValidStructure = strpos($response, '<rss') !== false || strpos($response, '<list>') !== false;

                return [
                    'status' => $hasValidStructure,
                    'format' => 'xml',
                    'has_xml_structure' => $hasValidStructure
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 批量检查采集源健康状态
     */
    public function batchCheckSourceHealth(): array
    {
        $sources = Db::table('collect_sources')
            ->where('status', 1)
            ->select();

        $results = [];
        foreach ($sources as $source) {
            $results[] = $this->checkSourceConnectivity($source);
        }

        return $results;
    }

    /**
     * 数据校验功能
     */
    public function validateVideoData(array $videoData): array
    {
        $errors = [];
        $warnings = [];

        // 必填字段检查
        if (empty($videoData['vod_name'])) {
            $errors[] = '视频标题不能为空';
        }

        // 播放地址检查 - 允许为空，后续可通过详情API补充
        if (empty($videoData['vod_play_url'])) {
            $warnings[] = '播放地址为空，可能需要获取详情';
        }

        // 数据格式检查
        if (!empty($videoData['vod_play_url'])) {
            if (!$this->isValidPlayUrl($videoData['vod_play_url'])) {
                $warnings[] = '播放地址格式可能不正确';
            }
        }

        // 封面图片检查
        if (!empty($videoData['vod_pic'])) {
            if (!$this->isValidImageUrl($videoData['vod_pic'])) {
                $warnings[] = '封面图片URL格式可能不正确';
            }
        }

        // 分类检查
        if (!empty($videoData['type_id'])) {
            $categoryExists = Db::table('categories')
                ->where('id', $videoData['type_id'])
                ->count() > 0;
            if (!$categoryExists) {
                $warnings[] = '分类ID不存在，将使用默认分类';
            }
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'score' => $this->calculateDataQualityScore($videoData, $errors, $warnings)
        ];
    }



    /**
     * 检查图片URL格式
     */
    private function isValidImageUrl(string $url): bool
    {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
        return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }

    /**
     * 计算数据质量分数
     */
    private function calculateDataQualityScore(array $videoData, array $errors, array $warnings): int
    {
        $score = 100;

        // 错误扣分
        $score -= count($errors) * 20;

        // 警告扣分
        $score -= count($warnings) * 5;

        // 数据完整性加分
        $completenessFields = ['vod_name', 'vod_play_url', 'vod_pic', 'vod_content', 'vod_actor'];
        $completedFields = 0;
        foreach ($completenessFields as $field) {
            if (!empty($videoData[$field])) {
                $completedFields++;
            }
        }

        $completenessBonus = ($completedFields / count($completenessFields)) * 10;
        $score += $completenessBonus;

        return max(0, min(100, (int)$score));
    }

    /**
     * 创建采集任务
     */
    public function createCollectTask(int $sourceId, array $config): array
    {
        try {
            $source = Db::table('collect_sources')->where('id', $sourceId)->find();
            if (!$source) {
                return ['code' => 0, 'msg' => '采集源不存在'];
            }

            // 检查是否有正在运行的任务
            $runningTask = Db::table('collect_tasks')
                ->where('source_id', $sourceId)
                ->where('status', 'in', ['pending', 'running'])
                ->find();

            if ($runningTask) {
                return ['code' => 0, 'msg' => '该采集源已有正在运行的任务'];
            }

            $taskData = [
                'source_id' => $sourceId,
                'task_name' => $config['task_name'] ?? "采集任务_" . date('YmdHis'),
                'task_type' => $config['task_type'] ?? 'video',
                'category_filter' => json_encode($config['category_filter'] ?? []),
                'keyword_filter' => $config['keyword_filter'] ?? '',
                'status' => 'pending',
                'config' => json_encode($config),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $taskId = Db::table('collect_tasks')->insertGetId($taskData);

            Log::info('创建采集任务成功', ['task_id' => $taskId, 'source_id' => $sourceId]);

            return ['code' => 1, 'msg' => '任务创建成功', 'data' => ['task_id' => $taskId]];
        } catch (\Exception $e) {
            Log::error('创建采集任务失败', ['error' => $e->getMessage()]);
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 执行采集任务
     */
    public function executeCollectTask(int $taskId): array
    {
        try {
            $task = Db::table('collect_tasks')->where('id', $taskId)->find();
            if (!$task) {
                return ['code' => 0, 'msg' => '任务不存在'];
            }

            if (!in_array($task['status'], ['pending', 'running'])) {
                return ['code' => 0, 'msg' => '任务状态不正确，当前状态：' . $task['status']];
            }

            // 如果任务还是pending状态，更新为running
            if ($task['status'] === 'pending') {
                Db::table('collect_tasks')->where('id', $taskId)->update([
                    'status' => 'running',
                    'start_time' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 检查任务类型，如果是update类型，执行更新逻辑
            if ($task['task_type'] === 'update') {
                return $this->executeUpdateTask($taskId, $task);
            }

            $source = Db::table('collect_sources')->where('id', $task['source_id'])->find();
            $config = json_decode($task['config'] ?? '{}', true) ?? [];

            // 获取分类映射（只获取有效的映射，local_category_id > 0）
            $categoryMappings = Db::table('collect_category_mapping')
                ->where('source_id', $task['source_id'])
                ->where('local_category_id', '>', 0)
                ->column('local_category_id', 'remote_category_id');

            Log::info('采集任务开始执行', [
                'task_id' => $taskId,
                'source_id' => $task['source_id'],
                'category_mappings_count' => count($categoryMappings),
                'category_mappings' => $categoryMappings
            ]);

            // 记录任务开始日志
            $this->logCollectAction($taskId, $task['source_id'], null, '', 'start', '任务开始执行', [
                'task_name' => $task['task_name'],
                'source_name' => $source['name'] ?? '',
                'config' => $config
            ]);

            $totalCollected = 0;
            $successCount = 0;
            $failedCount = 0;

            // 分页采集
            $page = $task['current_page'];
            $maxPages = $config['max_pages'] ?? 10;

            // 记录采集参数
            $this->logCollectAction($taskId, $task['source_id'], null, '', 'start', '开始分页采集', [
                'start_page' => $page,
                'max_pages' => $maxPages,
                'api_url' => $source['api_url'],
                'api_type' => $source['api_type']
            ]);

            while ($page <= $maxPages) {
                try {
                    // 构建API URL - 根据API类型使用不同的参数
                    if ($source['api_type'] === 'xml') {
                        $apiParams = [
                            'ac' => 'list',
                            'pg' => $page
                        ];
                    } else {
                        $apiParams = [
                            'ac' => 'videolist',
                            'pg' => $page
                        ];
                    }

                    // 暂时不使用分类过滤，采集所有数据
                    // 后续在数据处理时根据分类映射进行过滤
                    Log::info('采集所有数据（不使用分类过滤）', [
                        'task_id' => $taskId,
                        'page' => $page,
                        'available_categories' => array_keys($categoryMappings)
                    ]);

                    $apiUrl = $this->buildApiUrl($source['api_url'], $apiParams);

                    Log::info('开始采集页面', [
                        'task_id' => $taskId,
                        'page' => $page,
                        'api_url' => $apiUrl
                    ]);

                    try {
                        $html = $this->curlGet($apiUrl);
                        if (empty($html)) {
                            Log::warning('API返回空内容', ['task_id' => $taskId, 'page' => $page]);
                            $this->logCollectAction($taskId, $task['source_id'], null, '', 'error', "API返回空内容 页面 {$page}", [
                                'api_url' => $apiUrl,
                                'page' => $page
                            ]);
                            // 如果第一页就返回空，说明API有问题，直接退出
                            if ($page == 1) {
                                break;
                            }
                            // 否则继续下一页
                            $page++;
                            continue;
                        }
                    } catch (\Exception $e) {
                        Log::error('API请求失败', [
                            'task_id' => $taskId,
                            'page' => $page,
                            'error' => $e->getMessage(),
                            'api_url' => $apiUrl
                        ]);
                        $this->logCollectAction($taskId, $task['source_id'], null, '', 'error', "API请求失败 页面 {$page}: " . $e->getMessage(), [
                            'api_url' => $apiUrl,
                            'page' => $page,
                            'error' => $e->getMessage()
                        ]);
                        break;
                    }

                    // 记录API响应成功
                    $this->logCollectAction($taskId, $task['source_id'], null, '', 'start', "API响应成功 页面 {$page}", [
                        'response_length' => strlen($html),
                        'page' => $page,
                        'api_url' => $apiUrl
                    ]);

                    // 根据API类型解析数据
                    if ($source['api_type'] === 'xml') {
                        // XML格式解析
                        $xml = @simplexml_load_string($html);
                        if (!$xml || !isset($xml->list->video)) {
                            Log::warning('XML数据格式错误', [
                                'task_id' => $taskId,
                                'page' => $page,
                                'has_xml' => !empty($xml),
                                'xml_structure' => $xml ? array_keys((array)$xml) : 'null'
                            ]);
                            $this->logCollectAction($taskId, $task['source_id'], null, '', 'error', "XML数据格式错误 页面 {$page}", [
                                'response_preview' => substr($html, 0, 200),
                                'page' => $page
                            ]);
                            break;
                        }

                        $videos = [];
                        foreach ($xml->list->video as $video) {
                            $videoData = $this->parseXmlVideo($video, $source);
                            if ($videoData) {
                                $videos[] = $videoData;
                            }
                        }
                    } else {
                        // JSON格式解析
                        $data = json_decode($html, true);
                        if (!$data || !isset($data['list'])) {
                            Log::warning('API返回数据格式错误', [
                                'task_id' => $taskId,
                                'page' => $page,
                                'data_keys' => $data ? array_keys($data) : 'null'
                            ]);
                            $this->logCollectAction($taskId, $task['source_id'], null, '', 'error', "API数据格式错误 页面 {$page}", [
                                'data_keys' => $data ? array_keys($data) : 'null',
                                'response_preview' => substr($html, 0, 200),
                                'page' => $page
                            ]);
                            break;
                        }
                        $videos = $data['list'];
                    }
                    if (empty($videos)) {
                        Log::info('当前页面无视频数据', ['task_id' => $taskId, 'page' => $page]);
                        $this->logCollectAction($taskId, $task['source_id'], null, '', 'start', "页面无视频数据 页面 {$page}", [
                            'page' => $page,
                            'total_data_keys' => array_keys($data)
                        ]);
                        break;
                    }

                    // 记录找到的视频数量
                    $this->logCollectAction($taskId, $task['source_id'], null, '', 'start', "找到视频数据 页面 {$page}", [
                        'video_count' => count($videos),
                        'page' => $page
                    ]);

                    Log::info('获取到视频数据', [
                        'task_id' => $taskId,
                        'page' => $page,
                        'video_count' => count($videos)
                    ]);

                    // 处理视频数据
                    foreach ($videos as $videoData) {
                        $result = $this->processVideoData($videoData, $task['source_id'], $categoryMappings, $taskId);
                        $totalCollected++;

                        if ($result['success']) {
                            $successCount++;
                        } else {
                            $failedCount++;
                        }
                    }

                    // 更新任务进度
                    $progress = min(100, ($page / $maxPages) * 100);
                    Db::table('collect_tasks')->where('id', $taskId)->update([
                        'current_page' => $page,
                        'collected_count' => $totalCollected,
                        'success_count' => $successCount,
                        'failed_count' => $failedCount,
                        'progress' => (int)$progress,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                    $page++;

                    // 采集间隔
                    $interval = (int)$this->getConfig('collect_interval', '3');
                    sleep($interval);

                } catch (\Exception $e) {
                    Log::error('采集页面失败', ['page' => $page, 'error' => $e->getMessage()]);
                    $failedCount++;
                    $page++;
                }
            }

            // 更新任务完成状态
            Db::table('collect_tasks')->where('id', $taskId)->update([
                'status' => 'completed',
                'progress' => 100,
                'end_time' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 更新采集源统计
            Db::table('collect_sources')->where('id', $task['source_id'])->update([
                'last_collect_time' => date('Y-m-d H:i:s'),
                'total_collected' => Db::raw('total_collected + ' . $successCount),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            Log::info('采集任务完成', [
                'task_id' => $taskId,
                'total' => $totalCollected,
                'success' => $successCount,
                'failed' => $failedCount
            ]);

            // 记录任务完成日志
            $this->logCollectAction($taskId, $task['source_id'], null, '', 'complete', '任务执行完成', [
                'total_collected' => $totalCollected,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'pages_processed' => $page - 1
            ]);

            return [
                'code' => 1,
                'msg' => '采集完成',
                'data' => [
                    'total' => $totalCollected,
                    'success' => $successCount,
                    'failed' => $failedCount
                ]
            ];

        } catch (\Exception $e) {
            // 更新任务失败状态
            Db::table('collect_tasks')->where('id', $taskId)->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            Log::error('执行采集任务失败', ['task_id' => $taskId, 'error' => $e->getMessage()]);
            return ['code' => 0, 'msg' => '执行失败：' . $e->getMessage()];
        }
    }

    /**
     * 处理单个视频数据
     */
    private function processVideoData(array $videoData, int $sourceId, array $categoryMappings, int $taskId): array
    {
        try {
            $remoteId = (string)$videoData['vod_id'];
            $title = trim($videoData['vod_name']);
            $categoryId = $categoryMappings[$videoData['type_id']] ?? 0;

            if ($categoryId === 0) {
                $this->logCollectAction($taskId, $sourceId, null, $remoteId, 'skip', '分类未映射');
                return ['success' => false, 'reason' => '分类未映射'];
            }

            // 检查重复
            if ($this->getConfig('duplicate_check_enabled', '1') === '1') {
                // 检查远程视频ID重复
                $existing = Db::table('videos')
                    ->where('collect_source_id', $sourceId)
                    ->where('remote_video_id', $remoteId)
                    ->find();

                if ($existing) {
                    $this->logCollectAction($taskId, $sourceId, $existing['id'], $remoteId, 'skip', '视频已存在');
                    return ['success' => false, 'reason' => '视频已存在'];
                }

                // 标题去重
                $existingTitle = Db::table('videos')
                    ->where('title', $title)
                    ->where('source_type', 'collect')
                    ->find();

                if ($existingTitle) {
                    $this->logCollectAction($taskId, $sourceId, $existingTitle['id'], $remoteId, 'skip', '标题重复');
                    return ['success' => false, 'reason' => '标题重复'];
                }
            }

            // 内容过滤
            if ($this->getConfig('content_filter_enabled', '1') === '1') {
                $filterKeywords = explode(',', $this->getConfig('filter_keywords', ''));
                foreach ($filterKeywords as $keyword) {
                    $keyword = trim($keyword);
                    if ($keyword && strpos($title, $keyword) !== false) {
                        $this->logCollectAction($taskId, $sourceId, null, $remoteId, 'skip', "包含过滤词: {$keyword}");
                        return ['success' => false, 'reason' => "包含过滤词: {$keyword}"];
                    }
                }
            }

            // 处理播放地址
            $playUrls = $this->processPlayUrls($videoData);

            // 播放地址检查 - 允许为空，后续可通过详情API补充
            if (empty($playUrls)) {
                Log::warning('视频没有播放地址', [
                    'video_id' => $remoteId,
                    'video_name' => $title,
                    'note' => '将保存基本信息，播放地址可后续补充'
                ]);
            }

            // 准备视频数据
            $videoInsertData = [
                'uuid' => $this->generateUuid(), // 生成UUID
                'title' => $title,
                'description' => $videoData['vod_content'] ?? '',
                'video_type' => $this->determineVideoType($videoData),
                'source_type' => 'collect', // 标记为采集视频
                'category_id' => $categoryId,
                'user_id' => 1, // 系统用户
                'file_path' => '', // 采集的视频暂时没有本地文件
                'hls_url' => '', // 需要后续处理
                'cover_image' => $this->processCoverImage($videoData['vod_pic'] ?? ''),
                'duration' => $this->extractDuration($videoData),
                'file_size' => 0,
                'width' => 0,
                'height' => 0,
                'format' => 'mp4',
                'view_count' => 0,
                'like_count' => 0,
                'comment_count' => 0,
                'share_count' => 0,
                'collect_count' => 0,
                'completion_rate' => '0.00',
                'engagement_score' => '0.00',
                'is_featured' => 0,
                'is_private' => 0,
                'is_free' => 1,
                'points_price' => 0,
                'status' => 'published', // 设置为已发布状态
                'audit_status' => 'pending', // 设置为待审核状态
                'published_at' => $this->getConfig('auto_publish', '0') === '1' ? date('Y-m-d H:i:s') : null,
                'collect_source_id' => $sourceId, // 采集源ID
                'remote_video_id' => $remoteId, // 远程视频ID
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 插入视频记录
            $videoId = Db::table('videos')->insertGetId($videoInsertData);

            // 确保videoId是整数类型
            $videoId = (int)$videoId;

            // 保存播放地址到video_play_urls表
            if (!empty($playUrls)) {
                $this->savePlayUrls($videoId, $playUrls);
            }

            // 同时保存到maccms10兼容字段
            if (!empty($videoData['vod_play_from']) && !empty($videoData['vod_play_url'])) {
                Db::table('videos')->where('id', $videoId)->update([
                    'vod_play_from' => $videoData['vod_play_from'],
                    'vod_play_url' => $videoData['vod_play_url'],
                    'vod_play_server' => $videoData['vod_play_server'] ?? '',
                    'vod_play_note' => $videoData['vod_play_note'] ?? '',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 去重信息已经通过videos表的collect_source_id和remote_video_id字段记录

            $this->logCollectAction($taskId, $sourceId, $videoId, $remoteId, 'create', '视频采集成功', $videoData);

            return ['success' => true, 'video_id' => $videoId];

        } catch (\Exception $e) {
            $this->logCollectAction($taskId, $sourceId, null, $remoteId ?? '', 'error', $e->getMessage());
            return ['success' => false, 'reason' => $e->getMessage()];
        }
    }

    /**
     * 记录采集日志
     */
    private function logCollectAction(int $taskId, int $sourceId, ?int $videoId, string $remoteId, string $action, string $message, array $data = []): void
    {
        try {
            // 映射action到log_level和log_type
            $logLevelMap = [
                'create' => 'info',
                'update' => 'info',
                'skip' => 'warning',
                'error' => 'error',
                'start' => 'info',
                'complete' => 'info'
            ];

            $logLevel = $logLevelMap[$action] ?? 'info';

            Db::table('collect_logs')->insert([
                'source_id' => $sourceId,
                'task_id' => $taskId,
                'video_id' => $videoId,
                'log_level' => $logLevel,
                'log_type' => 'collect',
                'action' => $action,
                'message' => $message,
                'details' => !empty($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : null,
                'context' => !empty($remoteId) ? json_encode(['remote_video_id' => $remoteId], JSON_UNESCAPED_UNICODE) : null,
                'occurred_at' => date('Y-m-d H:i:s'),
                'status' => 'completed'
            ]);
        } catch (\Exception $e) {
            Log::error('记录采集日志失败', [
                'error' => $e->getMessage(),
                'task_id' => $taskId,
                'source_id' => $sourceId,
                'action' => $action
            ]);
        }
    }

    /**
     * 构建API URL
     */
    private function buildApiUrl(string $baseUrl, array $params): string
    {
        $url = $baseUrl;
        if (strpos($url, '?') === false) {
            $url .= '?';
        } else {
            $url .= '&';
        }

        return $url . http_build_query($params);
    }







    /**
     * 处理播放地址
     */
    private function processPlayUrls(array $videoData): array
    {
        $playUrls = [];

        try {
            // 处理vod_play_url和vod_play_from字段
            if (isset($videoData['vod_play_url']) && !empty($videoData['vod_play_url'])) {
                $playFrom = $videoData['vod_play_from'] ?? '默认线路';
                $playUrl = $videoData['vod_play_url'];

                Log::info('开始处理播放地址', [
                    'video_id' => $videoData['id'] ?? 'unknown',
                    'play_from' => $playFrom,
                    'play_url_length' => strlen($playUrl)
                ]);

                // 处理多个播放源（用$$$分隔）
                if (strpos($playUrl, '$$$') !== false) {
                    $urls = explode('$$$', $playUrl);
                    $froms = explode('$$$', $playFrom);

                    foreach ($urls as $index => $urlGroup) {
                        $fromName = $froms[$index] ?? "线路" . ($index + 1);
                        $episodes = explode('#', $urlGroup);

                        $playUrls[$fromName] = [];
                        foreach ($episodes as $episodeIndex => $episode) {
                            $episode = trim($episode);
                            if (empty($episode)) continue;

                            if (strpos($episode, '$') !== false) {
                                list($episodeName, $episodeUrl) = explode('$', $episode, 2);
                                $episodeName = trim($episodeName);
                                $episodeUrl = trim($episodeUrl);

                                if (!empty($episodeUrl) && $this->isValidPlayUrl($episodeUrl)) {
                                    $playUrls[$fromName][] = [
                                        'name' => $episodeName ?: "第" . ($episodeIndex + 1) . "集",
                                        'url' => $episodeUrl,
                                        'sort' => $episodeIndex
                                    ];
                                }
                            } else {
                                // 没有集数标识的地址，直接使用
                                if ($this->isValidPlayUrl($episode)) {
                                    $playUrls[$fromName][] = [
                                        'name' => "第" . ($episodeIndex + 1) . "集",
                                        'url' => $episode,
                                        'sort' => $episodeIndex
                                    ];
                                }
                            }
                        }
                    }
                } else {
                    // 处理单个播放源
                    $episodes = explode('#', $playUrl);
                    $playUrls[$playFrom] = [];

                    foreach ($episodes as $episodeIndex => $episode) {
                        $episode = trim($episode);
                        if (empty($episode)) continue;

                        if (strpos($episode, '$') !== false) {
                            list($episodeName, $episodeUrl) = explode('$', $episode, 2);
                            $episodeName = trim($episodeName);
                            $episodeUrl = trim($episodeUrl);

                            if (!empty($episodeUrl) && $this->isValidPlayUrl($episodeUrl)) {
                                $playUrls[$playFrom][] = [
                                    'name' => $episodeName ?: "第" . ($episodeIndex + 1) . "集",
                                    'url' => $episodeUrl,
                                    'sort' => $episodeIndex
                                ];
                            }
                        } else {
                            // 没有集数标识的地址，直接使用
                            if ($this->isValidPlayUrl($episode)) {
                                $playUrls[$playFrom][] = [
                                    'name' => "第" . ($episodeIndex + 1) . "集",
                                    'url' => $episode,
                                    'sort' => $episodeIndex
                                ];
                            }
                        }
                    }
                }

                Log::info('播放地址处理完成', [
                    'video_id' => $videoData['id'] ?? 'unknown',
                    'source_count' => count($playUrls),
                    'total_episodes' => array_sum(array_map('count', $playUrls))
                ]);
            } else {
                Log::warning('视频没有播放地址', [
                    'video_id' => $videoData['id'] ?? 'unknown',
                    'video_name' => $videoData['name'] ?? 'unknown'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('处理播放地址失败', [
                'video_id' => $videoData['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }

        return $playUrls;
    }

    /**
     * 保存播放地址
     */
    private function savePlayUrls(int $videoId, array $playUrls): void
    {
        try {
            // 先清除该视频的旧播放地址
            Db::table('video_play_urls')->where('video_id', $videoId)->delete();

            // 创建video_play_urls表的数据 - 适配现有表结构
            foreach ($playUrls as $fromName => $episodes) {
                if (is_array($episodes)) {
                    foreach ($episodes as $index => $episode) {
                        if (is_array($episode) && isset($episode['name']) && isset($episode['url'])) {
                            $insertData = [
                                'video_id' => $videoId,
                                'url' => $episode['url'],
                                'quality' => $this->mapQuality($episode['quality'] ?? ''),
                                'format' => $this->getUrlFormat($episode['url']),
                                'is_primary' => $index === 0 ? 1 : 0, // 第一个设为主要播放地址
                                'status' => 'active',
                                'created_at' => date('Y-m-d H:i:s'),
                                'updated_at' => date('Y-m-d H:i:s')
                            ];

                            Db::table('video_play_urls')->insert($insertData);

                            Log::info('插入播放地址记录', [
                                'video_id' => $videoId,
                                'url' => substr($episode['url'], 0, 100) . '...',
                                'format' => $insertData['format'],
                                'quality' => $insertData['quality']
                            ]);
                        }
                    }
                }
            }

            Log::info('保存播放地址成功', [
                'video_id' => $videoId,
                'source_count' => count($playUrls)
            ]);
        } catch (\Exception $e) {
            Log::error('保存播放地址失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 映射画质
     */
    private function mapQuality(string $quality): string
    {
        $quality = strtolower($quality);
        $qualityMap = [
            '超清' => '1080p',
            '高清' => '720p',
            '标清' => '480p',
            '流畅' => '360p',
            'hd' => '720p',
            'sd' => '480p',
            '1080' => '1080p',
            '720' => '720p',
            '480' => '480p',
            '360' => '360p',
            '240' => '240p'
        ];

        return $qualityMap[$quality] ?? '720p';
    }

    /**
     * 获取URL格式
     */
    private function getUrlFormat(string $url): string
    {
        if (strpos($url, '.m3u8') !== false) return 'm3u8';
        if (strpos($url, '.mp4') !== false) return 'mp4';
        if (strpos($url, '.flv') !== false) return 'flv';
        if (strpos($url, '.avi') !== false) return 'avi';
        return 'unknown';
    }

    /**
     * 处理封面图片
     */
    private function processCoverImage(string $imageUrl): string
    {
        if (empty($imageUrl)) {
            return '';
        }

        // 如果启用了图片同步
        if ($this->getConfig('image_sync_enabled', '1') === '1') {
            try {
                // 下载并保存图片到本地
                $imageData = $this->curlGet($imageUrl);
                if (!empty($imageData)) {
                    $fileName = 'cover_' . time() . '_' . rand(1000, 9999) . '.jpg';
                    $savePath = '/uploads/covers/' . date('Y/m/');
                    $fullPath = __DIR__ . '/../../public' . $savePath;

                    if (!is_dir($fullPath)) {
                        mkdir($fullPath, 0755, true);
                    }

                    $fullFilePath = $fullPath . $fileName;
                    if (file_put_contents($fullFilePath, $imageData)) {
                        return $savePath . $fileName;
                    }
                }
            } catch (\Exception $e) {
                Log::error('下载封面图片失败', ['url' => $imageUrl, 'error' => $e->getMessage()]);
            }
        }

        // 如果下载失败或未启用同步，返回原始URL
        return $imageUrl;
    }

    /**
     * 确定视频类型
     */
    private function determineVideoType(array $videoData): string
    {
        // 根据视频数据判断是长视频还是短视频
        $duration = $this->extractDuration($videoData);

        // 小于5分钟的视为短视频
        if ($duration > 0 && $duration < 300) {
            return 'short';
        }

        return 'long';
    }

    /**
     * 提取视频时长
     */
    private function extractDuration(array $videoData): int
    {
        // 尝试从不同字段提取时长信息
        if (isset($videoData['vod_duration']) && is_numeric($videoData['vod_duration'])) {
            return (int)$videoData['vod_duration'];
        }

        if (isset($videoData['vod_time_add'])) {
            // 如果是数字，直接返回
            if (is_numeric($videoData['vod_time_add'])) {
                return (int)$videoData['vod_time_add'];
            }

            // 尝试解析时间格式，如 "01:23:45"
            if (is_string($videoData['vod_time_add']) && preg_match('/(\d{1,2}):(\d{2}):(\d{2})/', $videoData['vod_time_add'], $matches)) {
                return $matches[1] * 3600 + $matches[2] * 60 + $matches[3];
            }
        }

        return 0;
    }



    /**
     * XML格式采集 - 完全基于maccms10标准实现，优化播放地址处理
     */
    public function collectXmlData(array $source, array $task): array
    {
        Log::info('开始XML采集', [
            'source_id' => $source['id'],
            'source_name' => $source['name'],
            'task_id' => $task['id'],
            'api_url' => $source['api_url']
        ]);

        try {
            $this->currentSource = $source;
            $this->currentTask = $task;

            $param = json_decode($task['config'], true) ?? [];

            // 构建请求URL (maccms10标准参数)
            $urlParam = [
                'ac' => $param['ac'] ?? 'videolist',
                't' => $param['t'] ?? '',
                'pg' => $param['page'] ?? 1,
                'h' => $param['h'] ?? '',
                'ids' => $param['ids'] ?? '',
                'wd' => $param['wd'] ?? '',
                'at' => $param['at'] ?? '',
                'ct' => $param['ct'] ?? '',
                'year' => $param['year'] ?? '',
                'area' => $param['area'] ?? '',
                'lang' => $param['lang'] ?? '',
                'letter' => $param['letter'] ?? '',
                'by' => $param['by'] ?? '',
                'sort' => $param['sort'] ?? '',
                'start' => $param['start'] ?? '',
                'end' => $param['end'] ?? ''
            ];

            // 过滤空参数
            $urlParam = array_filter($urlParam, function($value) {
                return $value !== '' && $value !== null;
            });

            $url = $source['api_url'];
            if (strpos($url, '?') === false) {
                $url .= '?';
            } else {
                $url .= '&';
            }
            $url .= http_build_query($urlParam);

            // 获取XML数据
            $html = $this->curlGet($url);
            if (empty($html)) {
                return ['code' => 0, 'msg' => '获取数据失败，URL: ' . $url];
            }

            // 编码转换
            $charset = $source['charset'] ?? 'utf-8';
            if (strtolower($charset) !== 'utf-8') {
                $html = mb_convert_encoding($html, 'UTF-8', $charset);
            }

            // 解析XML
            $xml = @simplexml_load_string($html);
            if (empty($xml)) {
                // 处理CDATA问题
                $html = $this->fixXmlCdata($html);
                $xml = @simplexml_load_string($html);
                if (empty($xml)) {
                    return ['code' => 0, 'msg' => 'XML解析失败'];
                }
            }

            // 解析分页信息
            $pageInfo = [
                'page' => (int)$xml->list->attributes()->page,
                'pagecount' => (int)$xml->list->attributes()->pagecount,
                'pagesize' => (int)$xml->list->attributes()->pagesize,
                'recordcount' => (int)$xml->list->attributes()->recordcount
            ];

            // 解析视频数据
            $videos = [];
            Log::info('开始解析XML视频数据', [
                'task_id' => $this->currentTask['id'] ?? 0,
                'has_list' => isset($xml->list),
                'has_video' => isset($xml->list->video),
                'video_count' => isset($xml->list->video) ? count($xml->list->video) : 0
            ]);

            if (isset($xml->list->video)) {
                foreach ($xml->list->video as $index => $video) {
                    Log::info('解析视频', ['index' => $index, 'video_id' => (string)$video->id]);
                    $videoData = $this->parseXmlVideo($video, $source);
                    if ($videoData) {
                        $videos[] = $videoData;
                        Log::info('视频解析成功', ['index' => $index, 'title' => $videoData['title'] ?? '']);
                    } else {
                        Log::warning('视频解析失败', ['index' => $index, 'video_id' => (string)$video->id]);
                    }
                }
            } else {
                Log::warning('XML中没有找到视频数据', [
                    'xml_structure' => array_keys((array)$xml),
                    'list_structure' => isset($xml->list) ? array_keys((array)$xml->list) : 'no list'
                ]);
            }

            // 保存采集的视频数据
            $savedCount = 0;
            foreach ($videos as $videoData) {
                if ($this->saveCollectedVideo($videoData, $source)) {
                    $savedCount++;
                }
            }

            return [
                'code' => 1,
                'msg' => '采集成功',
                'data' => [
                    'page' => $pageInfo,
                    'total' => count($videos),
                    'saved' => $savedCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('XML采集失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'XML采集失败: ' . $e->getMessage()];
        }
    }

    /**
     * 解析XML视频数据 (maccms10标准)
     */
    private function parseXmlVideo($video, $source = null): ?array
    {
        try {
            $data = [];

            // 基础信息 - 使用maccms10标准字段名
            $data['vod_id'] = (string)$video->id;
            $data['vod_name'] = (string)$video->n ?? (string)$video->name;
            $data['type_id'] = (string)$video->tid;
            $data['type_name'] = (string)$video->type;
            $data['vod_pic'] = (string)$video->pic;
            $data['vod_lang'] = (string)$video->lang;
            $data['vod_area'] = (string)$video->area;
            $data['vod_year'] = (string)$video->year;
            $data['vod_state'] = (string)$video->state;
            $data['vod_remarks'] = (string)$video->note;
            $data['vod_actor'] = (string)$video->actor;
            $data['vod_director'] = (string)$video->director;
            $data['vod_content'] = (string)$video->des;

            // 播放信息
            $data['last'] = (string)$video->last;
            $data['dt'] = (string)$video->dt;
            $data['total'] = (string)$video->total;
            $data['serial'] = (string)$video->serial;
            $data['tv'] = (string)$video->tv;
            $data['weekday'] = (string)$video->weekday;
            $data['duration'] = (string)$video->duration;

            // 评分信息
            $data['douban_id'] = (string)$video->douban_id;
            $data['douban_score'] = (string)$video->douban_score;
            $data['imdb_id'] = (string)$video->imdb_id;
            $data['imdb_score'] = (string)$video->imdb_score;

            // 播放地址处理 - 完全兼容maccms10标准
            $data['vod_play_from'] = '';
            $data['vod_play_url'] = '';
            $data['vod_play_server'] = '';
            $data['vod_play_note'] = '';

            if (isset($video->dl->dd)) {
                $array_from = [];
                $array_url = [];
                $array_server = [];
                $array_note = [];

                foreach ($video->dl->dd as $dd) {
                    $flag = (string)$dd->attributes()->flag;
                    $urls = (string)$dd;

                    if ($flag && $urls) {
                        $array_from[] = $flag;

                        // 处理播放地址 - 按maccms10标准
                        $urlArray = explode('#', $urls);
                        $sortedUrls = $this->sortPlayUrlsMaccms10($urlArray);
                        $array_url[] = implode('#', $sortedUrls);

                        $array_server[] = 'no';
                        $array_note[] = '';

                        Log::info('播放地址处理成功', [
                            'flag' => $flag,
                            'url_count' => count($sortedUrls),
                            'video_id' => $data['id'],
                            'video_name' => $data['name']
                        ]);
                    }
                }

                // 使用maccms10标准的$$$分隔符
                $data['vod_play_from'] = implode('$$$', $array_from);
                $data['vod_play_url'] = implode('$$$', $array_url);
                $data['vod_play_server'] = implode('$$$', $array_server);
                $data['vod_play_note'] = implode('$$$', $array_note);

                Log::info('maccms10格式播放地址生成完成', [
                    'video_id' => $data['id'],
                    'play_from_count' => count($array_from),
                    'vod_play_from' => $data['vod_play_from'],
                    'vod_play_url_length' => strlen($data['vod_play_url'])
                ]);
            }

            // 如果没有播放地址，尝试获取详情
            if (empty($data['vod_play_from']) && $source && !empty($data['vod_id'])) {
                Log::info('视频没有播放地址，尝试获取详情', [
                    'video_id' => $data['vod_id'],
                    'video_name' => $data['vod_name']
                ]);

                $detailData = $this->fetchVideoDetail($data['vod_id'], $source);
                if ($detailData && !empty($detailData['vod_play_from'])) {
                    $data['vod_play_from'] = $detailData['vod_play_from'];
                    $data['vod_play_url'] = $detailData['vod_play_url'];
                    $data['vod_play_server'] = $detailData['vod_play_server'];
                    $data['vod_play_note'] = $detailData['vod_play_note'];

                    // 同时更新其他详情信息
                    if (!empty($detailData['vod_pic'])) $data['vod_pic'] = $detailData['vod_pic'];
                    if (!empty($detailData['vod_content'])) $data['vod_content'] = $detailData['vod_content'];
                    if (!empty($detailData['vod_actor'])) $data['vod_actor'] = $detailData['vod_actor'];
                    if (!empty($detailData['vod_director'])) $data['vod_director'] = $detailData['vod_director'];

                    Log::info('成功获取视频详情', [
                        'video_id' => $data['vod_id'],
                        'play_from_count' => count(explode('$$$', $data['vod_play_from']))
                    ]);
                } else {
                    Log::warning('获取视频详情失败', [
                        'video_id' => $data['vod_id'],
                        'video_name' => $data['vod_name']
                    ]);
                }
            }

            return $data;

        } catch (\Exception $e) {
            Log::error('解析XML视频数据失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取视频详情
     */
    private function fetchVideoDetail(string $videoId, array $source): ?array
    {
        try {
            // 构建详情请求URL
            $url = $source['api_url'];
            if (strpos($url, '?') === false) {
                $url .= '?';
            } else {
                $url .= '&';
            }
            $url .= http_build_query(['ac' => 'detail', 'ids' => $videoId]);

            // 获取详情数据
            $html = $this->curlGet($url);
            if (empty($html)) {
                return null;
            }

            // 编码转换
            $charset = $source['charset'] ?? 'utf-8';
            if (strtolower($charset) !== 'utf-8') {
                $html = mb_convert_encoding($html, 'UTF-8', $charset);
            }

            // 解析XML
            $xml = @simplexml_load_string($html);
            if (empty($xml)) {
                // 处理CDATA问题
                $html = $this->fixXmlCdata($html);
                $xml = @simplexml_load_string($html);
                if (empty($xml)) {
                    return null;
                }
            }

            // 解析详情视频数据
            if (isset($xml->list->video[0])) {
                return $this->parseXmlVideoDetail($xml->list->video[0]);
            }

            return null;

        } catch (\Exception $e) {
            Log::error('获取视频详情失败: ' . $e->getMessage(), [
                'video_id' => $videoId,
                'source_id' => $source['id']
            ]);
            return null;
        }
    }

    /**
     * 解析XML视频详情数据（只解析播放地址等详情信息）
     */
    private function parseXmlVideoDetail($video): ?array
    {
        try {
            $data = [];

            // 基础信息 - 使用maccms10标准字段名
            $data['vod_pic'] = (string)$video->pic;
            $data['vod_content'] = (string)$video->des;
            $data['vod_actor'] = (string)$video->actor;
            $data['vod_director'] = (string)$video->director;

            // 播放地址处理
            $data['vod_play_from'] = '';
            $data['vod_play_url'] = '';
            $data['vod_play_server'] = '';
            $data['vod_play_note'] = '';

            if (isset($video->dl->dd)) {
                $array_from = [];
                $array_url = [];
                $array_server = [];
                $array_note = [];

                foreach ($video->dl->dd as $dd) {
                    $flag = (string)$dd->attributes()->flag;
                    $urls = (string)$dd;

                    if ($flag && $urls) {
                        $array_from[] = $flag;

                        // 处理播放地址
                        $urlArray = explode('#', $urls);
                        $sortedUrls = $this->sortPlayUrlsMaccms10($urlArray);
                        $array_url[] = implode('#', $sortedUrls);

                        $array_server[] = 'no';
                        $array_note[] = '';
                    }
                }

                // 使用maccms10标准的$$$分隔符
                $data['vod_play_from'] = implode('$$$', $array_from);
                $data['vod_play_url'] = implode('$$$', $array_url);
                $data['vod_play_server'] = implode('$$$', $array_server);
                $data['vod_play_note'] = implode('$$$', $array_note);
            }

            return $data;

        } catch (\Exception $e) {
            Log::error('解析XML视频详情失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 修复XML中的CDATA问题
     */
    private function fixXmlCdata(string $xml): string
    {
        // 处理CDATA标签
        $xml = preg_replace('/<!\[CDATA\[(.*?)\]\]>/s', '$1', $xml);

        // 转义特殊字符
        $xml = str_replace(['&', '<', '>'], ['&amp;', '&lt;', '&gt;'], $xml);

        // 恢复XML标签
        $xml = preg_replace('/&lt;(\/?[a-zA-Z][^&]*?)&gt;/', '<$1>', $xml);

        return $xml;
    }

    /**
     * JSON格式采集 (maccms10标准)
     */
    public function collectJsonData(array $source, array $task): array
    {
        try {
            $param = json_decode($task['config'], true) ?? [];

            // 构建请求URL
            $urlParam = [
                'ac' => $param['ac'] ?? 'list',  // 修改为list，这是标准参数
                't' => $param['t'] ?? '',
                'pg' => $param['page'] ?? 1,
                'h' => $param['h'] ?? '',
                'ids' => $param['ids'] ?? '',
                'wd' => $param['wd'] ?? ''
            ];

            $url = $source['api_url'] . '?' . http_build_query($urlParam);

            Log::info('开始JSON采集', [
                'url' => $url,
                'source_id' => $source['id'],
                'source_name' => $source['name']
            ]);

            // 获取JSON数据
            $response = $this->curlGet($url);
            if (empty($response)) {
                Log::error('获取采集数据失败', ['url' => $url]);
                return ['code' => 0, 'msg' => '获取数据失败'];
            }

            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON解析失败', ['error' => json_last_error_msg()]);
                return ['code' => 0, 'msg' => 'JSON解析失败'];
            }

            // 解析视频数据
            $videos = $data['list'] ?? [];
            $savedCount = 0;

            Log::info('开始处理视频数据', [
                'total_videos' => count($videos),
                'source_id' => $source['id']
            ]);

            foreach ($videos as $videoData) {
                try {
                    // 检查是否有播放地址，如果没有则获取详情
                    if (empty($videoData['vod_play_url']) && !empty($videoData['vod_id'])) {
                        Log::info('视频缺少播放地址，获取详情', [
                            'vod_id' => $videoData['vod_id'],
                            'vod_name' => $videoData['vod_name'] ?? 'unknown'
                        ]);

                        // 获取详情数据
                        $detailData = $this->getVideoDetail($source, $videoData['vod_id']);
                        if ($detailData && !empty($detailData['vod_play_url'])) {
                            // 合并详情数据
                            $videoData = array_merge($videoData, $detailData);
                            Log::info('成功获取视频详情', [
                                'vod_id' => $videoData['vod_id'],
                                'play_url_length' => strlen($videoData['vod_play_url'])
                            ]);
                        } else {
                            Log::warning('获取视频详情失败或无播放地址', [
                                'vod_id' => $videoData['vod_id']
                            ]);
                        }
                    }

                    $processedData = $this->parseJsonVideo($videoData);
                    if ($processedData && $this->saveCollectedVideo($processedData, $source)) {
                        $savedCount++;
                    }
                } catch (\Exception $e) {
                    Log::error('处理视频数据失败', [
                        'vod_id' => $videoData['vod_id'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return [
                'code' => 1,
                'msg' => '采集成功',
                'data' => [
                    'total' => count($videos),
                    'saved' => $savedCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('JSON采集失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'JSON采集失败: ' . $e->getMessage()];
        }
    }





    /**
     * 获取视频详情数据
     */
    private function getVideoDetail(array $source, string $vodId): ?array
    {
        try {
            $detailUrl = $source['api_url'] . '?ac=detail&ids=' . $vodId;

            Log::info('获取视频详情', [
                'url' => $detailUrl,
                'vod_id' => $vodId
            ]);

            $response = $this->curlGet($detailUrl);
            if (empty($response)) {
                Log::warning('详情接口响应为空', ['url' => $detailUrl]);
                return null;
            }

            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('详情数据JSON解析失败', [
                    'error' => json_last_error_msg(),
                    'vod_id' => $vodId
                ]);
                return null;
            }

            if (!isset($data['list']) || empty($data['list'])) {
                Log::warning('详情数据为空', ['vod_id' => $vodId]);
                return null;
            }

            return $data['list'][0];

        } catch (\Exception $e) {
            Log::error('获取视频详情异常', [
                'vod_id' => $vodId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 解析JSON视频数据
     */
    private function parseJsonVideo(array $video): ?array
    {
        try {
            return [
                'id' => $video['vod_id'] ?? 0,
                'name' => $video['vod_name'] ?? '',
                'title' => $video['vod_name'] ?? '',
                'description' => $video['vod_content'] ?? '',
                'category_id' => $video['type_id'] ?? 0,
                'category_name' => $video['type_name'] ?? '',
                'cover_image' => $video['vod_pic'] ?? '',
                'duration' => $video['vod_duration'] ?? '',
                'year' => $video['vod_year'] ?? '',
                'area' => $video['vod_area'] ?? '',
                'lang' => $video['vod_lang'] ?? '',
                'actor' => $video['vod_actor'] ?? '',
                'director' => $video['vod_director'] ?? '',
                // maccms10标准播放地址字段
                'vod_play_url' => $video['vod_play_url'] ?? '',
                'vod_play_from' => $video['vod_play_from'] ?? '',
                'vod_play_server' => $video['vod_play_server'] ?? '',
                'vod_play_note' => $video['vod_play_note'] ?? '',
                // 兼容字段
                'play_url' => $video['vod_play_url'] ?? '',
                'source_id' => $video['vod_id'] ?? 0,
                'source_updated' => $video['vod_time_add'] ?? '',
                'collected_at' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            Log::error('解析JSON视频数据失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 保存采集的视频数据 - 优化版本
     */
    private function saveCollectedVideo(array $videoData, array $source): bool
    {
        try {
            Log::info('开始保存采集视频', [
                'video_id' => $videoData['vod_id'] ?? 'unknown',
                'video_name' => $videoData['vod_name'] ?? 'unknown',
                'has_play_url' => !empty($videoData['vod_play_url'])
            ]);

            // 转换数据格式以匹配数据库结构
            $dbData = $this->convertVideoDataForDb($videoData, $source);

            if (empty($dbData)) {
                Log::warning('视频数据转换失败', [
                    'video_id' => $videoData['vod_id'] ?? 'unknown',
                    'video_data_keys' => array_keys($videoData)
                ]);
                return false;
            }

            Log::info('视频数据转换成功', [
                'video_id' => $videoData['vod_id'] ?? 'unknown',
                'db_data_keys' => array_keys($dbData),
                'title' => $dbData['title'] ?? 'unknown'
            ]);

            // 检查是否已存在（通过采集源ID和远程视频ID）
            $existing = Db::table('collect_bind')
                ->where('source_id', $source['id'])
                ->where('remote_video_id', $videoData['vod_id'])
                ->find();

            if ($existing) {
                // 更新现有记录
                $videoId = $existing['local_video_id'];

                // 检查播放地址是否有变化
                $playUrlHash = md5(serialize($dbData['play_urls']));
                if ($existing['play_url_hash'] !== $playUrlHash) {
                    // 播放地址有变化，更新视频和绑定记录
                    Db::table('videos')->where('id', $videoId)->update([
                        'title' => $dbData['title'],
                        'description' => $dbData['description'],
                        'cover_image' => $dbData['cover_image'],
                        'duration' => $dbData['duration'],
                        'vod_play_from' => $videoData['vod_play_from'] ?? '',
                        'vod_play_url' => $videoData['vod_play_url'] ?? '',
                        'vod_play_server' => $videoData['vod_play_server'] ?? '',
                        'vod_play_note' => $videoData['vod_play_note'] ?? '',
                        'category_id' => $dbData['category_id'],
                        'tags' => $dbData['tags'],
                        'actors' => $dbData['actors'],
                        'director' => $dbData['director'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                    // 更新绑定记录
                    Db::table('collect_bind')->where('id', $existing['id'])->update([
                        'last_update' => date('Y-m-d H:i:s'),
                        'update_count' => $existing['update_count'] + 1,
                        'play_url_hash' => $playUrlHash
                    ]);

                    // 记录采集日志
                    $this->logCollectAction($this->currentTask['id'] ?? 0, $source['id'], $videoId, $videoData['id'], 'update', '更新视频信息和播放地址', [
                        'play_url_count' => count($dbData['play_urls']),
                        'play_url_status' => 'success'
                    ]);

                    Log::info('更新采集视频成功', [
                        'video_id' => $videoId,
                        'remote_id' => $videoData['vod_id'],
                        'title' => $dbData['title'],
                        'play_url_count' => count($dbData['play_urls'])
                    ]);
                }

                return true;
            } else {
                // 插入新记录
                $insertData = [
                    'uuid' => $this->generateUuid(),
                    'title' => $dbData['title'],
                    'description' => $dbData['description'],
                    'cover_image' => $dbData['cover_image'],
                    'duration' => $dbData['duration'],
                    'category_id' => $dbData['category_id'],
                    'tags' => $dbData['tags'],
                    'actors' => $dbData['actors'],
                    'director' => $dbData['director'],
                    'status' => $this->getConfig('auto_publish', '0') == '1' ? 'published' : 'draft',
                    'audit_status' => 'approved',
                    'likes' => 0,
                    'vod_play_from' => $videoData['vod_play_from'] ?? '',
                    'vod_play_url' => $videoData['vod_play_url'] ?? '',
                    'vod_play_server' => $videoData['vod_play_server'] ?? '',
                    'vod_play_note' => $videoData['vod_play_note'] ?? '',
                    'collect_source_id' => $source['id'],
                    'remote_video_id' => $videoData['vod_id'],
                    'source_type' => 'collect',
                    'user_id' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // 插入视频记录
                $videoId = Db::table('videos')->insertGetId($insertData);

                // 保存播放地址到video_play_urls表
                if (!empty($dbData['play_urls'])) {
                    Log::info('准备保存播放地址', [
                        'video_id' => $videoId,
                        'play_urls_structure' => array_keys($dbData['play_urls']),
                        'play_urls_count' => count($dbData['play_urls'])
                    ]);
                    $this->savePlayUrls($videoId, $dbData['play_urls']);
                } else {
                    Log::warning('播放地址数据为空', [
                        'video_id' => $videoId,
                        'db_data_keys' => array_keys($dbData)
                    ]);
                }

                // 创建绑定记录
                $playUrlHash = md5(serialize($dbData['play_urls']));
                Db::table('collect_bind')->insert([
                    'source_id' => $source['id'],
                    'local_video_id' => $videoId,
                    'remote_video_id' => $videoData['vod_id'],
                    'bind_time' => date('Y-m-d H:i:s'),
                    'play_url_hash' => $playUrlHash
                ]);

                // 记录采集日志
                $this->logCollectAction($this->currentTask['id'] ?? 0, $source['id'], $videoId, $videoData['vod_id'], 'create', '新增采集视频', [
                    'play_url_count' => count($dbData['play_urls']),
                    'play_url_status' => count($dbData['play_urls']) > 0 ? 'success' : 'empty'
                ]);

                Log::info('新增采集视频成功', [
                    'video_id' => $videoId,
                    'remote_id' => $videoData['vod_id'],
                    'title' => $dbData['title'],
                    'play_url_count' => count($dbData['play_urls'])
                ]);

                return true;
            }
        } catch (\Exception $e) {
            Log::error('保存采集视频失败', [
                'error' => $e->getMessage(),
                'video_id' => $videoData['vod_id'] ?? 'unknown',
                'video_name' => $videoData['vod_name'] ?? 'unknown'
            ]);

            // 记录错误日志
            $this->logCollectAction($this->currentTask['id'] ?? 0, $source['id'], null, $videoData['id'] ?? '', 'error', '保存视频失败: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 转换视频数据为数据库格式 - 兼容maccms10
     */
    private function convertVideoDataForDb(array $videoData, array $source): array
    {
        try {
            // 处理播放地址 - 使用maccms10标准格式
            $playUrls = [];
            $playFrom = $videoData['vod_play_from'] ?? '';
            $playUrl = $videoData['vod_play_url'] ?? '';

            if (!empty($playFrom) && !empty($playUrl)) {
                $fromArray = explode('$$$', $playFrom);
                $urlArray = explode('$$$', $playUrl);

                for ($i = 0; $i < count($fromArray) && $i < count($urlArray); $i++) {
                    if (!empty($fromArray[$i]) && !empty($urlArray[$i])) {
                        $sourceName = $fromArray[$i];
                        $episodesString = $urlArray[$i];

                        // 解析每个播放源的集数
                        $episodes = explode('#', $episodesString);
                        $playUrls[$sourceName] = [];

                        foreach ($episodes as $index => $episode) {
                            $episode = trim($episode);
                            if (empty($episode)) continue;

                            if (strpos($episode, '$') !== false) {
                                // 格式：集数名称$播放地址
                                list($episodeName, $episodeUrl) = explode('$', $episode, 2);
                                $episodeName = trim($episodeName);
                                $episodeUrl = trim($episodeUrl);
                            } else {
                                // 没有集数名称，使用默认
                                $episodeName = '第' . ($index + 1) . '集';
                                $episodeUrl = $episode;
                            }

                            if (!empty($episodeUrl)) {
                                $playUrls[$sourceName][] = [
                                    'name' => $episodeName,
                                    'url' => $episodeUrl,
                                    'sort' => $index
                                ];
                            }
                        }
                    }
                }
            }

            // 处理分类映射
            $categoryId = $this->mapCategory($videoData['type_name'] ?? '', $source['id']);

            // 处理图片
            $coverImage = $this->processCoverImage($videoData['vod_pic'] ?? '');

            // 处理时长
            $duration = $this->parseDuration($videoData['vod_duration'] ?? '');

            return [
                'title' => trim($videoData['vod_name'] ?? ''),
                'description' => trim($videoData['vod_content'] ?? ''),
                'cover_image' => $coverImage,
                'duration' => $duration,
                'category_id' => $categoryId,
                'tags' => $this->processTags($videoData),
                'actors' => trim($videoData['vod_actor'] ?? ''),
                'director' => trim($videoData['vod_director'] ?? ''),
                // 将播放地址信息存储为JSON格式，用于后续处理
                'play_urls' => $playUrls
            ];
        } catch (\Exception $e) {
            Log::error('转换视频数据失败', [
                'error' => $e->getMessage(),
                'video_data' => $videoData
            ]);
            return [];
        }
    }



    /**
     * 映射分类
     */
    private function mapCategory(string $remoteCategory, int $sourceId): int
    {
        try {
            // 查找分类映射
            $mapping = Db::table('collect_category_mapping')
                ->where('source_id', $sourceId)
                ->where('remote_category_name', $remoteCategory)
                ->find();

            if ($mapping) {
                return $mapping['local_category_id'];
            }

            // 如果没有映射，返回默认分类ID（假设1是默认分类）
            return 1;
        } catch (\Exception $e) {
            Log::error('分类映射失败', ['error' => $e->getMessage()]);
            return 1;
        }
    }



    /**
     * 处理标签
     */
    private function processTags(array $videoData): string
    {
        $tags = [];

        // 从各个字段提取标签
        if (!empty($videoData['area'])) {
            $tags[] = $videoData['area'];
        }
        if (!empty($videoData['lang'])) {
            $tags[] = $videoData['lang'];
        }
        if (!empty($videoData['year'])) {
            $tags[] = $videoData['year'];
        }

        return implode(',', array_unique($tags));
    }

    /**
     * 解析时长
     */
    private function parseDuration(string $duration): int
    {
        if (empty($duration)) {
            return 0;
        }

        // 处理各种时长格式
        if (preg_match('/(\d+):(\d+):(\d+)/', $duration, $matches)) {
            return (int)$matches[1] * 3600 + (int)$matches[2] * 60 + (int)$matches[3];
        } elseif (preg_match('/(\d+):(\d+)/', $duration, $matches)) {
            return (int)$matches[1] * 60 + (int)$matches[2];
        } elseif (preg_match('/(\d+)/', $duration, $matches)) {
            return (int)$matches[1] * 60; // 假设是分钟
        }

        return 0;
    }

    /**
     * 解析日期
     */
    private function parseDate(string $dateStr): ?string
    {
        if (empty($dateStr)) {
            return null;
        }

        try {
            $timestamp = strtotime($dateStr);
            if ($timestamp !== false) {
                return date('Y-m-d H:i:s', $timestamp);
            }
        } catch (\Exception $e) {
            Log::warning('日期解析失败', ['date_str' => $dateStr, 'error' => $e->getMessage()]);
        }

        return null;
    }

    /**
     * 优化的任务执行方法 - 支持批量处理和进度跟踪
     */
    public function executeTaskOptimized(int $taskId, int $batchSize = 50, int $maxExecutionTime = 300): array
    {
        $startTime = time();
        $result = [
            'success' => false,
            'processed' => 0,
            'success_count' => 0,
            'failed_count' => 0,
            'skip_count' => 0,
            'progress' => 0,
            'message' => '',
            'error' => ''
        ];

        try {
            // 获取任务信息
            $task = Db::table('collect_tasks')->where('id', $taskId)->find();
            if (!$task) {
                throw new \Exception('任务不存在');
            }

            // 获取采集源信息
            $source = Db::table('collect_sources')->where('id', $task['source_id'])->find();
            if (!$source) {
                throw new \Exception('采集源不存在');
            }

            $this->currentTask = $task;
            $this->currentSource = $source;

            // 解析任务配置
            $config = json_decode($task['config'], true) ?? [];
            $startPage = $config['page'] ?? 1;
            $maxPages = $config['max_pages'] ?? 10; // 默认最多采集10页

            Log::info('开始执行优化采集任务', [
                'task_id' => $taskId,
                'source_name' => $source['name'],
                'batch_size' => $batchSize,
                'start_page' => $startPage,
                'max_pages' => $maxPages
            ]);

            // 分页采集
            for ($currentPage = $startPage; $currentPage <= $maxPages; $currentPage++) {
                // 检查执行时间限制
                if (time() - $startTime > $maxExecutionTime) {
                    Log::warning('采集任务达到时间限制，停止执行', [
                        'task_id' => $taskId,
                        'current_page' => $currentPage,
                        'execution_time' => time() - $startTime
                    ]);
                    break;
                }

                // 更新当前页码配置
                $pageConfig = array_merge($config, ['page' => $currentPage]);
                $pageTask = array_merge($task, ['config' => json_encode($pageConfig)]);

                // 执行单页采集
                $pageResult = $this->executeSinglePage($source, $pageTask, $batchSize);

                // 累计结果
                $result['processed'] += $pageResult['processed'] ?? 0;
                $result['success_count'] += $pageResult['success_count'] ?? 0;
                $result['failed_count'] += $pageResult['failed_count'] ?? 0;
                $result['skip_count'] += $pageResult['skip_count'] ?? 0;

                // 更新任务进度
                $progress = min(100, ($currentPage / $maxPages) * 100);
                Db::table('collect_tasks')->where('id', $taskId)->update([
                    'current_page' => $currentPage,
                    'progress' => (int)$progress,
                    'processed_count' => $result['processed'],
                    'success_count' => $result['success_count'],
                    'failed_count' => $result['failed_count'],
                    'skip_count' => $result['skip_count'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 如果当前页没有数据，可能已经到达最后一页
                if (($pageResult['processed'] ?? 0) === 0) {
                    Log::info('当前页无数据，可能已到达最后一页', [
                        'task_id' => $taskId,
                        'current_page' => $currentPage
                    ]);
                    break;
                }

                // 页面间隔休眠
                $sleepTime = (int)$this->getConfig('collect_sleep', '1');
                if ($sleepTime > 0) {
                    sleep($sleepTime);
                }
            }

            $result['success'] = true;
            $result['progress'] = 100;
            $result['message'] = "采集完成，共处理 {$result['processed']} 个视频，成功 {$result['success_count']} 个";

            Log::info('采集任务执行完成', [
                'task_id' => $taskId,
                'total_processed' => $result['processed'],
                'success_count' => $result['success_count'],
                'failed_count' => $result['failed_count'],
                'execution_time' => time() - $startTime
            ]);

        } catch (\Exception $e) {
            $result['error'] = $e->getMessage();
            $result['message'] = '采集任务执行失败: ' . $e->getMessage();

            Log::error('采集任务执行失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * 执行单页采集
     */
    private function executeSinglePage(array $source, array $task, int $batchSize): array
    {
        $result = [
            'processed' => 0,
            'success_count' => 0,
            'failed_count' => 0,
            'skip_count' => 0
        ];

        try {
            // 根据API类型选择采集方法
            if ($source['api_type'] === 'xml') {
                $collectResult = $this->collectXmlData($source, $task);
            } else {
                $collectResult = $this->collectJsonData($source, $task);
            }

            if ($collectResult['code'] === 1) {
                $result['processed'] = $collectResult['data']['total'] ?? 0;
                $result['success_count'] = $collectResult['data']['saved'] ?? 0;
                $result['failed_count'] = $result['processed'] - $result['success_count'];
            } else {
                $result['failed_count'] = 1;
                Log::error('单页采集失败', [
                    'source_id' => $source['id'],
                    'error' => $collectResult['msg'] ?? 'Unknown error'
                ]);
            }

        } catch (\Exception $e) {
            $result['failed_count'] = 1;
            Log::error('单页采集异常', [
                'source_id' => $source['id'],
                'error' => $e->getMessage()
            ]);
        }

        return $result;
    }

    /**
     * 生成UUID
     */
    private function generateUuid(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * 执行更新任务 - 更新现有视频的播放地址
     */
    private function executeUpdateTask(int $taskId, array $task): array
    {
        try {
            $source = Db::table('collect_sources')->where('id', $task['source_id'])->find();
            if (!$source) {
                return ['code' => 0, 'msg' => '采集源不存在'];
            }

            $config = json_decode($task['config'], true);
            $totalUpdated = 0;
            $successCount = 0;
            $failedCount = 0;

            Log::info('开始执行更新任务', [
                'task_id' => $taskId,
                'source_id' => $task['source_id']
            ]);

            // 查找需要更新播放地址的视频
            $videos = Db::table('videos')
                ->where('collect_source_id', $task['source_id'])
                ->where('remote_video_id', '<>', '')
                ->where('remote_video_id', 'not null')
                ->where(function($query) {
                    $query->whereNull('vod_play_url')
                          ->whereOr('vod_play_url', '=', '')
                          ->whereOr('vod_play_url', '=', 'null');
                })
                ->limit(50) // 限制每次处理的数量
                ->select();

            Log::info('找到需要更新的视频', [
                'task_id' => $taskId,
                'video_count' => count($videos)
            ]);

            foreach ($videos as $video) {
                try {
                    $totalUpdated++;

                    // 通过API获取视频详情
                    $apiUrl = $this->buildApiUrl($source['api_url'], [
                        'ac' => 'videolist',
                        'ids' => $video['remote_video_id']
                    ]);

                    $html = $this->curlGet($apiUrl);
                    if (empty($html)) {
                        Log::warning('获取视频详情失败', [
                            'video_id' => $video['id'],
                            'remote_id' => $video['remote_video_id']
                        ]);
                        $failedCount++;
                        continue;
                    }

                    $data = json_decode($html, true);
                    if (!$data || !isset($data['list']) || empty($data['list'])) {
                        Log::warning('视频详情数据为空', [
                            'video_id' => $video['id'],
                            'remote_id' => $video['remote_video_id']
                        ]);
                        $failedCount++;
                        continue;
                    }

                    $videoData = $data['list'][0];

                    // 处理播放地址
                    $playUrls = $this->processPlayUrls($videoData);

                    if (empty($playUrls)) {
                        Log::warning('播放地址为空', [
                            'video_id' => $video['id'],
                            'remote_id' => $video['remote_video_id']
                        ]);
                        $failedCount++;
                        continue;
                    }

                    // 更新视频播放地址
                    Db::table('videos')->where('id', $video['id'])->update([
                        'vod_play_url' => $videoData['vod_play_url'] ?? '',
                        'vod_play_from' => $videoData['vod_play_from'] ?? '',
                        'vod_play_server' => $videoData['vod_play_server'] ?? '',
                        'vod_play_note' => $videoData['vod_play_note'] ?? '',
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                    Log::info('更新视频播放地址成功', [
                        'video_id' => $video['id'],
                        'remote_id' => $video['remote_video_id'],
                        'title' => $video['title'],
                        'play_url_count' => count($playUrls)
                    ]);

                    $successCount++;

                    // 添加延迟避免请求过快
                    usleep(500000); // 0.5秒

                } catch (\Exception $e) {
                    Log::error('更新视频播放地址失败', [
                        'video_id' => $video['id'],
                        'error' => $e->getMessage()
                    ]);
                    $failedCount++;
                }
            }

            // 更新任务完成状态
            Db::table('collect_tasks')->where('id', $taskId)->update([
                'status' => 'completed',
                'progress' => 100,
                'processed_count' => $totalUpdated,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'end_time' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            Log::info('更新任务完成', [
                'task_id' => $taskId,
                'total' => $totalUpdated,
                'success' => $successCount,
                'failed' => $failedCount
            ]);

            return [
                'code' => 1,
                'msg' => '更新完成',
                'data' => [
                    'total' => $totalUpdated,
                    'success' => $successCount,
                    'failed' => $failedCount
                ]
            ];

        } catch (\Exception $e) {
            // 更新任务失败状态
            Db::table('collect_tasks')->where('id', $taskId)->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            Log::error('更新任务执行失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage()
            ]);

            return ['code' => 0, 'msg' => '更新失败：' . $e->getMessage()];
        }
    }

}
