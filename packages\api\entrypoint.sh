#!/bin/bash

echo "🚀 启动API服务 (开发模式)"

# 等待数据库连接
echo "⏳ 等待数据库连接..."
while ! nc -z mysql 3306; do
  sleep 1
done

echo "✅ 数据库连接成功"

# 启用Apache模块
echo "🔧 启用Apache模块..."
a2enmod headers expires rewrite
echo "✅ Apache模块启用完成"

# 设置权限
chown -R www-data:www-data /var/www/html
chmod -R 755 /var/www/html

# 创建必要目录
mkdir -p /var/www/html/runtime/log
mkdir -p /var/www/html/runtime/cache
mkdir -p /var/www/html/public/uploads
chmod -R 777 /var/www/html/runtime
chmod -R 777 /var/www/html/public/uploads

echo "✅ API服务启动完成 (开发模式)"
echo "🌐 访问地址: http://localhost:${API_PORT:-3000}"

# 启动队列工作进程（后台运行）
echo "🔄 启动队列工作进程..."
if [ -f "/var/www/html/queue_manager.sh" ]; then
    chmod +x /var/www/html/queue_manager.sh
    # 等待一下确保数据库完全就绪
    sleep 3
    if /var/www/html/queue_manager.sh start; then
        echo "✅ 队列工作进程启动成功"
    else
        echo "⚠️  队列工作进程启动失败，但继续启动Apache"
    fi
else
    echo "⚠️  队列管理脚本不存在，跳过队列启动"
fi

# 启动Apache
exec apache2-foreground
