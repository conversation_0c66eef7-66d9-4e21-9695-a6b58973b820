<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="260px" class="sidebar">
        <div class="logo">
          <div class="logo-icon">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div class="logo-text">
            <h3 class="text-gradient">视频平台</h3>
            <span class="logo-subtitle">管理系统</span>
          </div>
        </div>
        <el-menu
          :default-active="$route.path"
          :default-openeds="defaultOpeneds"
          :unique-opened="false"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard" class="menu-item">
            <div class="menu-item-content">
              <el-icon class="menu-icon"><House /></el-icon>
              <span class="menu-text">仪表盘</span>
            </div>
          </el-menu-item>
          <!-- 用户管理子菜单 -->
          <el-sub-menu index="users" class="menu-item">
            <template #title>
              <div class="menu-item-content">
                <el-icon class="menu-icon"><User /></el-icon>
                <span class="menu-text">用户管理</span>
              </div>
            </template>
            <el-menu-item index="/users" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><List /></el-icon>
                <span class="menu-text">用户列表</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/users/vip" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Star /></el-icon>
                <span class="menu-text">VIP管理</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/users/behavior" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><PieChart /></el-icon>
                <span class="menu-text">行为分析</span>
              </div>
            </el-menu-item>
          </el-sub-menu>
          <!-- 视频管理子菜单 -->
          <el-sub-menu index="videos" class="menu-item">
            <template #title>
              <div class="menu-item-content">
                <el-icon class="menu-icon"><VideoPlay /></el-icon>
                <span class="menu-text">视频管理</span>
              </div>
            </template>
            <el-menu-item index="/videos" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><List /></el-icon>
                <span class="menu-text">视频列表</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/videos/review" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Check /></el-icon>
                <span class="menu-text">视频审核</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/videos/analytics" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><TrendCharts /></el-icon>
                <span class="menu-text">视频分析</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/ad-detection" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Warning /></el-icon>
                <span class="menu-text">广告检测</span>
              </div>
            </el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/categories" class="menu-item">
            <div class="menu-item-content">
              <el-icon class="menu-icon"><Menu /></el-icon>
              <span class="menu-text">分类管理</span>
            </div>
          </el-menu-item>
          <el-menu-item index="/comments" class="menu-item">
            <div class="menu-item-content">
              <el-icon class="menu-icon"><ChatDotRound /></el-icon>
              <span class="menu-text">评论管理</span>
            </div>
          </el-menu-item>
          <!-- 安全管理子菜单 -->
          <el-sub-menu index="security" class="menu-item">
            <template #title>
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Lock /></el-icon>
                <span class="menu-text">安全管理</span>
              </div>
            </template>
            <el-menu-item index="/security/auth" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Key /></el-icon>
                <span class="menu-text">认证管理</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/security/permissions" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><UserFilled /></el-icon>
                <span class="menu-text">权限管理</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/security/logs" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Warning /></el-icon>
                <span class="menu-text">安全日志</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/security/sessions" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Monitor /></el-icon>
                <span class="menu-text">会话管理</span>
              </div>
            </el-menu-item>
          </el-sub-menu>
          <!-- 采集管理子菜单 -->
          <el-sub-menu index="collect" class="menu-item">
            <template #title>
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Download /></el-icon>
                <span class="menu-text">采集管理</span>
              </div>
            </template>
            <el-menu-item index="/collect/sources" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Link /></el-icon>
                <span class="menu-text">采集源</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/collect/mapping" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Connection /></el-icon>
                <span class="menu-text">分类映射</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/collect/tasks" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><List /></el-icon>
                <span class="menu-text">采集任务</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/collect/logs" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Document /></el-icon>
                <span class="menu-text">采集日志</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/collect/config" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Setting /></el-icon>
                <span class="menu-text">采集配置</span>
              </div>
            </el-menu-item>
            <el-menu-item index="/collect/monitor" class="sub-menu-item">
              <div class="menu-item-content">
                <el-icon class="menu-icon"><Monitor /></el-icon>
                <span class="menu-text">采集监控</span>
              </div>
            </el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/settings" class="menu-item">
            <div class="menu-item-content">
              <el-icon class="menu-icon"><Setting /></el-icon>
              <span class="menu-text">系统设置</span>
            </div>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-content">
            <div class="header-left">
              <div class="page-title-section">
                <h2 class="page-title">{{ getPageTitle() }}</h2>
                <p class="page-subtitle">{{ getPageSubtitle() }}</p>
              </div>
            </div>
            <div class="header-right">
              <div class="header-actions">
                <!-- 通知按钮 -->
                <el-badge :value="3" class="notification-badge">
                  <el-button circle class="action-btn">
                    <el-icon><Bell /></el-icon>
                  </el-button>
                </el-badge>
                
                <!-- 搜索按钮 -->
                <el-button circle class="action-btn">
                  <el-icon><Search /></el-icon>
                </el-button>
                
                <!-- 用户下拉菜单 -->
                <el-dropdown @command="handleCommand" class="user-dropdown">
                  <div class="user-info">
                    <el-avatar :size="36" :src="authStore.admin?.avatar" class="user-avatar">
                      {{ authStore.admin?.real_name?.charAt(0) || 'A' }}
                    </el-avatar>
                    <div class="user-details">
                      <span class="username">{{ authStore.admin?.real_name || authStore.admin?.username }}</span>
                      <span class="user-role">{{ getRoleText(authStore.admin?.role) }}</span>
                    </div>
                    <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu class="user-dropdown-menu">
                      <el-dropdown-item command="profile">
                        <el-icon><User /></el-icon>
                        个人资料
                      </el-dropdown-item>
                      <el-dropdown-item command="settings">
                        <el-icon><Setting /></el-icon>
                        系统设置
                      </el-dropdown-item>
                      <el-dropdown-item divided command="logout">
                        <el-icon><SwitchButton /></el-icon>
                        退出登录
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </el-header>

        <!-- 主要内容区域 -->
        <el-main class="main-content">
          <slot />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  House, User, VideoPlay, Menu, ArrowDown, Setting,
  Bell, Search, SwitchButton, ChatDotRound, Download,
  Link, Connection, List, Document, Lock, Key, UserFilled,
  Warning, Monitor, Check, TrendCharts, Star, PieChart
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 计算默认展开的子菜单
const defaultOpeneds = computed(() => {
  const openeds = []
  // 如果当前路由是用户管理相关的，展开用户管理子菜单
  if (route.path.startsWith('/users')) {
    openeds.push('users')
  }
  // 如果当前路由是视频管理相关的，展开视频管理子菜单
  if (route.path.startsWith('/videos') || route.path.startsWith('/ad-detection')) {
    openeds.push('videos')
  }
  // 如果当前路由是安全管理相关的，展开安全管理子菜单
  if (route.path.startsWith('/security')) {
    openeds.push('security')
  }
  // 如果当前路由是采集管理相关的，展开采集管理子菜单
  if (route.path.startsWith('/collect')) {
    openeds.push('collect')
  }
  return openeds
})

const getPageTitle = () => {
  const titles: Record<string, string> = {
    '/dashboard': '仪表盘',
    '/users': '用户管理',
    '/users/vip': 'VIP用户管理',
    '/users/behavior': '用户行为分析',
    '/videos': '视频管理',
    '/videos/review': '视频审核',
    '/videos/analytics': '视频分析',
    '/ad-detection': '广告检测管理',
    '/categories': '分类管理',
    '/comments': '评论管理',
    '/security/auth': '认证管理',
    '/security/permissions': '权限管理',
    '/security/logs': '安全日志',
    '/security/sessions': '会话管理',
    '/collect/sources': '采集源管理',
    '/collect/mapping': '分类映射',
    '/collect/tasks': '采集任务',
    '/collect/logs': '采集日志',
    '/collect/config': '采集配置',
    '/collect/monitor': '采集监控',
    '/settings': '系统设置'
  }
  return titles[route.path] || '仪表盘'
}

const getPageSubtitle = () => {
  const subtitles: Record<string, string> = {
    '/dashboard': '欢迎回来，查看您的平台数据概览',
    '/users': '管理平台上的所有用户',
    '/videos': '管理平台上的所有视频内容',
    '/ad-detection': '智能检测和管理视频中的广告内容',
    '/categories': '管理视频分类和标签',
    '/settings': '配置系统参数和选项'
  }
  return subtitles[route.path] || '欢迎回来，查看您的平台数据概览'
}

const getRoleText = (role?: string) => {
  const roles: Record<string, string> = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'moderator': '审核员'
  }
  return roles[role || 'admin'] || '管理员'
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        authStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  background: var(--bg-secondary);
}

/* 侧边栏样式 */
.sidebar {
  background: linear-gradient(180deg, var(--bg-dark) 0%, var(--gray-800) 100%);
  box-shadow: var(--shadow-lg);
  border-right: 1px solid var(--border-light);
}

.logo {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 var(--spacing-lg);
  background: var(--bg-darker);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: var(--spacing-md);
}

.logo-icon {
  width: 42px;
  height: 42px;
  background: var(--primary-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: 20px;
  box-shadow: var(--shadow-md);
}

.logo-text h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-inverse);
}

.logo-subtitle {
  font-size: var(--text-xs);
  color: var(--gray-400);
  font-weight: var(--font-medium);
}

.sidebar-menu {
  background: transparent !important;
  border: none !important;
  padding: var(--spacing-md) 0;
}

.menu-item {
  margin: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-lg) !important;
  transition: all var(--transition-normal) !important;
  background: transparent !important;
  border: none !important;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  transform: translateX(4px);
}

.menu-item.is-active {
  background: var(--primary-gradient) !important;
  box-shadow: var(--shadow-md);
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

.menu-icon {
  font-size: 18px;
  color: var(--gray-300);
  transition: color var(--transition-fast);
}

.menu-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-300);
  transition: color var(--transition-fast);
}

.menu-item:hover .menu-icon,
.menu-item:hover .menu-text,
.menu-item.is-active .menu-icon,
.menu-item.is-active .menu-text {
  color: var(--text-inverse);
}

/* 子菜单样式 */
:deep(.el-sub-menu) {
  margin: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-lg);
}

:deep(.el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  background: transparent;
  color: var(--gray-300) !important;
}

:deep(.el-sub-menu__title:hover) {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
  color: var(--text-inverse) !important;
}

:deep(.el-sub-menu.is-opened .el-sub-menu__title) {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-inverse) !important;
}

:deep(.el-sub-menu__icon-arrow) {
  color: var(--gray-300) !important;
  transition: color var(--transition-fast);
}

:deep(.el-sub-menu:hover .el-sub-menu__icon-arrow),
:deep(.el-sub-menu.is-opened .el-sub-menu__icon-arrow) {
  color: var(--text-inverse) !important;
}

.sub-menu-item {
  margin: var(--spacing-xs) var(--spacing-lg) !important;
  padding-left: var(--spacing-xl) !important;
  border-radius: var(--radius-md) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  height: 40px !important;
  line-height: 40px !important;
}

.sub-menu-item:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  transform: translateX(2px);
}

.sub-menu-item.is-active {
  background: var(--primary-gradient) !important;
  border-left: 3px solid var(--primary-color);
  color: var(--text-inverse) !important;
}

.sub-menu-item .menu-icon,
.sub-menu-item .menu-text {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.sub-menu-item:hover .menu-icon,
.sub-menu-item:hover .menu-text,
.sub-menu-item.is-active .menu-icon,
.sub-menu-item.is-active .menu-text {
  color: var(--text-inverse) !important;
}

/* 头部样式 */
.header {
  background: var(--bg-primary) !important;
  border-bottom: 1px solid var(--border-light) !important;
  padding: 0 var(--spacing-xl) !important;
  box-shadow: var(--shadow-sm);
  height: 70px !important;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.page-title-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.page-title {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.page-subtitle {
  margin: 0;
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full) !important;
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-light) !important;
  color: var(--text-secondary) !important;
  transition: all var(--transition-normal) !important;
}

.action-btn:hover {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.user-dropdown {
  margin-left: var(--spacing-sm);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
}

.user-info:hover {
  background: var(--bg-tertiary);
  box-shadow: var(--shadow-md);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.username {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.user-role {
  font-size: 10px;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.dropdown-arrow {
  color: var(--text-secondary);
  font-size: 12px;
  transition: transform var(--transition-fast);
}

.user-info:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 主要内容区域 */
.main-content {
  padding: 0 !important;
  background: var(--bg-secondary);
  min-height: calc(100vh - 70px);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-menu) {
  border-right: none;
  background: transparent;
}

/* 确保子菜单正确显示 */
:deep(.el-menu--vertical .el-sub-menu .el-menu) {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: var(--radius-md);
  margin: var(--spacing-xs) 0;
  padding: var(--spacing-xs) 0;
}

:deep(.el-sub-menu .el-menu-item) {
  background: transparent !important;
  color: var(--gray-300) !important;
  margin: var(--spacing-xs) var(--spacing-md) !important;
  border-radius: var(--radius-md) !important;
  height: 40px !important;
  line-height: 40px !important;
  padding-left: var(--spacing-xl) !important;
  min-height: 40px !important;
}

:deep(.el-sub-menu .el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.15) !important;
  color: var(--text-inverse) !important;
  transform: translateX(2px);
}

:deep(.el-sub-menu .el-menu-item.is-active) {
  background: var(--primary-gradient) !important;
  color: var(--text-inverse) !important;
  border-left: 3px solid var(--primary-color);
}

/* 强制显示子菜单 */
:deep(.el-sub-menu.is-opened .el-menu) {
  display: block !important;
}

:deep(.el-sub-menu .el-menu) {
  background: rgba(0, 0, 0, 0.2) !important;
}

:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  margin: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

:deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

:deep(.el-menu-item.is-active) {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

:deep(.el-dropdown-menu) {
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-sm);
}

:deep(.el-dropdown-menu__item) {
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-xs) 0;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-sm);
}

:deep(.el-dropdown-menu__item:hover) {
  background: var(--bg-tertiary);
  color: var(--primary-color);
}

:deep(.el-badge__content) {
  background: var(--error-color);
  border: 2px solid var(--bg-primary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sidebar {
    width: 220px !important;
  }
  
  .logo {
    padding: 0 var(--spacing-md);
  }
  
  .main-content {
    padding: var(--spacing-md) !important;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 180px !important;
  }
  
  .logo-text h3 {
    font-size: var(--text-base);
  }
  
  .page-title {
    font-size: var(--text-lg);
  }
  
  .header-actions {
    gap: var(--spacing-sm);
  }
  
  .user-details {
    display: none;
  }
  
  .main-content {
    padding: var(--spacing-sm) !important;
  }
}
</style>
