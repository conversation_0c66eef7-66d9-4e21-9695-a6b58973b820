<template>
  <div class="collect-monitor">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ statistics.tasks?.total_tasks || 0 }}</div>
            <div class="stat-label">总任务数</div>
          </div>
          <el-icon class="stat-icon"><Document /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ statistics.videos?.collected_videos || 0 }}</div>
            <div class="stat-label">采集视频数</div>
          </div>
          <el-icon class="stat-icon"><VideoPlay /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ statistics.sources?.active_sources || 0 }}</div>
            <div class="stat-label">活跃采集源</div>
          </div>
          <el-icon class="stat-icon"><Connection /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
          <el-icon class="stat-icon"><SuccessFilled /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 采集源健康状态 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>采集源健康状态</span>
          <div class="header-actions">
            <el-button type="success" size="small" @click="autoHealthCheck" :loading="autoCheckLoading">
              <el-icon><Monitor /></el-icon>
              自动健康检查
            </el-button>
            <el-button type="warning" size="small" @click="retryFailedTasks" :loading="retryLoading">
              <el-icon><RefreshRight /></el-icon>
              重试失败任务
            </el-button>
            <el-button type="primary" size="small" @click="refreshSourceHealth">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="sourceHealth" v-loading="healthLoading">
        <el-table-column prop="source_name" label="采集源名称" />
        <el-table-column prop="success_rate" label="成功率" width="100">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.success_rate" 
              :color="getProgressColor(row.success_rate)"
              :show-text="false"
              style="width: 60px"
            />
            <span class="ml-2">{{ row.success_rate }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="recent_errors" label="24h错误数" width="100" />
        <el-table-column prop="health_score" label="健康分数" width="100">
          <template #default="{ row }">
            <el-tag :type="getHealthTagType(row.health_score)">
              {{ row.health_score }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_collect_time" label="最后采集时间" width="180" />
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="checkConnectivity(row.source_id)"
              :loading="connectivityChecking[row.source_id]"
            >
              连通性检查
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 实时监控 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>任务执行趋势</span>
          </template>
          <div ref="taskTrendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>采集源性能分布</span>
          </template>
          <div ref="sourcePerformanceChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 连通性检查结果对话框 -->
    <el-dialog 
      v-model="connectivityDialogVisible" 
      title="连通性检查结果" 
      width="600px"
    >
      <div v-if="connectivityResult">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="采集源">
            {{ connectivityResult.source_name }}
          </el-descriptions-item>
          <el-descriptions-item label="API地址">
            {{ connectivityResult.api_url }}
          </el-descriptions-item>
          <el-descriptions-item label="整体状态">
            <el-tag :type="connectivityResult.overall_status === 'healthy' ? 'success' : 'danger'">
              {{ connectivityResult.overall_status === 'healthy' ? '健康' : '异常' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="响应时间">
            {{ connectivityResult.response_time }}ms
          </el-descriptions-item>
        </el-descriptions>

        <el-divider>详细测试结果</el-divider>

        <el-table :data="connectivityTestResults" size="small">
          <el-table-column prop="test_name" label="测试项目" />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-icon :color="row.status ? '#67C23A' : '#F56C6C'">
                <component :is="row.status ? 'SuccessFilled' : 'CircleCloseFilled'" />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column prop="details" label="详情" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  Document, 
  VideoPlay, 
  Connection, 
  SuccessFilled, 
  Refresh,
  CircleCloseFilled,
  Monitor,
  RefreshRight
} from '@element-plus/icons-vue'
import { collectApi } from '@/api/collect'

// 响应式数据
const statistics = ref({})
const sourceHealth = ref([])
const healthLoading = ref(false)
const autoCheckLoading = ref(false)
const retryLoading = ref(false)
const connectivityChecking = reactive({})
const connectivityDialogVisible = ref(false)
const connectivityResult = ref(null)
const trendsData = ref([])
const performanceData = ref({ distribution: {}, source_details: [] })
const autoRefreshTimer = ref(null)

// 图表实例
let taskTrendChart = null
let sourcePerformanceChart = null

// 计算属性
const successRate = computed(() => {
  const tasks = statistics.value.tasks
  if (!tasks || !tasks.total_collected) return 0
  return Math.round((tasks.total_success / tasks.total_collected) * 100)
})

const connectivityTestResults = computed(() => {
  if (!connectivityResult.value?.tests) return []
  
  const tests = connectivityResult.value.tests
  return [
    {
      test_name: '基本连接',
      status: tests.basic_connection?.status || false,
      details: tests.basic_connection?.error || `HTTP ${tests.basic_connection?.http_code || 'N/A'}`
    },
    {
      test_name: 'API响应',
      status: tests.api_response?.status || false,
      details: tests.api_response?.error || `响应长度: ${tests.api_response?.response_length || 0}`
    },
    {
      test_name: '数据格式',
      status: tests.data_format?.status || false,
      details: tests.data_format?.error || `格式: ${tests.data_format?.format || 'N/A'}`
    }
  ]
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await collectApi.getStatistics('today')
    statistics.value = response.data
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  }
}

const loadSourceHealth = async () => {
  healthLoading.value = true
  try {
    const response = await collectApi.getSourceHealth()
    sourceHealth.value = response.data
  } catch (error) {
    ElMessage.error('加载健康状态失败')
  } finally {
    healthLoading.value = false
  }
}

const refreshSourceHealth = () => {
  loadSourceHealth()
}

// 自动健康检查
const autoHealthCheck = async () => {
  autoCheckLoading.value = true
  try {
    const response = await collectApi.autoHealthCheck()
    const result = response.data
    
    ElMessage.success(`健康检查完成！检查了${result.checked_sources}个采集源，健康${result.healthy_sources}个，警告${result.warning_sources}个，异常${result.critical_sources}个，自动恢复${result.auto_recovered}个`)
    
    // 刷新健康状态数据
    await loadSourceHealth()
    await loadTrendsData()
    await loadPerformanceData()
    
  } catch (error) {
    ElMessage.error('自动健康检查失败: ' + error.message)
  } finally {
    autoCheckLoading.value = false
  }
}

// 重试失败任务
const retryFailedTasks = async () => {
  retryLoading.value = true
  try {
    const response = await collectApi.retryFailedTasks()
    const result = response.data
    
    ElMessage.success(`重试完成！处理了${result.retried_tasks}个失败任务，成功恢复${result.recovered_tasks}个`)
    
    // 刷新统计数据
    await loadStatistics()
    
  } catch (error) {
    ElMessage.error('重试失败任务失败: ' + error.message)
  } finally {
    retryLoading.value = false
  }
}

// 加载趋势数据
const loadTrendsData = async () => {
  try {
    const response = await collectApi.getCollectTrends(7)
    trendsData.value = response.data
    // 数据加载完成后更新图表
    nextTick(() => {
      updateTaskTrendChart()
    })
  } catch (error) {
    console.error('加载趋势数据失败:', error)
  }
}

// 加载性能数据
const loadPerformanceData = async () => {
  try {
    const response = await collectApi.getSourcePerformance()
    performanceData.value = response.data
    // 数据加载完成后更新图表
    nextTick(() => {
      updatePerformanceChart()
    })
  } catch (error) {
    console.error('加载性能数据失败:', error)
  }
}

const checkConnectivity = async (sourceId) => {
  connectivityChecking[sourceId] = true
  try {
    const response = await collectApi.checkSourceConnectivity(sourceId)
    connectivityResult.value = response.data
    connectivityDialogVisible.value = true
  } catch (error) {
    ElMessage.error('连通性检查失败')
  } finally {
    connectivityChecking[sourceId] = false
  }
}

// 工具方法
const getProgressColor = (percentage) => {
  if (percentage >= 90) return '#67C23A'
  if (percentage >= 70) return '#E6A23C'
  return '#F56C6C'
}

const getHealthTagType = (score) => {
  if (score >= 80) return 'success'
  if (score >= 60) return 'warning'
  return 'danger'
}

const getStatusTagType = (status) => {
  switch (status) {
    case 'healthy': return 'success'
    case 'warning': return 'warning'
    case 'critical': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'healthy': return '健康'
    case 'warning': return '警告'
    case 'critical': return '异常'
    default: return '未知'
  }
}

// 初始化图表
const initCharts = () => {
  // 任务执行趋势图表
  taskTrendChart = echarts.init(document.querySelector('[ref="taskTrendChart"]'))
  
  // 采集源性能分布图表
  sourcePerformanceChart = echarts.init(document.querySelector('[ref="sourcePerformanceChart"]'))
  
  // 窗口大小改变时重新调整图表
  window.addEventListener('resize', () => {
    taskTrendChart?.resize()
    sourcePerformanceChart?.resize()
  })
}

// 更新任务趋势图表
const updateTaskTrendChart = () => {
  if (!taskTrendChart || !trendsData.value.length) return
  
  const dates = trendsData.value.map(item => item.date_label)
  const successData = trendsData.value.map(item => item.total_success)
  const failedData = trendsData.value.map(item => item.total_failed)
  const tasksData = trendsData.value.map(item => item.total_tasks)
  
  const option = {
    title: { 
      text: '最近7天采集趋势',
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: { 
      trigger: 'axis',
      formatter: function(params) {
        let result = params[0].axisValue + '<br/>'
        params.forEach(param => {
          result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: { 
      data: ['总任务', '成功数', '失败数'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: { 
      type: 'category',
      data: dates,
      axisLabel: { rotate: 45 }
    },
    yAxis: { 
      type: 'value',
      name: '数量'
    },
    series: [
      {
        name: '总任务',
        type: 'bar',
        data: tasksData,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '成功数',
        type: 'line',
        data: successData,
        itemStyle: { color: '#67C23A' },
        smooth: true
      },
      {
        name: '失败数',
        type: 'line',
        data: failedData,
        itemStyle: { color: '#F56C6C' },
        smooth: true
      }
    ]
  }
  taskTrendChart.setOption(option)
}

// 更新性能分布图表
const updatePerformanceChart = () => {
  if (!sourcePerformanceChart || !performanceData.value.distribution) return
  
  const distribution = performanceData.value.distribution
  const pieData = [
    { value: distribution.healthy || 0, name: '健康', itemStyle: { color: '#67C23A' } },
    { value: distribution.warning || 0, name: '警告', itemStyle: { color: '#E6A23C' } },
    { value: distribution.critical || 0, name: '异常', itemStyle: { color: '#F56C6C' } },
    { value: distribution.unknown || 0, name: '未知', itemStyle: { color: '#909399' } }
  ].filter(item => item.value > 0)
  
  const option = {
    title: { 
      text: '采集源健康状态分布',
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: { 
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: '采集源状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: '{b}: {c}'
        }
      }
    ]
  }
  sourcePerformanceChart.setOption(option)
}

// 开始自动刷新
const startAutoRefresh = () => {
  // 清除之前的定时器
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
  }
  
  // 设置新的定时器，每30秒刷新一次
  autoRefreshTimer.value = setInterval(async () => {
    await loadStatistics()
    await loadSourceHealth()
    await loadTrendsData()
    await loadPerformanceData()
  }, 30000)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 组件挂载后初始化
onMounted(async () => {
  // 加载基础数据
  await loadStatistics()
  await loadSourceHealth()
  
  // 加载图表数据
  await loadTrendsData()
  await loadPerformanceData()
  
  // 初始化图表
  nextTick(() => {
    initCharts()
    updateTaskTrendChart()
    updatePerformanceChart()
  })
  
  // 开始自动刷新
  startAutoRefresh()
})

// 组件卸载时清理资源
onUnmounted(() => {
  stopAutoRefresh()
  
  // 销毁图表实例
  if (taskTrendChart) {
    taskTrendChart.dispose()
    taskTrendChart = null
  }
  
  if (sourcePerformanceChart) {
    sourcePerformanceChart.dispose()
    sourcePerformanceChart = null
  }
  
  // 移除窗口事件监听
  window.removeEventListener('resize', () => {
    taskTrendChart?.resize()
    sourcePerformanceChart?.resize()
  })
})
</script>

<style scoped>
.collect-monitor {
  padding: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 48px;
  color: #409EFF;
  opacity: 0.1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 20px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
