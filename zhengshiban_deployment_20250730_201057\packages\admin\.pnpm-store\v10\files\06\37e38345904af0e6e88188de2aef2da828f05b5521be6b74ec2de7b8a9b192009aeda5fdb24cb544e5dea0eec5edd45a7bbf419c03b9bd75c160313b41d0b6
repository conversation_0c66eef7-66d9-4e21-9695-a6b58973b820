"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_promise = void 0;
const base_config_1 = require("./base-config");
exports.esnext_promise = {
    AggregateError: base_config_1.TYPE_VALUE,
    AggregateErrorConstructor: base_config_1.TYPE,
    PromiseConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=esnext.promise.js.map