<template>
  <div class="short-video-container">
    <!-- 视频列表 -->
    <div 
      class="video-list"
      ref="videoContainer"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @wheel="handleWheel"
    >
      <div 
        v-for="(video, index) in videos" 
        :key="video.id"
        class="video-item"
        :class="{ 'active': index === currentIndex }"
        :style="{ transform: `translateY(${(index - currentIndex) * 100}vh)` }"
      >
        <!-- 视频播放器 -->
        <video
          :ref="el => setVideoRef(el, index)"
          class="video-player"
          :poster="video.coverUrl"
          muted
          playsinline
          webkit-playsinline
          x5-playsinline
          x5-video-player-type="h5"
          x5-video-player-fullscreen="true"
          x5-video-orientation="portrait"
          @loadedmetadata="onVideoLoaded(index)"
          @canplay="onVideoCanPlay(index)"
          @error="onVideoError(index)"
          @click="togglePlay"
        ></video>

        <!-- 播放按钮 -->
        <div 
          v-if="!isPlaying && currentIndex === index" 
          class="play-button"
          @click="togglePlay"
        >
          <svg viewBox="0 0 24 24" fill="white" width="80" height="80">
            <path d="M8 5v14l11-7z"/>
          </svg>
        </div>

        <!-- 加载状态 -->
        <div v-if="video.isLoading" class="loading">
          <div class="spinner"></div>
          <p>加载中...</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="video.hasError" class="error">
          <p>视频加载失败</p>
          <button @click="retryVideo(index)">重试</button>
        </div>

        <!-- 视频信息 -->
        <div class="video-info">
          <h3>{{ video.description }}</h3>
          <p>@{{ video.author.name }}</p>
        </div>

        <!-- 操作按钮 -->
        <div class="video-actions">
          <button @click="toggleLike(index)" :class="{ liked: video.isLiked }">
            ❤️ {{ formatCount(video.likes) }}
          </button>
          <button @click="showComments(index)">
            💬 {{ formatCount(video.comments) }}
          </button>
          <button @click="shareVideo(index)">
            📤 {{ formatCount(video.shares) }}
          </button>
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <button class="back-button" @click="$router.go(-1)">
      ← 返回
    </button>

    <!-- 调试信息 -->
    <div class="debug-info" v-if="showDebug">
      <p>当前视频: {{ currentIndex }}</p>
      <p>总视频数: {{ videos.length }}</p>
      <p>播放状态: {{ isPlaying ? '播放中' : '暂停' }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import Hls from 'hls.js'

// 路由
const router = useRouter()

// 响应式数据
const currentIndex = ref(0)
const isPlaying = ref(false)
const videos = ref<any[]>([])
const videoRefs = ref<{ [key: number]: HTMLVideoElement }>({})
const hlsInstances = ref<{ [key: number]: Hls | null }>({})
const showDebug = ref(true)

// 触摸相关
const touchStartY = ref(0)
const isDragging = ref(false)

// 容器引用
const videoContainer = ref<HTMLElement>()

// 设置视频引用
const setVideoRef = (el: any, index: number) => {
  if (el && el instanceof HTMLVideoElement) {
    videoRefs.value[index] = el
    console.log(`📹 设置视频引用 ${index}:`, el.src)
  }
}

// 加载视频数据
const loadVideos = async () => {
  try {
    console.log('🎬 开始加载短视频数据...')

    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    const response = await fetch(`${apiBaseUrl}/api/v1/videos/short-videos`, {
      headers: {
        'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+'
      }
    })
    
    console.log('📡 API响应状态:', response.status)
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('📦 API响应数据:', data)
    
    if (data.success && data.data && Array.isArray(data.data)) {
      videos.value = data.data.map((video: any, index: number) => {
        // 构建完整的视频URL - 优先使用HLS切片播放
        let videoUrl = ''
        if (video.hls_url && video.has_hls) {
          // 优先使用HLS格式，提供更快更丝滑的播放体验
          const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
          if (video.hls_url.startsWith('http')) {
            videoUrl = video.hls_url
          } else {
            // 处理相对路径的HLS URL
            const path = video.hls_url.startsWith('/') ? video.hls_url : `/${video.hls_url}`
            videoUrl = `${apiBaseUrl}${path}`
          }
          console.log(`📺 视频 ${video.id} 使用HLS格式:`, videoUrl)
        } else if (video.file_path) {
          // 备用MP4格式（仅在没有HLS时使用）
          const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
          const path = video.file_path.startsWith('/') ? video.file_path : `/${video.file_path}`
          videoUrl = `${apiBaseUrl}${path}`
          console.log(`📺 视频 ${video.id} 备用MP4格式:`, videoUrl)
        }

        // 构建完整的封面URL
        let coverUrl = 'https://via.placeholder.com/400x600'
        if (video.cover_image) {
          const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
          const path = video.cover_image.startsWith('/') ? video.cover_image : `/${video.cover_image}`
          coverUrl = `${apiBaseUrl}${path}`
        }

        return {
          id: video.id,
          videoUrl: videoUrl,
          coverUrl: coverUrl,
          description: video.description || video.title || `视频 ${index + 1}`,
          author: {
            id: video.user_id || 1,
            name: video.nickname || video.username || `用户${video.user_id || 1}`,
            avatar: video.user_avatar || 'https://via.placeholder.com/50',
            isFollowing: false
          },
          likes: video.like_count || Math.floor(Math.random() * 1000),
          comments: video.comment_count || Math.floor(Math.random() * 100),
          shares: video.share_count || Math.floor(Math.random() * 50),
          isLiked: false,
          isLoading: false,
          hasError: false
        }
      })
      
      console.log(`✅ 成功加载 ${videos.value.length} 个视频`)
    } else {
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 加载视频失败:', error)
    
    // 创建测试数据
    videos.value = [
      {
        id: 1,
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
        coverUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
        description: '测试短视频 - Big Buck Bunny',
        author: { id: 1, name: '测试用户', avatar: 'https://via.placeholder.com/50', isFollowing: false },
        likes: 1234, comments: 56, shares: 12, isLiked: false, isLoading: false, hasError: false
      },
      {
        id: 2,
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
        coverUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ElephantsDream.jpg',
        description: '测试短视频 - Elephants Dream',
        author: { id: 2, name: '测试用户2', avatar: 'https://via.placeholder.com/50', isFollowing: false },
        likes: 2345, comments: 78, shares: 23, isLiked: false, isLoading: false, hasError: false
      },
      {
        id: 3,
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
        coverUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerBlazes.jpg',
        description: '测试短视频 - For Bigger Blazes',
        author: { id: 3, name: '测试用户3', avatar: 'https://via.placeholder.com/50', isFollowing: false },
        likes: 3456, comments: 89, shares: 34, isLiked: false, isLoading: false, hasError: false
      }
    ]
    
    console.log('🎯 使用测试数据:', videos.value.length, '个视频')
  }
  
  // 初始化第一个视频
  if (videos.value.length > 0) {
    await nextTick()
    initCurrentVideo()
  }
}

// 初始化当前视频
const initCurrentVideo = async () => {
  const video = videoRefs.value[currentIndex.value]
  const videoData = videos.value[currentIndex.value]

  if (video && videoData) {
    try {
      console.log(`🎬 初始化视频 ${currentIndex.value}:`, videoData.videoUrl)

      // 清理之前的HLS实例
      if (hlsInstances.value[currentIndex.value]) {
        hlsInstances.value[currentIndex.value]?.destroy()
        hlsInstances.value[currentIndex.value] = null
      }

      const videoUrl = videoData.videoUrl

      // 检查是否为HLS流
      if (videoUrl && videoUrl.includes('.m3u8')) {
        if (Hls.isSupported()) {
          // 使用HLS.js播放HLS流
          const hls = new Hls({
            enableWorker: true,
            lowLatencyMode: true,
            backBufferLength: 90,
            // 为密钥请求添加API密钥头
            xhrSetup: function(xhr, url) {
              // 修复错误的URL格式（如 http:///api/... 变成 http://localhost:3000/api/...）
              let fixedUrl = url
              if (url.includes('http:///api/')) {
                const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
                fixedUrl = url.replace('http:///', apiBaseUrl + '/')
                console.log('🔧 修复HLS密钥URL:', url, '->', fixedUrl)

                // 重新打开正确的URL
                xhr.open('GET', fixedUrl, true)
              }

              // 为所有API请求添加密钥头
              if (fixedUrl.includes('/api/') || fixedUrl.includes('encryption/key')) {
                xhr.setRequestHeader('X-API-Key', 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+')
                console.log('🔑 为HLS请求添加API密钥头:', fixedUrl)
              }
            }
          })

          hlsInstances.value[currentIndex.value] = hls

          hls.loadSource(videoUrl)
          hls.attachMedia(video)

          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            console.log(`✅ HLS视频 ${currentIndex.value} manifest解析完成`)
            videoData.isLoading = false
          })

          hls.on(Hls.Events.ERROR, (event, data) => {
            console.error(`❌ HLS视频 ${currentIndex.value} 错误:`, data)
            if (data.fatal) {
              videoData.hasError = true
              videoData.isLoading = false
            }
          })

        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Safari原生支持HLS
          video.src = videoUrl
          console.log(`✅ 使用原生HLS播放视频 ${currentIndex.value}`)
        } else {
          console.error(`❌ 浏览器不支持HLS播放视频 ${currentIndex.value}`)
          videoData.hasError = true
        }
      } else {
        // 普通视频文件
        video.src = videoUrl
        await video.load()
        console.log(`✅ 使用原生播放器播放视频 ${currentIndex.value}`)
      }

      video.currentTime = 0

    } catch (error) {
      console.error(`❌ 视频 ${currentIndex.value} 初始化失败:`, error)
      videos.value[currentIndex.value].hasError = true
    }
  }
}

// 播放/暂停控制
const togglePlay = async () => {
  const video = videoRefs.value[currentIndex.value]
  if (!video) return

  try {
    if (isPlaying.value) {
      video.pause()
      isPlaying.value = false
      console.log(`⏸️ 暂停视频 ${currentIndex.value}`)
    } else {
      await video.play()
      isPlaying.value = true
      console.log(`▶️ 播放视频 ${currentIndex.value}`)
    }
  } catch (error) {
    console.error(`❌ 播放控制失败:`, error)
  }
}

// 切换到指定视频
const switchToVideo = async (targetIndex: number) => {
  if (targetIndex < 0 || targetIndex >= videos.value.length) return
  
  console.log(`🔄 切换到视频 ${targetIndex}`)
  
  // 暂停当前视频
  const currentVideo = videoRefs.value[currentIndex.value]
  if (currentVideo) {
    currentVideo.pause()
    isPlaying.value = false
  }
  
  // 更新索引
  currentIndex.value = targetIndex
  
  // 初始化新视频
  await nextTick()
  await initCurrentVideo()
}

// 触摸事件处理
const handleTouchStart = (e: TouchEvent) => {
  touchStartY.value = e.touches[0].clientY
  isDragging.value = true
}

const handleTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) return
  e.preventDefault()
}

const handleTouchEnd = (e: TouchEvent) => {
  if (!isDragging.value) return
  
  const endY = e.changedTouches[0].clientY
  const deltaY = endY - touchStartY.value
  
  // 判断滑动方向
  if (Math.abs(deltaY) > 50) {
    if (deltaY > 0 && currentIndex.value > 0) {
      // 向下滑动，切换到上一个视频
      switchToVideo(currentIndex.value - 1)
    } else if (deltaY < 0 && currentIndex.value < videos.value.length - 1) {
      // 向上滑动，切换到下一个视频
      switchToVideo(currentIndex.value + 1)
    }
  }
  
  isDragging.value = false
}

// 鼠标滚轮事件
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()
  
  if (e.deltaY > 0 && currentIndex.value < videos.value.length - 1) {
    switchToVideo(currentIndex.value + 1)
  } else if (e.deltaY < 0 && currentIndex.value > 0) {
    switchToVideo(currentIndex.value - 1)
  }
}

// 视频事件处理
const onVideoLoaded = (index: number) => {
  videos.value[index].isLoading = false
  console.log(`✅ 视频 ${index} 加载完成`)
}

const onVideoCanPlay = (index: number) => {
  console.log(`✅ 视频 ${index} 可以播放`)
}

const onVideoError = (index: number) => {
  const video = videoRefs.value[index]
  const videoData = videos.value[index]

  // 只有在视频有有效src且当前正在播放时才报告错误
  if (video && video.src && video.src !== '' && index === currentIndex.value) {
    console.error(`❌ 视频 ${index} 加载错误:`, video.src)
    videoData.hasError = true
    videoData.isLoading = false
  } else {
    // 忽略无src或非当前视频的错误（这些是预期的）
    console.log(`🔇 忽略视频 ${index} 的预期错误（无src或非当前视频）`)
  }
}

// 操作方法
const retryVideo = (index: number) => {
  videos.value[index].hasError = false
  videos.value[index].isLoading = true
  const video = videoRefs.value[index]
  if (video) {
    video.load()
  }
}

const toggleLike = (index: number) => {
  const video = videos.value[index]
  video.isLiked = !video.isLiked
  video.likes += video.isLiked ? 1 : -1
}

const showComments = (index: number) => {
  console.log('显示评论', index)
}

const shareVideo = (index: number) => {
  console.log('分享视频', index)
}

const formatCount = (count: number): string => {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M'
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K'
  }
  return count.toString()
}

// 生命周期
onMounted(() => {
  loadVideos()
})

onUnmounted(() => {
  // 清理所有HLS实例
  Object.values(hlsInstances.value).forEach(hls => {
    if (hls) {
      hls.destroy()
    }
  })

  // 清理所有视频
  Object.values(videoRefs.value).forEach(video => {
    if (video) {
      video.pause()
      video.src = ''
    }
  })
})
</script>

<style scoped>
.short-video-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden;
  user-select: none;
}

.video-list {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.video-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.play-button:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translate(-50%, -50%) scale(1.1);
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  text-align: center;
  z-index: 10;
}

.error button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #ff2d55;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
}

.video-info {
  position: absolute;
  left: 16px;
  bottom: 120px;
  right: 80px;
  color: white;
  z-index: 10;
}

.video-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.video-info p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.video-actions {
  position: absolute;
  right: 16px;
  bottom: 120px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 10;
}

.video-actions button {
  background: transparent;
  border: none;
  color: white;
  font-size: 12px;
  cursor: pointer;
  text-align: center;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.video-actions button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.video-actions button.liked {
  color: #ff2d55;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 16px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  z-index: 20;
}

.debug-info {
  position: absolute;
  top: 60px;
  left: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 20;
}

.debug-info p {
  margin: 2px 0;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .video-actions {
    right: 12px;
    bottom: 100px;
  }

  .video-info {
    left: 12px;
    right: 70px;
    bottom: 100px;
  }

  .play-button {
    width: 80px;
    height: 80px;
  }
}

/* 防止选中文本 */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
</style>
