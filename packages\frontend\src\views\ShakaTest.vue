<template>
  <div class="shaka-test-page">
    <h1>🎬 简化视频播放器测试页面</h1>
    
    <div class="test-container">
      <div class="video-wrapper">
        <VideoPlayer
          :src="testVideoUrl"
          :autoplay="false"
          :muted="false"
          class="test-video"
          @play="onPlay"
          @pause="onPause"
          @error="onError"
          @loadstart="onLoadStart"
          @canplay="onCanPlay"
          @ready="onReady"
        />
      </div>
      
      <div class="controls">
        <h3>测试控制</h3>
        <div class="control-group">
          <label>视频URL:</label>
          <select v-model="testVideoUrl" @change="onUrlChange">
            <option value="http://localhost:3000/hls/video_116/playlist.m3u8">视频 116</option>
            <option value="http://localhost:3000/hls/video_34/playlist.m3u8">视频 34</option>
            <option value="http://localhost:3000/hls/video_35/playlist.m3u8">视频 35</option>
            <option value="http://localhost:3000/hls/video_31/playlist.m3u8">视频 31</option>
            <option value="http://localhost:3000/hls/video_32/playlist.m3u8">视频 32</option>
          </select>
        </div>
        
        <div class="status">
          <h4>播放状态:</h4>
          <ul>
            <li>加载状态: {{ loadStatus }}</li>
            <li>播放状态: {{ playStatus }}</li>
            <li>错误信息: {{ errorMessage || '无' }}</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="logs">
      <h3>日志信息:</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level" :class="log.level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import VideoPlayer from '@/components/VideoPlayer.vue'

// 状态
const testVideoUrl = ref('http://localhost:3000/hls/video_116/playlist.m3u8')
const loadStatus = ref('未开始')
const playStatus = ref('暂停')
const errorMessage = ref('')
const logs = ref<Array<{time: string, level: string, message: string}>>([])

// 添加日志
const addLog = (level: string, message: string) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  logs.value.unshift({
    time,
    level,
    message
  })
  
  // 只保留最新的50条日志
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
  
  console.log(`[${level}] ${message}`)
}

// 事件处理
const onPlay = () => {
  playStatus.value = '播放中'
  addLog('INFO', '视频开始播放')
}

const onPause = () => {
  playStatus.value = '暂停'
  addLog('INFO', '视频暂停')
}

const onError = (error: any) => {
  errorMessage.value = error?.message || error?.toString() || '未知错误'
  addLog('ERROR', `播放错误: ${errorMessage.value}`)
}

const onLoadStart = () => {
  loadStatus.value = '加载中'
  addLog('INFO', '开始加载视频')
}

const onCanPlay = () => {
  loadStatus.value = '可以播放'
  addLog('SUCCESS', '视频加载完成，可以播放')
}

const onReady = () => {
  loadStatus.value = '播放器就绪'
  addLog('SUCCESS', '终极播放器初始化完成')
}

const onUrlChange = () => {
  loadStatus.value = '切换中'
  playStatus.value = '暂停'
  errorMessage.value = ''
  addLog('INFO', `切换视频源: ${testVideoUrl.value}`)
}

onMounted(() => {
  addLog('INFO', 'Shaka Player 测试页面已加载')
})
</script>

<style scoped>
.shaka-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.test-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.video-wrapper {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  aspect-ratio: 16/9;
}

.test-video {
  width: 100%;
  height: 100%;
}

.controls {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.control-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.status {
  margin-top: 20px;
}

.status h4 {
  margin-bottom: 10px;
  color: #333;
}

.status ul {
  list-style: none;
  padding: 0;
}

.status li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.logs {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  background: #f9f9f9;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  min-width: 60px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-level.INFO {
  color: #2196F3;
}

.log-level.SUCCESS {
  color: #4CAF50;
}

.log-level.ERROR {
  color: #F44336;
}

.log-message {
  flex: 1;
  color: #333;
}

@media (max-width: 768px) {
  .test-container {
    grid-template-columns: 1fr;
  }
}
</style>
