<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 视频模型
 * @mixin \think\Model
 */
class Video extends Model
{
    // 设置表名
    protected $name = 'videos';

    // 设置主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'category_id' => 'integer',
        'user_id' => 'integer',
        'duration' => 'integer',
        'file_size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'view_count' => 'integer',
        'like_count' => 'integer',
        'comment_count' => 'integer',
        'share_count' => 'integer',
        'collect_count' => 'integer',
        'completion_rate' => 'float',
        'engagement_score' => 'float',
        'is_featured' => 'boolean',
        'is_private' => 'boolean',
        'is_free' => 'boolean',
        'points_price' => 'integer',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        // 广告检测相关字段
        'has_ads' => 'boolean',
        'ad_segments' => 'json',
        'ad_confidence_score' => 'float',
        'ad_last_detected_at' => 'datetime'
    ];

    // 字段验证规则
    protected $rule = [
        'title' => 'require|length:1,200',
        'video_type' => 'require|in:long,short',
        'category_id' => 'require|integer|gt:0',
        'user_id' => 'require|integer|gt:0',
        'file_path' => 'require'
    ];

    // 验证提示信息
    protected $message = [
        'title.require' => '视频标题不能为空',
        'title.length' => '视频标题长度不能超过200个字符',
        'category_id.require' => '分类不能为空',
        'category_id.integer' => '分类ID必须是整数',
        'category_id.gt' => '分类ID必须大于0',
        'user_id.require' => '用户ID不能为空',
        'user_id.integer' => '用户ID必须是整数',
        'user_id.gt' => '用户ID必须大于0',
        'file_path.require' => '视频文件路径不能为空'
    ];

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联分类模型
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 状态获取器 - 转换状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            'uploading' => '上传中',
            'processing' => '处理中',
            'published' => '已发布',
            'private' => '私有',
            'deleted' => '已删除'
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 审核状态获取器
     */
    public function getAuditStatusTextAttr($value, $data)
    {
        $status = [
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝'
        ];
        return $status[$data['audit_status']] ?? '未知';
    }

    /**
     * 文件大小获取器 - 格式化显示
     */
    public function getFileSizeTextAttr($value, $data)
    {
        $size = $data['file_size'];
        if ($size < 1024) {
            return $size . 'B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . 'KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / 1024 / 1024, 2) . 'MB';
        } else {
            return round($size / 1024 / 1024 / 1024, 2) . 'GB';
        }
    }

    /**
     * 时长获取器 - 格式化显示
     */
    public function getDurationTextAttr($value, $data)
    {
        $duration = $data['duration'];
        $hours = floor($duration / 3600);
        $minutes = floor(($duration % 3600) / 60);
        $seconds = $duration % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $seconds);
        }
    }

    /**
     * 计算互动评分
     */
    public function calculateEngagementScore()
    {
        $viewCount = $this->view_count ?: 1;
        $likeRate = ($this->like_count / $viewCount) * 100;
        $commentRate = ($this->comment_count / $viewCount) * 100;
        $shareRate = ($this->share_count / $viewCount) * 100;
        $collectRate = ($this->collect_count / $viewCount) * 100;
        $completionRate = $this->completion_rate ?: 0;

        // 综合评分算法
        $score = ($likeRate * 0.3) + ($commentRate * 0.25) + ($shareRate * 0.2) +
                ($collectRate * 0.15) + ($completionRate * 0.1);

        $this->engagement_score = round($score, 2);
        $this->save();

        return $this->engagement_score;
    }

    /**
     * 获取推荐视频
     */
    public static function getRecommendedVideos($videoType = 'short', $limit = 20)
    {
        return self::where('video_type', $videoType)
            ->where('status', 'published')
            ->where('audit_status', 'approved')
            ->order('engagement_score', 'desc')
            ->order('view_count', 'desc')
            ->order('published_at', 'desc')
            ->limit($limit)
            ->select();
    }

    /**
     * 检查是否为短视频
     */
    public function isShortVideo()
    {
        return $this->video_type === 'short';
    }

    /**
     * 检查是否为长视频
     */
    public function isLongVideo()
    {
        return $this->video_type === 'long';
    }

    /**
     * 检查是否包含广告
     */
    public function hasAds(): bool
    {
        return $this->has_ads === 1;
    }

    /**
     * 获取广告段信息
     */
    public function getAdSegments(): array
    {
        if (!$this->ad_segments) {
            return [];
        }

        $segments = json_decode($this->ad_segments, true);
        return is_array($segments) ? $segments : [];
    }

    /**
     * 是否需要广告检测
     */
    public function needsAdDetection(): bool
    {
        return $this->ad_detection_status === 'pending' ||
               $this->ad_detection_status === null;
    }

    /**
     * 是否为采集视频
     */
    public function isCollectedVideo(): bool
    {
        return $this->source_type === 'collect' ||
               !empty($this->collect_source_id);
    }

    /**
     * 获取广告检测置信度等级
     */
    public function getAdConfidenceLevel(): string
    {
        $score = $this->ad_confidence_score ?? 0;

        if ($score >= 0.8) return 'high';
        if ($score >= 0.5) return 'medium';
        if ($score >= 0.3) return 'low';
        return 'none';
    }

    /**
     * 获取广告检测状态文本
     */
    public function getAdDetectionStatusText(): string
    {
        $statusMap = [
            'pending' => '待检测',
            'detected' => '已检测',
            'manual' => '手动标记',
            'skipped' => '已跳过'
        ];

        return $statusMap[$this->ad_detection_status] ?? '未知';
    }

    /**
     * 作用域：有广告的视频
     */
    public function scopeWithAds($query)
    {
        return $query->where('has_ads', 1);
    }

    /**
     * 作用域：无广告的视频
     */
    public function scopeWithoutAds($query)
    {
        return $query->where('has_ads', 0);
    }

    /**
     * 作用域：待检测广告的视频
     */
    public function scopePendingAdDetection($query)
    {
        return $query->where('ad_detection_status', 'pending');
    }

    /**
     * 作用域：采集的视频
     */
    public function scopeCollected($query)
    {
        return $query->where(function($q) {
            $q->where('source_type', 'collect')
              ->whereOr('collect_source_id', '>', 0);
        });
    }
}
