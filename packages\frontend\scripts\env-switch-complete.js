#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 环境配置
const environments = {
  local: {
    VITE_API_BASE_URL: 'http://localhost:3000',
    VITE_FRONTEND_BASE_URL: 'http://localhost:3002',
    VITE_ADMIN_BASE_URL: 'http://localhost:3001',
    VITE_WS_URL: 'ws://localhost:3000/ws',
    VITE_USE_REMOTE: 'false',
    VITE_APP_ENV: 'development'
  },
  remote: {
    VITE_API_BASE_URL: 'http://*************:3000',
    VITE_FRONTEND_BASE_URL: 'http://*************:3002',
    VITE_ADMIN_BASE_URL: 'http://*************:3001',
    VITE_WS_URL: 'ws://*************:3000/ws',
    VITE_USE_REMOTE: 'true',
    VITE_APP_ENV: 'production'
  }
};

// 获取命令行参数
const targetEnv = process.argv[2];
const autoBuild = process.argv.includes('--build');
const autoRestart = process.argv.includes('--restart');

if (!targetEnv || !environments[targetEnv]) {
  console.log('使用方法: node env-switch-complete.js <local|remote> [--build] [--restart]');
  console.log('');
  console.log('可用环境:');
  console.log('  local  - 本地开发环境 (localhost)');
  console.log('  remote - 远程生产环境 (*************)');
  console.log('');
  console.log('可选参数:');
  console.log('  --build   - 自动重新构建前端');
  console.log('  --restart - 自动重启前端容器');
  console.log('');
  console.log('示例:');
  console.log('  node env-switch-complete.js local --build --restart');
  process.exit(1);
}

// 环境文件路径
const envPath = path.join(__dirname, '../.env.local');
const envSimplePath = path.join(__dirname, '../.env');

// 生成环境变量内容
function generateEnvContent(env) {
  const config = environments[env];
  const timestamp = new Date().toISOString();
  
  return `# ===================================================================
# 🎬 51吃瓜网 - 前端环境配置
# 📅 更新时间: ${timestamp}
# 🎯 当前环境: ${env === 'local' ? '本地开发环境' : '远程生产环境'}
# ===================================================================

# ==========================================
# 🌍 环境基础配置
# ==========================================
VITE_APP_NAME=51吃瓜网${env === 'local' ? ' (开发版)' : ''}
VITE_APP_TITLE=51吃瓜网
VITE_APP_VERSION=2.0.0
VITE_APP_ENV=${config.VITE_APP_ENV}
VITE_APP_DEBUG=${env === 'local' ? 'true' : 'false'}
VITE_APP_DESCRIPTION=企业级视频平台

# ==========================================
# 🌐 服务地址配置
# ==========================================
VITE_API_BASE_URL=${config.VITE_API_BASE_URL}
VITE_FRONTEND_BASE_URL=${config.VITE_FRONTEND_BASE_URL}
VITE_ADMIN_BASE_URL=${config.VITE_ADMIN_BASE_URL}
VITE_WS_URL=${config.VITE_WS_URL}

# 远程服务地址 (备用)
VITE_REMOTE_API_BASE_URL=http://*************:3000
VITE_REMOTE_FRONTEND_BASE_URL=http://*************:3002
VITE_REMOTE_ADMIN_BASE_URL=http://*************:3001
VITE_REMOTE_WS_URL=ws://*************:3000/ws

# 环境切换标志
VITE_USE_REMOTE=${config.VITE_USE_REMOTE}

# ==========================================
# 🔑 API密钥配置
# ==========================================
VITE_API_KEY_USER="ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+"

# ==========================================
# 🎥 视频配置
# ==========================================
VITE_VIDEO_AUTO_PLAY=false
VITE_VIDEO_PRELOAD=metadata
VITE_VIDEO_QUALITY_AUTO=true

# ==========================================
# 📦 上传配置
# ==========================================
VITE_UPLOAD_MAX_SIZE=500
VITE_UPLOAD_CHUNK_SIZE=2
VITE_UPLOAD_CONCURRENT=3
VITE_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,mp4,avi,mov,wmv

# ==========================================
# 🔧 开发工具配置
# ==========================================
VITE_ENABLE_DEVTOOLS=${env === 'local' ? 'true' : 'false'}
VITE_SOURCE_MAP=${env === 'local' ? 'true' : 'false'}
VITE_CONSOLE_LOG=${env === 'local' ? 'true' : 'false'}
VITE_PERFORMANCE_MONITOR=${env === 'local' ? 'true' : 'false'}

# ==========================================
# 🎯 功能开关
# ==========================================
VITE_FEATURE_LIVE=true
VITE_FEATURE_VIP=true
VITE_FEATURE_COMMENT=true
VITE_FEATURE_SHARE=true
VITE_ENABLE_MOCK=false

# ==========================================
# 📊 缓存配置
# ==========================================
VITE_CACHE_ENABLED=true
VITE_CACHE_EXPIRE_TIME=60000

# ==========================================
# 🌐 网络配置
# ==========================================
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3
VITE_PROXY_ENABLED=true
VITE_PROXY_TARGET=http://zhengshiban-api

# ==========================================
# 🔒 安全配置
# ==========================================
VITE_SECURE_MODE=${env === 'remote' ? 'true' : 'false'}
VITE_HTTPS_ONLY=${env === 'remote' ? 'true' : 'false'}

# ==========================================
# 📈 监控配置
# ==========================================
VITE_ANALYTICS_ID=
VITE_SENTRY_DSN=

# ==========================================
# 🎨 CDN配置
# ==========================================
VITE_CDN_URL=
VITE_STATIC_URL=
`;
}

// 生成简化的 .env 文件
function generateSimpleEnvContent(env) {
  const config = environments[env];
  
  return `# 简化环境配置
VITE_API_BASE_URL=${config.VITE_API_BASE_URL}
VITE_API_KEY_USER="ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+"
VITE_USE_REMOTE=${config.VITE_USE_REMOTE}
VITE_APP_ENV=${config.VITE_APP_ENV}
`;
}

// 执行命令
function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: path.join(__dirname, '../..') });
    console.log(`✅ ${description}完成`);
    return true;
  } catch (error) {
    console.error(`❌ ${description}失败:`, error.message);
    return false;
  }
}

try {
  // 生成新的环境配置
  const envContent = generateEnvContent(targetEnv);
  const simpleEnvContent = generateSimpleEnvContent(targetEnv);
  
  // 写入文件
  fs.writeFileSync(envPath, envContent, 'utf8');
  fs.writeFileSync(envSimplePath, simpleEnvContent, 'utf8');
  
  console.log(`✅ 环境切换成功！`);
  console.log(`📁 配置文件: ${envPath}`);
  console.log(`📁 简化配置: ${envSimplePath}`);
  console.log(`🎯 当前环境: ${targetEnv === 'local' ? '本地开发环境' : '远程生产环境'}`);
  console.log('');
  console.log('📋 主要配置:');
  console.log(`   API地址: ${environments[targetEnv].VITE_API_BASE_URL}`);
  console.log(`   前端地址: ${environments[targetEnv].VITE_FRONTEND_BASE_URL}`);
  console.log(`   管理后台: ${environments[targetEnv].VITE_ADMIN_BASE_URL}`);
  console.log(`   WebSocket: ${environments[targetEnv].VITE_WS_URL}`);
  console.log('');
  
  // 自动构建
  if (autoBuild) {
    console.log('🔨 开始自动构建...');
    if (runCommand('cd packages/frontend && npm run build', '构建前端')) {
      console.log('');
    } else {
      process.exit(1);
    }
  }
  
  // 自动重启
  if (autoRestart) {
    console.log('🔄 开始重启容器...');
    if (runCommand('docker-compose restart frontend', '重启前端容器')) {
      console.log('');
    } else {
      process.exit(1);
    }
  }
  
  if (!autoBuild && !autoRestart) {
    console.log('🔄 下一步操作:');
    console.log('   1. 重新构建前端: npm run build');
    console.log('   2. 重启容器: docker-compose restart frontend');
    console.log('');
    console.log('💡 或者使用自动化命令:');
    console.log(`   node scripts/env-switch-complete.js ${targetEnv} --build --restart`);
  }
  
} catch (error) {
  console.error('❌ 环境切换失败:', error.message);
  process.exit(1);
}
