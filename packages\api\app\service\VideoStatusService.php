<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\exception\VideoProcessingException;

/**
 * 视频状态管理服务 - 简化状态管理
 */
class VideoStatusService
{
    /**
     * 视频状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_PRIVATE = 'private';
    const STATUS_DELETED = 'deleted';
    
    /**
     * 审核状态常量
     */
    const AUDIT_PENDING = 'pending';
    const AUDIT_APPROVED = 'approved';
    const AUDIT_REJECTED = 'rejected';
    
    /**
     * 处理步骤常量
     */
    const STEP_UPLOAD = 'upload';
    const STEP_THUMBNAIL = 'thumbnail';
    const STEP_TRANSCODE = 'transcode';
    const STEP_AUDIT = 'audit';
    
    /**
     * 更新视频处理状态
     * 
     * @param int $videoId 视频ID
     * @param string $step 处理步骤
     * @param string $status 状态 (pending, processing, completed, failed)
     * @param int $progress 进度 (0-100)
     * @param string $message 消息
     * @param string|null $errorMessage 错误消息
     * @return bool
     */
    public function updateProcessingStatus(
        int $videoId, 
        string $step, 
        string $status, 
        int $progress = 0, 
        string $message = '', 
        ?string $errorMessage = null
    ): bool {
        try {
            $updateData = [
                'status' => $status,
                'progress' => $progress,
                'message' => $message,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            if ($errorMessage) {
                $updateData['error_message'] = $errorMessage;
            }
            
            if ($status === 'processing' && $progress === 0) {
                $updateData['started_at'] = date('Y-m-d H:i:s');
            }
            
            if (in_array($status, ['completed', 'failed'])) {
                $updateData['completed_at'] = date('Y-m-d H:i:s');
                if ($status === 'completed') {
                    $updateData['progress'] = 100;
                }
            }
            
            $result = Db::table('video_processing_status')
                ->where('video_id', $videoId)
                ->where('process_type', $step)
                ->update($updateData);
            
            Log::info('更新视频处理状态', [
                'video_id' => $videoId,
                'step' => $step,
                'status' => $status,
                'progress' => $progress,
                'result' => $result
            ]);
            
            return $result > 0;
            
        } catch (\Exception $e) {
            Log::error('更新处理状态失败', [
                'video_id' => $videoId,
                'step' => $step,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 更新视频基本状态
     * 
     * @param int $videoId 视频ID
     * @param string $status 视频状态
     * @param array $additionalData 额外数据
     * @return bool
     */
    public function updateVideoStatus(int $videoId, string $status, array $additionalData = []): bool
    {
        try {
            // 验证状态值
            $validStatuses = [self::STATUS_DRAFT, self::STATUS_PUBLISHED, self::STATUS_PRIVATE, self::STATUS_DELETED];
            if (!in_array($status, $validStatuses)) {
                Log::warning('无效的视频状态', [
                    'video_id' => $videoId,
                    'invalid_status' => $status,
                    'valid_statuses' => $validStatuses
                ]);
                $status = self::STATUS_PUBLISHED; // 默认为已发布
            }
            
            $updateData = array_merge([
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ], $additionalData);
            
            $result = Db::table('videos')
                ->where('id', $videoId)
                ->update($updateData);
            
            Log::info('更新视频状态', [
                'video_id' => $videoId,
                'status' => $status,
                'additional_data' => $additionalData,
                'result' => $result
            ]);
            
            return $result > 0;
            
        } catch (\Exception $e) {
            Log::error('更新视频状态失败', [
                'video_id' => $videoId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取视频处理状态摘要
     * 
     * @param int $videoId 视频ID
     * @return array
     */
    public function getProcessingSummary(int $videoId): array
    {
        try {
            $statuses = Db::table('video_processing_status')
                ->where('video_id', $videoId)
                ->order('created_at asc')
                ->select();
            
            if (empty($statuses)) {
                return [
                    'overall_status' => 'none',
                    'overall_progress' => 0,
                    'current_step' => '',
                    'steps' => []
                ];
            }
            
            $steps = [];
            $totalProgress = 0;
            $completedSteps = 0;
            $currentStep = '';
            $overallStatus = 'completed';
            
            foreach ($statuses as $status) {
                $steps[$status['process_type']] = [
                    'status' => $status['status'],
                    'progress' => $status['progress'],
                    'message' => $status['message'],
                    'error_message' => $status['error_message'],
                    'updated_at' => $status['updated_at']
                ];
                
                $totalProgress += $status['progress'];
                
                if ($status['status'] === 'completed') {
                    $completedSteps++;
                } elseif ($status['status'] === 'failed') {
                    $overallStatus = 'failed';
                    $currentStep = $status['process_type'];
                } elseif ($status['status'] === 'processing') {
                    $overallStatus = 'processing';
                    $currentStep = $status['process_type'];
                } elseif ($status['status'] === 'pending' && $overallStatus === 'completed') {
                    $overallStatus = 'pending';
                }
            }
            
            $overallProgress = count($statuses) > 0 ? round($totalProgress / count($statuses)) : 0;
            
            return [
                'overall_status' => $overallStatus,
                'overall_progress' => $overallProgress,
                'current_step' => $currentStep,
                'completed_steps' => $completedSteps,
                'total_steps' => count($statuses),
                'steps' => $steps
            ];
            
        } catch (\Exception $e) {
            Log::error('获取处理状态摘要失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'overall_status' => 'error',
                'overall_progress' => 0,
                'current_step' => '',
                'error_message' => $e->getMessage(),
                'steps' => []
            ];
        }
    }
    
    /**
     * 检查视频是否可以审核
     * 
     * @param int $videoId 视频ID
     * @return array
     */
    public function canAudit(int $videoId): array
    {
        $summary = $this->getProcessingSummary($videoId);
        
        // 检查转码是否完成
        if (!isset($summary['steps']['transcode'])) {
            return [
                'can_audit' => false,
                'reason' => '视频尚未开始转码处理'
            ];
        }
        
        $transcodeStatus = $summary['steps']['transcode'];
        
        if ($transcodeStatus['status'] === 'failed') {
            return [
                'can_audit' => false,
                'reason' => '视频转码失败，无法审核'
            ];
        }
        
        if ($transcodeStatus['status'] !== 'completed') {
            return [
                'can_audit' => false,
                'reason' => '视频转码尚未完成，请等待处理完成后审核'
            ];
        }
        
        return [
            'can_audit' => true,
            'reason' => '可以审核'
        ];
    }
    
    /**
     * 重置处理状态
     * 
     * @param int $videoId 视频ID
     * @param string $step 处理步骤
     * @return bool
     */
    public function resetProcessingStatus(int $videoId, string $step): bool
    {
        return $this->updateProcessingStatus(
            $videoId,
            $step,
            'pending',
            0,
            '等待重新处理',
            null
        );
    }
}
