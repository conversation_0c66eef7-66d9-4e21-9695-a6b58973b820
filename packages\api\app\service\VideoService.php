<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use think\facade\Queue;
use app\model\Video as VideoModel;

/**
 * 统一视频服务
 * 
 * 整合所有视频相关功能：
 * - 视频CRUD操作（创建、读取、更新、删除）
 * - 视频播放和流媒体处理
 * - 视频搜索和推荐
 * - 视频处理和转码管理
 * - 视频统计和分析
 * - 视频缓存和性能优化
 */
class VideoService
{
    /**
     * 数据库服务
     */
    private DatabaseService $databaseService;

    /**
     * 响应服务
     */
    private ResponseService $responseService;

    /**
     * 验证服务
     */
    private ValidationService $validationService;

    /**
     * 缓存配置
     */
    private array $cacheConfig = [
        'video_detail_ttl' => 3600,     // 视频详情缓存1小时
        'video_list_ttl' => 1800,      // 视频列表缓存30分钟
        'hot_videos_ttl' => 7200,      // 热门视频缓存2小时
        'user_videos_ttl' => 1800,     // 用户视频缓存30分钟
    ];

    /**
     * 视频处理配置
     */
    private array $processingConfig = [
        'enabled_qualities' => ['720p', '1080p'],
        'generate_hls' => true,
        'generate_thumbnail' => true,
        'upload_to_cloud' => true,
        'cleanup_temp' => false,
    ];

    public function __construct()
    {
        $this->databaseService = new DatabaseService();
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
    }

    /**
     * 获取视频列表
     */
    public function getVideoList(array $params = []): array
    {
        // 如果是用户个人视频，需要传递当前用户ID
        if (!empty($params['user_only'])) {
            // 从请求中获取用户信息（假设已通过中间件验证）
            $request = request();
            if (isset($request->userInfo['id'])) {
                $params['current_user_id'] = $request->userInfo['id'];
            }
        }

        // 使用数据库服务的优化查询
        return $this->databaseService->getOptimizedVideoList($params);
    }

    /**
     * 获取视频详情
     */
    public function getVideoDetail(int $id, bool $incrementView = true): ?array
    {
        $cacheKey = "video_detail_{$id}";
        
        // 尝试从缓存获取
        $video = Cache::get($cacheKey);
        
        if (!$video) {
            // 从数据库获取
            $video = $this->databaseService->table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field([
                    'v.*',
                    'u.username', 'u.nickname', 'u.avatar as user_avatar',
                    'c.name as category_name'
                ])
                ->where('v.id', $id)
                ->where('v.status', 'published')
                ->find();
            
            if ($video) {
                // 获取播放地址
                $video['play_urls'] = $this->getVideoPlayUrls($id);

                // 处理广告信息
                $video = $this->processAdInfo($video);

                // 缓存视频详情
                Cache::set($cacheKey, $video, $this->cacheConfig['video_detail_ttl']);
            }
        }
        
        if ($video && $incrementView) {
            // 异步增加观看次数
            $this->incrementViewCountAsync($id);
        }
        
        return $video ? $video->toArray() : null;
    }

    /**
     * 创建视频
     */
    public function createVideo(array $data, int $userId): array
    {
        // 验证数据
        $validation = $this->validationService->validate(
            $data,
            $this->validationService->getVideoCreateRules(),
            $this->validationService->getVideoCreateMessages()
        );

        if ($validation !== true) {
            throw new \Exception($validation['error']);
        }

        // 验证分类是否存在
        if (!$this->validateCategoryExists($data['category_id'])) {
            throw new \Exception('指定的分类不存在');
        }

        // 构建视频数据
        $videoData = $this->buildVideoData($data, $userId);

        // 使用事务创建视频
        return $this->databaseService->transaction(function() use ($videoData, $data) {
            // 创建视频记录
            $videoId = $this->databaseService->writeTable('videos')->insertGetId($videoData);

            // 如果有文件路径，触发视频处理
            if (!empty($data['file_path'])) {
                $this->triggerVideoProcessing($videoId, $data['file_path']);
            }

            // 清除相关缓存
            $this->clearVideoCache();

            return [
                'video_id' => $videoId,
                'title' => $data['title'],
                'status' => 'published',
                'audit_status' => 'pending',
                'processing_status' => !empty($data['file_path']) ? 'pending' : null
            ];
        });
    }

    /**
     * 更新视频
     */
    public function updateVideo(int $id, array $data, int $userId): bool
    {
        // 检查视频是否存在且属于当前用户
        $video = $this->databaseService->table('videos')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->find();

        if (!$video) {
            throw new \Exception('视频不存在或无权限修改');
        }

        // 验证数据
        $validation = $this->validationService->validate(
            $data,
            $this->validationService->getVideoUpdateRules(),
            $this->validationService->getVideoUpdateMessages()
        );

        if ($validation !== true) {
            throw new \Exception($validation['error']);
        }

        // 更新视频
        $result = $this->databaseService->writeTable('videos')
            ->where('id', $id)
            ->update($data);

        if ($result) {
            // 清除缓存
            Cache::delete("video_detail_{$id}");
            $this->clearVideoCache();
        }

        return $result > 0;
    }

    /**
     * 删除视频
     */
    public function deleteVideo(int $id, int $userId): bool
    {
        // 检查视频是否存在且属于当前用户
        $video = $this->databaseService->table('videos')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->find();

        if (!$video) {
            throw new \Exception('视频不存在或无权限删除');
        }

        return $this->databaseService->transaction(function() use ($id, $video) {
            // 软删除视频
            $result = $this->databaseService->writeTable('videos')
                ->where('id', $id)
                ->update([
                    'status' => 'deleted',
                    'deleted_at' => date('Y-m-d H:i:s')
                ]);

            if ($result) {
                // 删除相关数据
                $this->deleteVideoRelatedData($id);
                
                // 清除缓存
                Cache::delete("video_detail_{$id}");
                $this->clearVideoCache();
            }

            return $result > 0;
        });
    }

    /**
     * 搜索视频
     */
    public function searchVideos(string $keyword, array $params = []): array
    {
        $page = max(1, $params['page'] ?? 1);
        $limit = min($params['limit'] ?? 20, 50);
        $offset = ($page - 1) * $limit;

        // 构建基础查询条件
        $where = [
            ['v.status', '=', 'published'],
            ['v.audit_status', '=', 'approved']
        ];

        // 添加分类过滤
        if (!empty($params['category_id'])) {
            $where[] = ['v.category_id', '=', $params['category_id']];
        }

        // 执行搜索
        $query = $this->databaseService->table('videos')
            ->alias('v')
            ->leftJoin('users u', 'v.user_id = u.id')
            ->leftJoin('video_categories c', 'v.category_id = c.id')
            ->field([
                'v.id', 'v.title', 'v.description', 'v.cover_image',
                'v.duration', 'v.view_count', 'v.like_count', 'v.created_at',
                'u.username', 'u.nickname', 'u.avatar as user_avatar',
                'c.name as category_name'
            ])
            ->where($where)
            ->where(function($query) use ($keyword) {
                $query->whereLike('v.title', "%{$keyword}%")
                      ->whereOr('v.description', 'like', "%{$keyword}%")
                      ->whereOr('u.nickname', 'like', "%{$keyword}%");
            });

        // 获取总数
        $total = $query->count();

        // 获取数据
        $videos = $query->order('v.view_count desc, v.created_at desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();

        return [
            'videos' => $videos,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
            'keyword' => $keyword
        ];
    }

    /**
     * 获取热门视频
     */
    public function getHotVideos(int $limit = 10): array
    {
        $cacheKey = "hot_videos_{$limit}";
        
        $videos = Cache::get($cacheKey);
        
        if (!$videos) {
            $videos = $this->databaseService->table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field([
                    'v.id', 'v.title', 'v.cover_image', 'v.duration',
                    'v.view_count', 'v.like_count', 'v.created_at',
                    'u.username', 'u.nickname', 'u.avatar as user_avatar',
                    'c.name as category_name'
                ])
                ->where([
                    ['v.status', '=', 'published'],
                    ['v.audit_status', '=', 'approved'],
                    ['v.created_at', '>=', date('Y-m-d H:i:s', strtotime('-30 days'))]
                ])
                ->order('v.view_count desc, v.like_count desc')
                ->limit($limit)
                ->select()
                ->toArray();
            
            Cache::set($cacheKey, $videos, $this->cacheConfig['hot_videos_ttl']);
        }
        
        return $videos;
    }

    /**
     * 获取用户视频列表
     */
    public function getUserVideos(int $userId, array $params = []): array
    {
        $page = max(1, $params['page'] ?? 1);
        $limit = min($params['limit'] ?? 20, 50);
        $offset = ($page - 1) * $limit;

        $cacheKey = "user_videos_{$userId}_{$page}_{$limit}";

        $result = Cache::get($cacheKey);

        if (!$result) {
            // 获取总数
            $total = $this->databaseService->table('videos')
                ->where('user_id', $userId)
                ->where('status', '<>', 'deleted')
                ->count();

            // 获取视频列表
            $videos = $this->databaseService->table('videos')
                ->alias('v')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field([
                    'v.id', 'v.title', 'v.description', 'v.cover_image',
                    'v.duration', 'v.view_count', 'v.like_count', 'v.status',
                    'v.audit_status', 'v.created_at', 'v.updated_at',
                    'c.name as category_name'
                ])
                ->where('v.user_id', $userId)
                ->where('v.status', '<>', 'deleted')
                ->order('v.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            $result = [
                'videos' => $videos,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];

            Cache::set($cacheKey, $result, $this->cacheConfig['user_videos_ttl']);
        }

        return $result;
    }

    /**
     * 记录视频观看
     */
    public function recordView(int $videoId, ?int $userId = null, string $ip = ''): bool
    {
        // 防重复观看：同一IP或用户在5分钟内不重复计数
        $cacheKey = $userId ? "view_log_user_{$userId}_{$videoId}" : "view_log_ip_{$ip}_{$videoId}";

        if (Cache::has($cacheKey)) {
            return true; // 已记录过
        }

        return $this->databaseService->transaction(function() use ($videoId, $userId, $ip, $cacheKey) {
            // 更新视频观看数
            $this->databaseService->writeTable('videos')
                ->where('id', $videoId)
                ->inc('view_count')
                ->update();

            // 记录观看日志（如果有观看日志表）
            try {
                $this->databaseService->writeTable('video_play_logs')->insert([
                    'video_id' => $videoId,
                    'user_id' => $userId,
                    'ip_address' => $ip,
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                    'play_duration' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } catch (\Exception $e) {
                // 观看日志表可能不存在，忽略错误
                Log::info('观看日志记录失败（表可能不存在）', ['error' => $e->getMessage()]);
            }

            // 设置缓存，5分钟内不重复计数
            Cache::set($cacheKey, true, 300);

            // 清除视频详情缓存
            Cache::delete("video_detail_{$videoId}");

            return true;
        });
    }

    /**
     * 获取视频播放地址
     * 支持上传视频和采集视频两种类型
     */
    public function getVideoPlayUrls(int $videoId): array
    {
        try {
            // 从videos表获取基本信息
            $video = $this->databaseService->table('videos')
                ->where('id', $videoId)
                ->field('id,file_path,source_type,hls_url')
                ->find();

            if (!$video) {
                return [];
            }

            $playUrls = [];

            // 1. 检查video_play_urls表（采集视频的播放地址）
            $externalUrls = $this->databaseService->table('video_play_urls')
                ->where('video_id', $videoId)
                ->where('status', 'active')
                ->order('is_primary desc, quality desc')
                ->select();

            if (!empty($externalUrls)) {
                foreach ($externalUrls as $urlData) {
                    $playUrls[] = [
                        'source_name' => 'external_' . $urlData['quality'],
                        'play_url' => $urlData['url'],
                        'quality' => $urlData['quality'],
                        'format' => $urlData['format'],
                        'is_primary' => $urlData['is_primary']
                    ];
                }
                return $playUrls;
            }

            // 2. 上传视频：优先使用HLS地址
            if (!empty($video['hls_url'])) {
                // 构建完整的HLS URL
                $hlsUrl = $video['hls_url'];
                if (!str_starts_with($hlsUrl, 'http')) {
                    $apiBaseUrl = env('API_BASE_URL', 'http://localhost:3000');
                    $hlsUrl = $apiBaseUrl . $hlsUrl;
                }

                $playUrls[] = [
                    'source_name' => 'hls',
                    'play_url' => $hlsUrl,
                    'quality' => 'adaptive',
                    'format' => 'm3u8',
                    'is_primary' => true
                ];
            }

            // 3. 上传视频：使用原始文件路径作为备选
            if (!empty($video['file_path'])) {
                // 构建完整的视频文件URL
                $fileUrl = $video['file_path'];
                if (!str_starts_with($fileUrl, 'http')) {
                    $apiBaseUrl = env('API_BASE_URL', 'http://localhost:3000');
                    $fileUrl = $apiBaseUrl . $fileUrl;
                }

                $playUrls[] = [
                    'source_name' => 'local',
                    'play_url' => $fileUrl,
                    'quality' => 'original',
                    'format' => 'mp4',
                    'is_primary' => empty($video['hls_url'])
                ];
            }

            return $playUrls;

        } catch (\Exception $e) {
            Log::error('获取视频播放地址失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 触发视频处理
     */
    public function triggerVideoProcessing(int $videoId, string $filePath): bool
    {
        try {
            $jobData = [
                'video_id' => $videoId,
                'file_path' => $filePath,
                'options' => $this->processingConfig
            ];

            // 尝试提交到队列
            try {
                Queue::push(\app\job\VideoProcessJob::class, $jobData, 'video_processing');

                Log::info('视频处理任务已提交到队列', [
                    'video_id' => $videoId,
                    'file_path' => $filePath
                ]);

                return true;
            } catch (\Exception $queueError) {
                Log::warning('队列提交失败，尝试直接处理', [
                    'video_id' => $videoId,
                    'queue_error' => $queueError->getMessage()
                ]);

                // 队列失败时直接处理
                return $this->processVideoDirectly($videoId, $filePath);
            }
        } catch (\Exception $e) {
            Log::error('提交视频处理任务失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 直接处理视频（不使用队列）
     */
    private function processVideoDirectly(int $videoId, string $filePath): bool
    {
        try {
            Log::info('开始直接处理视频', [
                'video_id' => $videoId,
                'file_path' => $filePath
            ]);

            // 异步执行处理命令，避免阻塞用户请求
            $command = sprintf(
                'php %s/process_video_direct.php %d "%s" > /dev/null 2>&1 &',
                __DIR__ . '/../..',
                $videoId,
                $filePath
            );

            exec($command);

            Log::info('视频直接处理命令已启动', [
                'video_id' => $videoId,
                'command' => $command
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('直接处理视频失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 批量发布视频
     */
    public function batchPublishVideos(array $videoIds): array
    {
        $successCount = 0;
        $failedCount = 0;
        $errors = [];

        foreach ($videoIds as $videoId) {
            try {
                $result = $this->databaseService->writeTable('videos')
                    ->where('id', $videoId)
                    ->update([
                        'status' => 'published',
                        'audit_status' => 'approved',
                        'published_at' => date('Y-m-d H:i:s')
                    ]);

                if ($result) {
                    $successCount++;
                    // 清除缓存
                    Cache::delete("video_detail_{$videoId}");
                } else {
                    $failedCount++;
                    $errors[] = "视频ID {$videoId}: 更新失败";
                }
            } catch (\Exception $e) {
                $failedCount++;
                $errors[] = "视频ID {$videoId}: " . $e->getMessage();
            }
        }

        // 清除列表缓存
        $this->clearVideoCache();

        return [
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
    }

    /**
     * 批量处理视频
     */
    public function batchProcessVideos(array $videoIds): array
    {
        $successCount = 0;
        $failedCount = 0;
        $errors = [];

        foreach ($videoIds as $videoId) {
            try {
                $video = $this->databaseService->table('videos')
                    ->where('id', $videoId)
                    ->find();

                if (!$video) {
                    $failedCount++;
                    $errors[] = "视频ID {$videoId}: 视频不存在";
                    continue;
                }

                if (empty($video['file_path'])) {
                    $failedCount++;
                    $errors[] = "视频ID {$videoId}: 文件路径为空";
                    continue;
                }

                if ($this->triggerVideoProcessing($videoId, $video['file_path'])) {
                    $successCount++;
                } else {
                    $failedCount++;
                    $errors[] = "视频ID {$videoId}: 提交处理任务失败";
                }
            } catch (\Exception $e) {
                $failedCount++;
                $errors[] = "视频ID {$videoId}: " . $e->getMessage();
            }
        }

        return [
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
    }

    /**
     * 异步增加观看次数
     */
    private function incrementViewCountAsync(int $videoId): void
    {
        try {
            // 使用队列异步处理，避免阻塞响应
            Queue::push(\app\job\IncrementViewJob::class, ['video_id' => $videoId], 'view_increment');
        } catch (\Exception $e) {
            // 如果队列失败，直接更新
            Log::warning('异步增加观看次数失败，使用同步方式', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);

            $this->databaseService->writeTable('videos')
                ->where('id', $videoId)
                ->inc('view_count')
                ->update();
        }
    }

    /**
     * 验证分类是否存在
     */
    private function validateCategoryExists(int $categoryId): bool
    {
        return $this->databaseService->table('video_categories')
            ->where('id', $categoryId)
            ->where('status', 'active')
            ->count() > 0;
    }

    /**
     * 构建视频数据
     */
    private function buildVideoData(array $data, int $userId): array
    {
        return [
            'title' => $data['title'],
            'description' => $data['description'] ?? '',
            'category_id' => $data['category_id'],
            'user_id' => $userId,
            'file_path' => $data['file_path'] ?? '',
            'cover_image' => $data['cover_image'] ?? '',
            'duration' => $data['duration'] ?? 0,
            'video_type' => $data['video_type'] ?? 'upload',
            'is_vip' => $data['is_vip'] ?? 0,
            'status' => 'published',
            'audit_status' => 'pending',
            'view_count' => 0,
            'like_count' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 删除视频相关数据
     */
    private function deleteVideoRelatedData(int $videoId): void
    {
        try {
            // 删除收藏记录
            $this->databaseService->writeTable('video_collections')
                ->where('video_id', $videoId)
                ->delete();

            // 删除评论
            $this->databaseService->writeTable('comments')
                ->where('video_id', $videoId)
                ->delete();

            // 删除播放记录
            $this->databaseService->writeTable('video_play_logs')
                ->where('video_id', $videoId)
                ->delete();

        } catch (\Exception $e) {
            Log::warning('删除视频相关数据失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 解析播放地址
     */
    private function parsePlayUrls(string $playUrlData): array
    {
        $urls = [];

        try {
            // 解析播放地址格式：source1$url1#source2$url2
            $sources = explode('#', $playUrlData);

            foreach ($sources as $source) {
                if (strpos($source, '$') !== false) {
                    list($sourceName, $playUrl) = explode('$', $source, 2);

                    if (!empty($playUrl)) {
                        $urls[] = [
                            'source_name' => trim($sourceName),
                            'play_url' => trim($playUrl),
                            'quality' => $this->detectQuality($sourceName)
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('解析播放地址失败', [
                'play_url_data' => $playUrlData,
                'error' => $e->getMessage()
            ]);
        }

        return $urls;
    }

    /**
     * 检测视频质量
     */
    private function detectQuality(string $sourceName): string
    {
        $sourceName = strtolower($sourceName);

        if (strpos($sourceName, '1080') !== false || strpos($sourceName, 'hd') !== false) {
            return '1080p';
        } elseif (strpos($sourceName, '720') !== false) {
            return '720p';
        } elseif (strpos($sourceName, '480') !== false) {
            return '480p';
        } elseif (strpos($sourceName, 'm3u8') !== false) {
            return 'hls';
        }

        return 'standard';
    }

    /**
     * 清除视频相关缓存
     */
    private function clearVideoCache(): void
    {
        try {
            // 清除热门视频缓存
            $hotVideoKeys = Cache::tag('hot_videos')->clear();

            // 清除视频列表缓存
            $listKeys = Cache::tag('video_list')->clear();

            Log::info('视频缓存已清除');
        } catch (\Exception $e) {
            Log::warning('清除视频缓存失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 获取视频统计信息
     */
    public function getVideoStats(int $videoId): array
    {
        try {
            $stats = $this->databaseService->query("
                SELECT
                    v.view_count,
                    v.like_count,
                    v.created_at,
                    COUNT(DISTINCT c.id) as comment_count,
                    COUNT(DISTINCT vc.id) as collection_count
                FROM videos v
                LEFT JOIN comments c ON v.id = c.video_id
                LEFT JOIN video_collections vc ON v.id = vc.video_id
                WHERE v.id = ?
                GROUP BY v.id
            ", [$videoId]);

            return $stats[0] ?? [
                'view_count' => 0,
                'like_count' => 0,
                'comment_count' => 0,
                'collection_count' => 0,
                'created_at' => null
            ];
        } catch (\Exception $e) {
            Log::error('获取视频统计失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取推荐视频
     */
    public function getRecommendedVideos(int $videoId, int $limit = 10): array
    {
        try {
            // 获取当前视频的分类
            $currentVideo = $this->databaseService->table('videos')
                ->where('id', $videoId)
                ->field('category_id, user_id')
                ->find();

            if (!$currentVideo) {
                return [];
            }

            // 推荐同分类的热门视频
            $recommended = $this->databaseService->table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->field([
                    'v.id', 'v.title', 'v.cover_image', 'v.duration',
                    'v.view_count', 'v.like_count', 'v.created_at',
                    'u.username', 'u.nickname'
                ])
                ->where([
                    ['v.status', '=', 'published'],
                    ['v.audit_status', '=', 'approved'],
                    ['v.category_id', '=', $currentVideo['category_id']],
                    ['v.id', '<>', $videoId]
                ])
                ->order('v.view_count desc, v.created_at desc')
                ->limit($limit)
                ->select()
                ->toArray();

            return $recommended;
        } catch (\Exception $e) {
            Log::error('获取推荐视频失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 处理视频广告信息
     *
     * @param array $video 视频数据
     * @return array 处理后的视频数据
     */
    private function processAdInfo(array $video): array
    {
        // 解析广告段信息
        if (!empty($video['ad_segments'])) {
            $video['ad_segments'] = json_decode($video['ad_segments'], true) ?: [];
        } else {
            $video['ad_segments'] = [];
        }

        // 添加广告相关的便捷字段
        $video['has_ads'] = (bool)($video['has_ads'] ?? false);
        $video['ad_confidence_level'] = $this->getAdConfidenceLevel($video['ad_confidence_score'] ?? 0);
        $video['ad_detection_status_text'] = $this->getAdDetectionStatusText($video['ad_detection_status'] ?? 'pending');

        // 如果有广告段，计算总广告时长
        if (!empty($video['ad_segments'])) {
            $totalAdDuration = 0;
            foreach ($video['ad_segments'] as $segment) {
                $totalAdDuration += ($segment['end'] ?? 0) - ($segment['start'] ?? 0);
            }
            $video['total_ad_duration'] = $totalAdDuration;
        } else {
            $video['total_ad_duration'] = 0;
        }

        return $video;
    }

    /**
     * 获取广告检测置信度等级
     */
    private function getAdConfidenceLevel(float $score): string
    {
        if ($score >= 0.8) return 'high';
        if ($score >= 0.5) return 'medium';
        if ($score >= 0.3) return 'low';
        return 'none';
    }

    /**
     * 获取广告检测状态文本
     */
    private function getAdDetectionStatusText(string $status): string
    {
        $statusMap = [
            'pending' => '待检测',
            'detected' => '已检测',
            'manual' => '手动标记',
            'skipped' => '已跳过'
        ];

        return $statusMap[$status] ?? '未知';
    }
}
