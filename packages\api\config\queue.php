<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * 队列配置
 * 
 * 用于配置视频处理等异步任务的队列系统
 */
return [
    // 默认队列连接 - 使用Redis队列（高性能）
    'default' => 'redis',

    // 队列连接配置
    'connections' => [
        // 数据库队列驱动（主要用于视频处理）
        'database' => [
            'type' => 'database',
            'queue' => 'video_processing',
            'table' => 'jobs',
            'connection' => null,
        ],

        // Redis队列驱动（主要用于视频处理）
        'redis' => [
            'type' => 'redis',
            'queue' => 'video_processing',
            'host' => 'redis', // Docker容器名
            'port' => 6379,
            'password' => '',
            'select' => 0,
            'timeout' => 0,
            'persistent' => false,
        ],

        // 同步队列驱动（用于测试）
        'sync' => [
            'type' => 'sync'
        ],
    ],

    // 失败任务配置
    'failed' => [
        'type' => 'database',
        'table' => 'failed_jobs',
    ],

    // 队列工作进程配置
    'worker' => [
        // 最大执行时间（秒）
        'timeout' => 60,
        // 内存限制（MB）
        'memory' => 128,
        // 休眠时间（秒）
        'sleep' => 3,
        // 最大尝试次数
        'tries' => 3,
    ],
];
