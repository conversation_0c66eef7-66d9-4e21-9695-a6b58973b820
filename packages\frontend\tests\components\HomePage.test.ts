/**
 * 🧪 首页组件测试
 * 
 * 测试首页的所有功能和交互
 * 
 * <AUTHOR> Video Platform Team
 * @version 2.0.0
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import HomePage from '@/views/HomePage.vue'
import { createMockUser, createMockApiResponse, waitFor } from '../setup'

// 创建测试路由
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: HomePage },
    { path: '/short-videos', component: { template: '<div>Short Videos</div>' } },
    { path: '/long-videos', component: { template: '<div>Long Videos</div>' } }
  ]
})

describe('HomePage', () => {
  let wrapper: any

  beforeEach(async () => {
    // 重置环境变量
    import.meta.env.VITE_EXTERNAL_LINK_1 = 'https://example1.com'
    import.meta.env.VITE_EXTERNAL_LINK_2 = 'https://example2.com'
    import.meta.env.VITE_EXTERNAL_LINK_3 = 'https://example3.com'

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await router.isReady()
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  describe('🎨 渲染测试', () => {
    it('应该正确渲染主要元素', () => {
      // 检查主标题
      expect(wrapper.find('.main-title').text()).toBe('51吃瓜网')
      
      // 检查副标题
      expect(wrapper.find('.subtitle').text()).toBe('专业视频平台 · 精彩内容聚集地')
      
      // 检查品牌统计
      expect(wrapper.find('.brand-stats').exists()).toBe(true)
      expect(wrapper.findAll('.stat-item')).toHaveLength(3)
    })

    it('应该渲染导航卡片', () => {
      const navCards = wrapper.findAll('.nav-card')
      expect(navCards).toHaveLength(2)
      
      // 检查短视频卡片
      const shortVideoCard = navCards[0]
      expect(shortVideoCard.find('.nav-title').text()).toBe('短视频')
      expect(shortVideoCard.find('.nav-description').text()).toBe('精彩短视频内容，快速浏览')
      
      // 检查长视频卡片
      const longVideoCard = navCards[1]
      expect(longVideoCard.find('.nav-title').text()).toBe('长视频')
      expect(longVideoCard.find('.nav-description').text()).toBe('高质量长视频内容，深度观看')
    })

    it('应该渲染特色功能', () => {
      const features = wrapper.findAll('.feature-item')
      expect(features).toHaveLength(3)
      
      expect(features[0].find('.feature-title').text()).toBe('高清画质')
      expect(features[1].find('.feature-title').text()).toBe('流畅播放')
      expect(features[2].find('.feature-title').text()).toBe('多端同步')
    })

    it('应该根据环境变量显示外部链接', () => {
      const externalLinks = wrapper.findAll('.external-link')
      expect(externalLinks).toHaveLength(3)
      
      expect(externalLinks[0].find('.external-title').text()).toBe('51吃瓜 线路一')
      expect(externalLinks[1].find('.external-title').text()).toBe('51吃瓜 线路二')
      expect(externalLinks[2].find('.external-title').text()).toBe('51吃瓜 线路三')
    })
  })

  describe('🖱️ 交互测试', () => {
    it('点击短视频卡片应该导航到短视频页面', async () => {
      const routerPushSpy = vi.spyOn(router, 'push')
      
      const shortVideoCard = wrapper.findAll('.nav-card')[0]
      await shortVideoCard.trigger('click')
      
      expect(routerPushSpy).toHaveBeenCalledWith('/short-videos')
    })

    it('点击长视频卡片应该导航到长视频页面', async () => {
      const routerPushSpy = vi.spyOn(router, 'push')
      
      const longVideoCard = wrapper.findAll('.nav-card')[1]
      await longVideoCard.trigger('click')
      
      expect(routerPushSpy).toHaveBeenCalledWith('/long-videos')
    })

    it('点击外部链接应该打开新窗口', async () => {
      const windowOpenSpy = vi.spyOn(window, 'open').mockImplementation(() => null)
      
      const externalLink = wrapper.findAll('.external-link')[0]
      await externalLink.trigger('click')
      
      expect(windowOpenSpy).toHaveBeenCalledWith(
        'https://example1.com',
        '_blank',
        'noopener,noreferrer'
      )
      
      windowOpenSpy.mockRestore()
    })

    it('悬停导航卡片应该有视觉反馈', async () => {
      const navCard = wrapper.findAll('.nav-card')[0]
      
      await navCard.trigger('mouseenter')
      
      // 检查是否添加了悬停样式类或内联样式
      expect(navCard.element.style.transform).toBeTruthy()
    })
  })

  describe('📱 响应式测试', () => {
    it('在移动设备上应该正确显示', async () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      })
      
      // 触发resize事件
      window.dispatchEvent(new Event('resize'))
      
      await waitFor(100)
      
      // 检查移动端布局
      const navGrid = wrapper.find('.nav-grid')
      expect(navGrid.exists()).toBe(true)
      
      // 在移动端，导航卡片应该是单列布局
      const computedStyle = window.getComputedStyle(navGrid.element)
      expect(computedStyle.gridTemplateColumns).toBe('1fr')
    })

    it('在平板设备上应该正确显示', async () => {
      // 模拟平板设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      })
      
      window.dispatchEvent(new Event('resize'))
      await waitFor(100)
      
      // 检查平板端布局
      const brandStats = wrapper.find('.brand-stats')
      expect(brandStats.exists()).toBe(true)
    })
  })

  describe('🎭 动画测试', () => {
    it('应该有正确的动画类', () => {
      expect(wrapper.find('.fade-in').exists()).toBe(true)
      expect(wrapper.find('.slide-in-left').exists()).toBe(true)
      expect(wrapper.find('.scale-in').exists()).toBe(true)
    })

    it('应该有背景动画元素', () => {
      expect(wrapper.find('.gradient-orbs').exists()).toBe(true)
      expect(wrapper.find('.grid-pattern').exists()).toBe(true)
      expect(wrapper.find('.floating-particles').exists()).toBe(true)
      
      const orbs = wrapper.findAll('.orb')
      expect(orbs).toHaveLength(3)
      
      const particles = wrapper.findAll('.particle')
      expect(particles).toHaveLength(6)
    })
  })

  describe('🔧 性能测试', () => {
    it('应该记录性能指标', async () => {
      const { advancedPerformanceMonitor } = await import('@/utils/advanced-performance')
      
      // 模拟用户交互
      const shortVideoCard = wrapper.findAll('.nav-card')[0]
      await shortVideoCard.trigger('click')
      
      expect(advancedPerformanceMonitor.startMeasure).toHaveBeenCalledWith('internal-navigation')
      expect(advancedPerformanceMonitor.endMeasure).toHaveBeenCalledWith('internal-navigation')
    })

    it('应该预加载关键路由', async () => {
      // 检查是否调用了路由预解析
      expect(router.resolve('/short-videos')).toBeTruthy()
      expect(router.resolve('/long-videos')).toBeTruthy()
    })
  })

  describe('🌐 无障碍测试', () => {
    it('应该有正确的语义化标签', () => {
      expect(wrapper.find('h1').exists()).toBe(true)
      expect(wrapper.find('h2').exists()).toBe(true)
      expect(wrapper.find('h3').exists()).toBe(true)
      expect(wrapper.find('h4').exists()).toBe(true)
    })

    it('应该有正确的ARIA属性', () => {
      const navCards = wrapper.findAll('.nav-card')
      navCards.forEach(card => {
        expect(card.attributes('role')).toBe('button')
        expect(card.attributes('tabindex')).toBe('0')
      })
    })

    it('应该支持键盘导航', async () => {
      const navCard = wrapper.findAll('.nav-card')[0]
      
      // 模拟Tab键聚焦
      await navCard.trigger('focus')
      expect(document.activeElement).toBe(navCard.element)
      
      // 模拟Enter键激活
      await navCard.trigger('keydown', { key: 'Enter' })
      expect(router.push).toHaveBeenCalledWith('/short-videos')
    })
  })

  describe('🔒 安全测试', () => {
    it('外部链接应该有安全属性', () => {
      const windowOpenSpy = vi.spyOn(window, 'open').mockImplementation(() => null)
      
      const externalLink = wrapper.findAll('.external-link')[0]
      externalLink.trigger('click')
      
      expect(windowOpenSpy).toHaveBeenCalledWith(
        expect.any(String),
        '_blank',
        'noopener,noreferrer'
      )
      
      windowOpenSpy.mockRestore()
    })

    it('应该正确处理无效的外部链接', async () => {
      // 设置无效链接
      import.meta.env.VITE_EXTERNAL_LINK_1 = '#'
      
      const consoleSpy = vi.spyOn(console, 'log')
      
      const externalLink = wrapper.findAll('.external-link')[0]
      await externalLink.trigger('click')
      
      expect(consoleSpy).toHaveBeenCalledWith('链接未配置: 51吃瓜 线路一')
      
      consoleSpy.mockRestore()
    })
  })

  describe('🐛 错误处理测试', () => {
    it('应该优雅处理路由错误', async () => {
      const routerPushSpy = vi.spyOn(router, 'push').mockRejectedValue(new Error('Navigation failed'))
      
      const shortVideoCard = wrapper.findAll('.nav-card')[0]
      await shortVideoCard.trigger('click')
      
      // 应该不会抛出未捕获的错误
      expect(routerPushSpy).toHaveBeenCalled()
      
      routerPushSpy.mockRestore()
    })

    it('应该处理性能监控错误', async () => {
      const { advancedPerformanceMonitor } = await import('@/utils/advanced-performance')
      
      // 模拟性能监控错误
      vi.mocked(advancedPerformanceMonitor.startMeasure).mockImplementation(() => {
        throw new Error('Performance monitoring failed')
      })
      
      const shortVideoCard = wrapper.findAll('.nav-card')[0]
      
      // 应该不会因为性能监控错误而崩溃
      expect(async () => {
        await shortVideoCard.trigger('click')
      }).not.toThrow()
    })
  })
})
