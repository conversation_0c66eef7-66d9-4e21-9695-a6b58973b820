<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放测试</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #000;
            color: #fff;
        }
        .video-container {
            max-width: 400px;
            margin: 20px auto;
        }
        video {
            width: 100%;
            height: auto;
            background: #000;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>视频播放测试</h1>
    
    <div class="video-container">
        <video id="video" controls poster="http://localhost:3000/uploads/thumbnails/thumbnail_7.jpg">
            您的浏览器不支持视频播放。
        </video>
        
        <div class="controls">
            <button onclick="testHLS()">测试HLS播放</button>
            <button onclick="testMP4()">测试MP4播放</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>
    
    <div id="log" class="log">
        <div>等待测试...</div>
    </div>

    <script>
        const video = document.getElementById('video');
        const logDiv = document.getElementById('log');
        
        function log(message) {
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logDiv.innerHTML = '<div>日志已清除</div>';
        }
        
        function testHLS() {
            log('开始测试HLS播放...');
            const hlsUrl = 'http://localhost:3000/hls/video_7/playlist.m3u8';
            
            if (Hls.isSupported()) {
                log('浏览器支持HLS.js');
                const hls = new Hls();
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    log('HLS manifest解析成功');
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    log(`HLS错误: ${data.type} - ${data.details}`);
                    if (data.fatal) {
                        log('致命错误，停止播放');
                    }
                });
                
                hls.loadSource(hlsUrl);
                hls.attachMedia(video);
                log(`加载HLS源: ${hlsUrl}`);
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                log('使用原生HLS支持');
                video.src = hlsUrl;
            } else {
                log('浏览器不支持HLS播放');
            }
        }
        
        function testMP4() {
            log('开始测试MP4播放...');
            const mp4Url = 'http://localhost:3000/storage/video/0/video_2025-07-27_13-10-26_1753750758_1759e0495d09d1c2.mp4';
            video.src = mp4Url;
            log(`加载MP4源: ${mp4Url}`);
        }
        
        // 视频事件监听
        video.addEventListener('loadstart', () => log('开始加载视频'));
        video.addEventListener('loadedmetadata', () => log('视频元数据加载完成'));
        video.addEventListener('loadeddata', () => log('视频数据加载完成'));
        video.addEventListener('canplay', () => log('视频可以开始播放'));
        video.addEventListener('canplaythrough', () => log('视频可以流畅播放'));
        video.addEventListener('play', () => log('视频开始播放'));
        video.addEventListener('pause', () => log('视频暂停'));
        video.addEventListener('ended', () => log('视频播放结束'));
        video.addEventListener('error', (e) => {
            log(`视频错误: ${e.target.error ? e.target.error.message : '未知错误'}`);
        });
        
        log('测试页面加载完成');
    </script>
</body>
</html>
