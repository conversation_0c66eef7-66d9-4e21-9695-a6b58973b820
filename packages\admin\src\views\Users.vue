<template>
  <AdminLayout>
    <div class="page-container">
      <div class="users-page">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h2>用户管理</h2>
          <p>管理平台上的所有用户</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加用户
          </el-button>
        </div>
      </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名/邮箱/昵称"
            clearable
            @change="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="状态" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="正常" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.level" placeholder="等级" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="普通用户" value="1" />
            <el-option label="VIP用户" value="2" />
            <el-option label="超级VIP" value="3" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="注册开始日期"
            end-placeholder="注册结束日期"
            @change="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="exportUsers">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-table
        :data="userList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :row-style="{ height: '80px' }"
        :cell-style="{ padding: '16px 12px' }"
      >
        <el-table-column type="selection" width="60" />

        <el-table-column label="用户信息" width="320" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="45" :src="row.avatar" style="margin-right: 15px;">
                {{ row.nickname?.charAt(0) || row.username?.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <h4 class="user-name" style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600;">
                  {{ row.nickname || row.username }}
                </h4>
                <p class="user-email" style="margin: 0 0 6px 0; color: #666; font-size: 12px;">
                  {{ row.email }}
                </p>
                <div class="user-meta">
                  <el-tag size="small" :type="row.level > 1 ? 'warning' : 'info'">
                    {{ getLevelText(row.level) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="用户名" prop="username" width="140" show-overflow-tooltip />

        <el-table-column label="星币" width="120" align="center">
          <template #default="{ row }">
            <span class="points" style="font-weight: 600; color: #f39c12; font-size: 14px;">
              {{ row.points || 0 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="default">
              {{ row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="最后登录" width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <span style="color: #666; font-size: 13px;">
              {{ row.last_login_time ? formatDate(row.last_login_time) : '从未登录' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="注册时间" width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <span style="color: #666; font-size: 13px;">
              {{ formatDate(row.created_at) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="260" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons" style="display: flex; gap: 8px; justify-content: center;">
              <el-button size="small" @click="viewUser(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button size="small" type="primary" @click="editUser(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                size="small"
                :type="row.status === 1 ? 'warning' : 'success'"
                @click="toggleUserStatus(row)"
              >
                {{ row.status === 1 ? '禁用' : '启用' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加用户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加用户"
      width="500px"
    >
      <el-form :model="userForm" :rules="userRules" ref="userFormRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="userForm.nickname" placeholder="请输入昵称" />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>

        <el-form-item label="用户等级" prop="level">
          <el-select v-model="userForm.level" placeholder="请选择等级">
            <el-option label="普通用户" :value="1" />
            <el-option label="VIP用户" :value="2" />
            <el-option label="超级VIP" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="星币" prop="points">
          <el-input-number v-model="userForm.points" :min="0" :max="999999" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddUser" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑用户"
      width="500px"
    >
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" placeholder="请输入昵称" />
        </el-form-item>

        <el-form-item label="用户等级" prop="level">
          <el-select v-model="editForm.level" placeholder="请选择等级">
            <el-option label="普通用户" :value="1" />
            <el-option label="VIP用户" :value="2" />
            <el-option label="超级VIP" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="星币" prop="points">
          <el-input-number v-model="editForm.points" :min="0" :max="999999" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleEditUser" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看用户对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="用户详情"
      width="600px"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentUser.email }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickname }}</el-descriptions-item>
          <el-descriptions-item label="等级">
            <el-tag :type="currentUser.level === 1 ? 'info' : currentUser.level === 2 ? 'warning' : 'success'">
              {{ currentUser.level === 1 ? '普通用户' : currentUser.level === 2 ? 'VIP用户' : '超级VIP' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="星币">{{ currentUser.points || 0 }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentUser.status === 1 ? 'success' : 'danger'">
              {{ currentUser.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="视频数量">{{ currentUser.video_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="关注数">{{ currentUser.following_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="粉丝数">{{ currentUser.follower_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ currentUser.last_login_time ? formatDate(currentUser.last_login_time) : '从未登录' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatDate(currentUser.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showViewDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, View, Edit, Download } from '@element-plus/icons-vue'
import AdminLayout from '@/components/AdminLayout.vue'
import { api } from '@/api'
import { API_PATHS } from '@/config'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showViewDialog = ref(false)
const userList = ref([])
const currentUser = ref(null)

// 表单引用
const userFormRef = ref()
const editFormRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  level: '',
  dateRange: null
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 用户表单
const userForm = reactive({
  username: '',
  email: '',
  nickname: '',
  password: '',
  level: 1,
  points: 100
})

// 编辑表单
const editForm = reactive({
  id: null,
  username: '',
  email: '',
  nickname: '',
  level: 1,
  points: 0,
  status: 1
})

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择用户等级', trigger: 'change' }
  ]
}

// 编辑表单验证规则
const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择用户等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择用户状态', trigger: 'change' }
  ]
}

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword,
      level: searchForm.level,
      registration_start: searchForm.registrationStart,
      registration_end: searchForm.registrationEnd
    }

    // 只有在有值的时候才添加status参数
    if (searchForm.status !== '') {
      params.status = searchForm.status
    }

    const response = await api.get(API_PATHS.USERS.LIST, { params })
    console.log('API响应:', response.data) // 调试日志
    userList.value = response.data.users || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('加载用户列表失败:', error)
    userList.value = []
    pagination.total = 0
    ElMessage.error('加载用户列表失败，请检查网络连接或联系管理员')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadUsers()
}

// 添加用户
const handleAddUser = async () => {
  submitting.value = true
  try {
    await api.post(API_PATHS.USERS.CREATE, userForm)
    ElMessage.success('用户添加成功')
    showAddDialog.value = false
    resetUserForm()
    loadUsers()
  } catch (error) {
    ElMessage.error('添加用户失败')
  } finally {
    submitting.value = false
  }
}

// 重置用户表单
const resetUserForm = () => {
  Object.assign(userForm, {
    username: '',
    email: '',
    nickname: '',
    password: '',
    level: 1,
    points: 100
  })
}

// 查看用户
const viewUser = (user: any) => {
  currentUser.value = user
  showViewDialog.value = true
}

// 编辑用户
const editUser = (user: any) => {
  // 填充编辑表单
  Object.assign(editForm, {
    id: user.id,
    username: user.username,
    email: user.email,
    nickname: user.nickname,
    level: user.level,
    points: user.points || 0,
    status: user.status
  })
  showEditDialog.value = true
}

// 切换用户状态
const toggleUserStatus = (user: any) => {
  const action = user.status === 1 ? '禁用' : '启用'
  ElMessageBox.confirm(
    `确定要${action}用户 "${user.nickname}" 吗？`,
    `确认${action}`,
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    user.status = user.status === 1 ? 0 : 1
    ElMessage.success(`${action}成功`)
  }).catch(() => {
    ElMessage.info(`已取消${action}`)
  })
}

// 导出用户数据
const exportUsers = () => {
  ElMessage.info('导出功能开发中...')
}

// 工具函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getLevelText = (level: number) => {
  const texts: Record<number, string> = {
    1: '普通用户',
    2: 'VIP用户',
    3: '超级VIP'
  }
  return texts[level] || '普通用户'
}



// 处理编辑用户
const handleEditUser = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    submitting.value = true

    const { id, ...updateData } = editForm
    const response = await api.put(`${API_PATHS.USERS.UPDATE}/${id}`, updateData)

    if (response.success) {
      ElMessage.success('用户更新成功')
      showEditDialog.value = false
      loadUsers()
    } else {
      ElMessage.error(response.message || '更新用户失败')
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    ElMessage.error('更新用户失败')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.page-container {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  min-height: 100vh;
}

.users-page {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 24px;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-section {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.table-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.user-email {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
}

.user-meta {
  margin-top: 4px;
}

.points {
  font-weight: 600;
  color: #f39c12;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.pagination {
  padding: 24px 20px;
  display: flex;
  justify-content: center;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-top: var(--spacing-lg);
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  font-size: 13px;
  padding: 16px 12px;
}

:deep(.el-table td) {
  padding: 16px 12px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9ff;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background-color: #f0f0ff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .users-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .search-section .el-row {
    flex-direction: column;
  }

  .search-section .el-col {
    width: 100% !important;
    margin-bottom: 10px;
  }
}
</style>
