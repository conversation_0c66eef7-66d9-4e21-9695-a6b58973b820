<?php
declare (strict_types = 1);

namespace app\controller\admin;

use think\Request;
use think\Response;
use app\model\SystemConfig;
use app\service\ResponseService;
use app\service\ValidationService;

/**
 * 系统配置管理控制器
 */
class Config
{
    
    protected $responseService;
    protected $validationService;
    
    public function __construct()
    {
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
    }
    /**
     * 获取所有配置
     *
     * @return \think\Response
     */
    public function index(): Response
    {
        try {
            $configs = SystemConfig::select();

            // 转换为键值对格式
            $result = [];
            foreach ($configs as $config) {
                // 兼容不同的字段名
                $key = $config->config_key ?? $config->key;
                $value = $config->config_value ?? $config->value;
                $type = $this->inferConfigType($key);

                $result[$key] = $this->parseConfigValue($value, $type);
            }

            return $this->responseService->success($result);
        } catch (\Exception $e) {
            return $this->responseService->error('获取配置失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取网站设置
     *
     * @return \think\Response
     */
    public function website(): Response
    {
        try {
            $configs = SystemConfig::where('group', 'website')->select();

            // 转换为键值对格式
            $settings = [];
            foreach ($configs as $config) {
                // 兼容不同的字段名
                $key = $config->config_key ?? $config->key;
                $value = $config->config_value ?? $config->value;
                $type = $this->inferConfigType($key);

                $settings[$key] = $this->parseConfigValue($value, $type);
            }

            // 设置默认值
            $defaultSettings = [
                'site_title' => '视频平台',
                'site_keywords' => '视频,在线观看,视频分享',
                'site_description' => '专业的视频分享平台',
                'site_logo' => '',
                'site_favicon' => '',
                'site_copyright' => '© 2025 视频平台. All rights reserved.',
                'site_icp' => '',
                'allow_register' => true,
                'allow_guest_view' => true,
                'email_verification' => false,
                'phone_verification' => false,
                'max_file_size' => 100,
                'max_video_size' => 500,
                'allowed_video_formats' => ['mp4', 'avi', 'mov'],
                'allowed_image_formats' => ['jpg', 'jpeg', 'png', 'gif'],
                'guest_view_limit' => 10,
                'default_user_level' => 1,
                'default_user_points' => 100,
                'video_auto_audit' => false,
                'video_watermark' => false,
                'video_download_allowed' => true,
                'payment_enabled' => false,
                'points_exchange_rate' => 100,
                'storage_type' => 'local',
                'storage_config' => []
            ];

            $settings = array_merge($defaultSettings, $settings);

            return $this->responseService->success($settings, 'success');
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取网站设置失败：' . $e->getMessage(),
                'data' => null,
                'timestamp' => time()
            ]);
        }
    }

    /**
     * 更新网站设置
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function updateWebsite(Request $request): Response
    {
        try {
            $data = $request->post();

            // 配置项定义
            $configDefs = [
                'site_title' => ['type' => 'string', 'group' => 'website', 'title' => '网站标题'],
                'site_keywords' => ['type' => 'string', 'group' => 'website', 'title' => '网站关键词'],
                'site_description' => ['type' => 'string', 'group' => 'website', 'title' => '网站描述'],
                'site_logo' => ['type' => 'string', 'group' => 'website', 'title' => '网站Logo'],
                'site_favicon' => ['type' => 'string', 'group' => 'website', 'title' => '网站图标'],
                'site_copyright' => ['type' => 'string', 'group' => 'website', 'title' => '版权信息'],
                'site_icp' => ['type' => 'string', 'group' => 'website', 'title' => 'ICP备案号'],
                'allow_register' => ['type' => 'boolean', 'group' => 'website', 'title' => '允许注册'],
                'allow_guest_view' => ['type' => 'boolean', 'group' => 'website', 'title' => '游客观看'],
                'email_verification' => ['type' => 'boolean', 'group' => 'website', 'title' => '邮箱验证'],
                'phone_verification' => ['type' => 'boolean', 'group' => 'website', 'title' => '手机验证'],
                'max_file_size' => ['type' => 'number', 'group' => 'upload', 'title' => '最大文件大小'],
                'max_video_size' => ['type' => 'number', 'group' => 'upload', 'title' => '最大视频大小'],
                'allowed_video_formats' => ['type' => 'json', 'group' => 'upload', 'title' => '允许的视频格式'],
                'allowed_image_formats' => ['type' => 'json', 'group' => 'upload', 'title' => '允许的图片格式'],
                'guest_view_limit' => ['type' => 'number', 'group' => 'user', 'title' => '游客观看限制'],
                'default_user_level' => ['type' => 'number', 'group' => 'user', 'title' => '默认用户等级'],
                'default_user_points' => ['type' => 'number', 'group' => 'user', 'title' => '默认用户积分'],
                'video_auto_audit' => ['type' => 'boolean', 'group' => 'video', 'title' => '视频自动审核'],
                'video_watermark' => ['type' => 'boolean', 'group' => 'video', 'title' => '视频水印'],
                'video_download_allowed' => ['type' => 'boolean', 'group' => 'video', 'title' => '允许下载'],
                'payment_enabled' => ['type' => 'boolean', 'group' => 'payment', 'title' => '支付功能'],
                'points_exchange_rate' => ['type' => 'number', 'group' => 'payment', 'title' => '积分兑换比例'],
                'storage_type' => ['type' => 'string', 'group' => 'storage', 'title' => '存储类型'],
                'storage_config' => ['type' => 'json', 'group' => 'storage', 'title' => '存储配置']
            ];

            // 更新配置
            foreach ($data as $key => $value) {
                if (isset($configDefs[$key])) {
                    $def = $configDefs[$key];
                    $config = SystemConfig::where('config_key', $key)->find();

                    if ($config) {
                        $config->config_value = $value;
                        $config->save();
                    } else {
                        SystemConfig::create([
                            'key' => $key,
                            'value' => $value,
                            'type' => $def['type'],
                            'group' => $def['group'],
                            'title' => $def['title'],
                            'sort_order' => 0
                        ]);
                    }
                }
            }

            return $this->responseService->success(null, '设置保存成功');
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '保存设置失败：' . $e->getMessage(),
                'data' => null,
                'timestamp' => time()
            ]);
        }
    }

    /**
     * 获取指定配置项
     *
     * @param  string  $key
     * @return \think\Response
     */
    public function read($key): Response
    {
        try {
            $config = SystemConfig::where('config_key', $key)->find();

            if (!$config) {
                return $this->responseService->error('配置项不存在', 404);
            }

            return $this->responseService->success($config
            , 'success');
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新指定配置项
     *
     * @param  \think\Request  $request
     * @param  string  $key
     * @return \think\Response
     */
    public function update(Request $request, $key): Response
    {
        try {
            $data = $request->put();

            $config = SystemConfig::where('config_key', $key)->find();
            if (!$config) {
                return $this->responseService->error('配置项不存在', 404);
            }

            $config->config_value = $data['value'] ?? $config->config_value;
            $config->save();

            return $this->responseService->success($config
            , '配置更新成功');
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新配置失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 批量更新配置
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function batchUpdate(Request $request): Response
    {
        try {
            $data = $request->put();

            foreach ($data as $key => $value) {
                // 处理不同类型的值
                $processedValue = $this->processConfigValue($value);
                $configType = $this->getConfigType($value);

                $config = SystemConfig::where('config_key', $key)->find();
                if ($config) {
                    $config->config_value = $processedValue;
                    $config->save();
                } else {
                    // 如果配置项不存在，创建新的配置项
                    SystemConfig::create([
                        'config_key' => $key,
                        'config_value' => $processedValue,
                        'description' => "自动创建的配置项: {$key}"
                    ]);
                }
            }

            return $this->responseService->success(null
            , '批量更新成功');
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '批量更新失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 处理配置值，将不同类型的值转换为字符串
     *
     * @param mixed $value
     * @return string
     */
    private function processConfigValue($value): string
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        } elseif (is_bool($value)) {
            return $value ? 'true' : 'false';
        } elseif (is_null($value)) {
            return '';
        } elseif (is_numeric($value)) {
            return (string) $value;
        } else {
            return (string) $value;
        }
    }

    /**
     * 获取配置值的类型
     *
     * @param mixed $value
     * @return string
     */
    private function getConfigType($value): string
    {
        if (is_array($value)) {
            return 'json';
        } elseif (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value) || is_float($value)) {
            return 'number';
        } elseif (is_numeric($value)) {
            return 'number';
        } else {
            return 'string';
        }
    }

    /**
     * 解析配置值，根据类型转换为正确的数据类型
     *
     * @param string $value
     * @param string $type
     * @return mixed
     */
    private function parseConfigValue($value, string $type = 'string')
    {
        // 如果值为空，返回默认值
        if ($value === null || $value === '') {
            switch ($type) {
                case 'boolean':
                    return false;
                case 'number':
                    return 0;
                case 'json':
                    return [];
                default:
                    return '';
            }
        }

        switch ($type) {
            case 'boolean':
                // 处理布尔值
                if (is_bool($value)) {
                    return $value;
                }
                return in_array(strtolower((string)$value), ['true', '1', 'yes', 'on']);
            case 'number':
            case 'integer':
                return is_numeric($value) ? (int)$value : 0;
            case 'json':
                if (is_array($value)) {
                    return $value;
                }
                $decoded = json_decode($value, true);
                return $decoded !== null ? $decoded : [];
            default:
                return (string)$value;
        }
    }

    /**
     * 重置配置到默认值
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function reset(Request $request): Response
    {
        try {
            $data = $request->post();
            $keys = $data['keys'] ?? [];

            // 这里可以定义默认配置值
            $defaultConfigs = [
                'site_title' => '视频平台',
                'site_keywords' => '视频,在线观看,视频分享',
                'site_description' => '专业的视频分享平台',
                'allow_register' => 'true',
                'allow_guest_view' => 'true',
                'guest_view_limit' => '10'
            ];

            foreach ($keys as $key) {
                if (isset($defaultConfigs[$key])) {
                    $config = SystemConfig::where('config_key', $key)->find();
                    if ($config) {
                        $config->config_value = $defaultConfigs[$key];
                        $config->save();
                    }
                }
            }

            return $this->responseService->success(null
            , '重置成功');
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '重置失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 清除配置缓存
     *
     * @return \think\Response
     */
    public function clearCache(): Response
    {
        try {
            // 这里可以清除配置相关的缓存
            // Cache::clear();

            return $this->responseService->success(null
            , '缓存清除成功');
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '清除缓存失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取播放器配置
     *
     * @return \think\Response
     */
    public function player(): Response
    {
        try {
            // 获取播放器相关配置
            $playerKeys = [
                'skip_ad_enabled',
                'ad_duration',
                'show_skip_button',
                'skip_button_delay',
                'auto_skip_enabled',
                'smart_ad_detection',
                'ad_keywords',
                'max_ad_duration',
                'player_theme',
                'player_volume',
                'player_autoplay',
                'player_loop',
                'player_speed_control',
                'player_quality_control',
                'player_fullscreen',
                'player_pip'
            ];

            $configs = SystemConfig::whereIn('config_key', $playerKeys)->select();

            // 转换为键值对格式
            $settings = [];
            foreach ($configs as $config) {
                $key = $config->config_key ?? $config->key;
                $value = $config->config_value ?? $config->value;
                $type = $this->inferConfigType($key);

                $settings[$key] = $this->parseConfigValue($value, $type);
            }

            // 设置默认值
            $defaultSettings = [
                'skip_ad_enabled' => true,
                'ad_duration' => 17,
                'show_skip_button' => true,
                'skip_button_delay' => 3,
                'auto_skip_enabled' => true,
                'smart_ad_detection' => true,
                'ad_keywords' => '澳门,新葡京,娱乐城,赌场,博彩,彩票,贷款,投资,理财',
                'max_ad_duration' => 30,
                'ad_detection_confidence_threshold' => 0.3,
                'auto_detect_collected_videos' => true,
                'skip_detection_for_uploaded' => true,
                'ad_url_patterns' => 'googleads.g.doubleclick.net,googlesyndication.com,tanx.com,alimama.com,/ads/,ad.mp4',
                'player_theme' => 'default',
                'player_volume' => 80,
                'player_autoplay' => false,
                'player_loop' => false,
                'player_speed_control' => true,
                'player_quality_control' => true,
                'player_fullscreen' => true,
                'player_pip' => true
            ];

            $settings = array_merge($defaultSettings, $settings);

            return $this->responseService->success($settings, 'success');
        } catch (\Exception $e) {
            return $this->responseService->error('获取播放器配置失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新播放器配置
     *
     * @param Request $request
     * @return \think\Response
     */
    public function updatePlayer(Request $request): Response
    {
        try {
            $data = $request->post();

            // 验证数据
            $rules = [
                'skip_ad_enabled' => 'boolean',
                'ad_duration' => 'integer|between:0,120',
                'show_skip_button' => 'boolean',
                'skip_button_delay' => 'integer|between:0,10',
                'auto_skip_enabled' => 'boolean',
                'smart_ad_detection' => 'boolean',
                'ad_keywords' => 'string',
                'max_ad_duration' => 'integer|between:10,120',
                'ad_detection_confidence_threshold' => 'float|between:0,1',
                'auto_detect_collected_videos' => 'boolean',
                'skip_detection_for_uploaded' => 'boolean',
                'ad_url_patterns' => 'string',
                'player_volume' => 'integer|between:0,100',
                'player_autoplay' => 'boolean',
                'player_loop' => 'boolean',
                'player_speed_control' => 'boolean',
                'player_quality_control' => 'boolean',
                'player_fullscreen' => 'boolean',
                'player_pip' => 'boolean'
            ];

            $validation = $this->validationService->validate($data, $rules);
            if (!$validation['valid']) {
                return $this->responseService->error($validation['message'], 400);
            }

            // 更新配置
            foreach ($data as $key => $value) {
                if (array_key_exists($key, $rules)) {
                    // 查找现有配置
                    $existing = SystemConfig::where('config_key', $key)->find();

                    // 转换值为字符串
                    $stringValue = is_bool($value) ? ($value ? '1' : '0') : (string)$value;

                    if ($existing) {
                        // 更新现有配置
                        $existing->config_value = $stringValue;
                        $existing->save();
                    } else {
                        // 创建新配置
                        SystemConfig::create([
                            'config_key' => $key,
                            'config_value' => $stringValue,
                            'description' => $this->getPlayerConfigDescription($key)
                        ]);
                    }
                }
            }

            return $this->responseService->success(null, '播放器配置更新成功');
        } catch (\Exception $e) {
            return $this->responseService->error('更新播放器配置失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取播放器配置项描述
     */
    private function getPlayerConfigDescription(string $key): string
    {
        $descriptions = [
            'skip_ad_enabled' => '是否启用自动跳过采集视频前的广告',
            'ad_duration' => '采集视频前广告的时长，单位为秒',
            'show_skip_button' => '是否显示手动跳过广告按钮',
            'skip_button_delay' => '跳过按钮出现的延迟时间',
            'auto_skip_enabled' => '是否在广告时长结束后自动跳过',
            'smart_ad_detection' => '是否启用智能广告检测',
            'ad_keywords' => '广告关键词列表（用逗号分隔）',
            'max_ad_duration' => '单个广告片段最大时长（秒）',
            'ad_detection_confidence_threshold' => '广告检测置信度阈值（0-1）',
            'auto_detect_collected_videos' => '是否自动检测采集视频的广告',
            'skip_detection_for_uploaded' => '是否跳过用户上传视频的广告检测',
            'ad_url_patterns' => '广告URL模式列表（用逗号分隔）',
            'player_theme' => '播放器界面主题',
            'player_volume' => '播放器默认音量(0-100)',
            'player_autoplay' => '视频是否自动播放',
            'player_loop' => '视频是否循环播放',
            'player_speed_control' => '是否显示倍速控制',
            'player_quality_control' => '是否显示清晰度选择',
            'player_fullscreen' => '是否支持全屏播放',
            'player_pip' => '是否支持画中画模式'
        ];

        return $descriptions[$key] ?? '';
    }

    /**
     * 根据配置键名推断配置类型
     *
     * @param string $key
     * @return string
     */
    private function inferConfigType(string $key): string
    {
        // 布尔类型的配置键
        $booleanKeys = [
            'skip_ad_enabled', 'show_skip_button', 'auto_skip_enabled', 'smart_ad_detection',
            'allow_register', 'allow_guest_view', 'email_verification', 'phone_verification',
            'video_auto_audit', 'video_watermark', 'video_download_allowed', 'payment_enabled',
            'player_autoplay', 'player_loop', 'player_speed_control', 'player_quality_control',
            'player_fullscreen', 'player_pip'
        ];

        // 数字类型的配置键
        $numberKeys = [
            'ad_duration', 'skip_button_delay', 'max_ad_duration', 'max_file_size', 'max_video_size',
            'guest_view_limit', 'default_user_level', 'default_user_points', 'points_exchange_rate',
            'player_volume'
        ];

        // JSON/数组类型的配置键
        $jsonKeys = [
            'allowed_video_formats', 'allowed_image_formats', 'storage_config', 'ad_keywords'
        ];

        if (in_array($key, $booleanKeys)) {
            return 'boolean';
        } elseif (in_array($key, $numberKeys)) {
            return 'number';
        } elseif (in_array($key, $jsonKeys)) {
            return 'json';
        } else {
            return 'string';
        }
    }
}
