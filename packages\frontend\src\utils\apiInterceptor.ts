/**
 * 全局API拦截器 - 在应用启动时立即安装
 * 确保所有HTTP请求都能正确添加认证头
 */

let interceptorInstalled = false

export const installGlobalAPIInterceptor = () => {
  if (interceptorInstalled) {
    console.log('⚠️ 全局API拦截器已安装，跳过重复安装')
    return
  }

  console.log('🚀 安装全局API拦截器...')

  // 保存原始方法
  const OriginalXHR = window.XMLHttpRequest
  const OriginalFetch = window.fetch

  // 拦截XMLHttpRequest
  window.XMLHttpRequest = function() {
    const xhr = new OriginalXHR()
    const originalOpen = xhr.open
    const originalSend = xhr.send

    xhr.open = function(method: string, url: string, ...args: any[]) {
      console.log('🌐 全局XHR拦截:', method, url)

      // 修复URL
      if (url.startsWith('http://api/')) {
        url = url.replace('http://api/', 'http://localhost:3000/api/')
        console.log('🔧 全局URL修复:', url)
      }

      // 保存请求信息
      (this as any)._interceptorUrl = url
      (this as any)._interceptorMethod = method
      return originalOpen.call(this, method, url, ...args)
    }

    xhr.send = function(data?: any) {
      const url = (this as any)._interceptorUrl || ''
      const method = (this as any)._interceptorMethod || ''

      console.log('📤 全局XHR发送:', method, url)

      // 为所有API相关请求添加认证头
      if (shouldAddAuth(url)) {
        console.log('🔑 全局XHR添加认证头:', url)
        this.setRequestHeader('X-API-Key', 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+')
        this.setRequestHeader('Authorization', 'Bearer ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+')
        // 移除可能导致CORS问题的头
        // this.setRequestHeader('Cache-Control', 'no-cache')
        // this.setRequestHeader('Accept', '*/*')
        // this.setRequestHeader('Content-Type', 'application/json')
      }

      return originalSend.call(this, data)
    }

    return xhr
  } as any

  // 拦截Fetch API
  window.fetch = function(input: RequestInfo | URL, init?: RequestInit) {
    let url = typeof input === 'string' ? input : input.toString()
    console.log('🌐 全局Fetch拦截:', url)

    // 修复URL
    if (url.startsWith('http://api/')) {
      url = url.replace('http://api/', 'http://localhost:3000/api/')
      console.log('🔧 全局Fetch URL修复:', url)
      input = url
    }

    // 为所有API相关请求添加认证头
    if (shouldAddAuth(url)) {
      console.log('🔑 全局Fetch添加认证头:', url)

      const headers = new Headers(init?.headers)
      headers.set('X-API-Key', 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+')
      headers.set('Authorization', 'Bearer ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+')
      // 移除可能导致CORS问题的头
      // headers.set('Cache-Control', 'no-cache')
      // headers.set('Accept', '*/*')

      init = { ...init, headers }
    }

    return OriginalFetch(input, init)
  }

  interceptorInstalled = true
  console.log('✅ 全局API拦截器安装完成')
}

// 判断是否需要添加认证头
const shouldAddAuth = (url: string): boolean => {
  return url.includes('localhost:3000') || 
         url.includes('/api/') || 
         url.includes('encryption/key') || 
         url.includes('.ts') || 
         url.includes('.m3u8') ||
         url.includes('/hls/')
}

// 卸载拦截器（用于测试或重置）
export const uninstallGlobalAPIInterceptor = () => {
  // 注意：实际上很难完全恢复原始状态，因为我们没有保存原始方法的引用
  // 这个函数主要用于标记状态
  interceptorInstalled = false
  console.log('🗑️ 全局API拦截器已标记为卸载')
}

// 检查拦截器状态
export const isInterceptorInstalled = (): boolean => {
  return interceptorInstalled
}

// 立即安装拦截器（当模块被导入时）
installGlobalAPIInterceptor()
