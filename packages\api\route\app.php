<?php
// +----------------------------------------------------------------------
// | 视频平台主路由配置 - 重构版本
// +----------------------------------------------------------------------
// | 基于ThinkPHP 8.0的RESTful API路由
// | 重构后的统一路由管理，保留所有现有功能
// +----------------------------------------------------------------------

use think\facade\Route;

// 调试：记录路由文件加载
error_log('Refactored route file loaded at: ' . date('Y-m-d H:i:s'));

// =====================================================
// 基础路由和测试接口
// =====================================================

// 最简单的测试路由 - 放在最前面
Route::get('simple', function() {
    return 'Simple route works!';
});

// 测试路由 - 验证路由是否工作
Route::get('test-route', function() {
    return json(['message' => 'Test route works!', 'time' => date('Y-m-d H:i:s')]);
});

// API首页
Route::get('/', 'Api/index');

// 健康检查
Route::get('health', 'Api/health');

// 简单测试路由
Route::get('simple-test', function() {
    return json(['status' => 'ok', 'message' => 'Route works!', 'time' => date('Y-m-d H:i:s')]);
});

// =====================================================
// 环境检查接口（公开访问）
// =====================================================

Route::group('environment', function () {
    Route::get('check', 'EnvironmentController/check');
    Route::get('report', 'EnvironmentController/report');
    Route::get('health', 'EnvironmentController/health');
});

// =====================================================
// 静态文件路由（开发环境需要CORS支持）
// =====================================================

Route::get('storage/videos/<path>', 'Static/serve')->pattern(['path' => '.*'])->middleware('app\middleware\Cors');
Route::get('storage/images/<path>', 'Static/serve')->pattern(['path' => '.*'])->middleware('app\middleware\Cors');
Route::get('storage/avatars/<path>', 'Static/serve')->pattern(['path' => '.*'])->middleware('app\middleware\Cors');
Route::get('uploads/<path>', 'Static/serve')->pattern(['path' => '.*'])->middleware('app\middleware\Cors');

// HLS文件路由（开发环境需要CORS支持）
Route::get('hls/<path>', 'Static/serve')->pattern(['path' => '.*'])->middleware('app\middleware\Cors');
Route::options('hls/<path>', 'Static/serve')->pattern(['path' => '.*'])->middleware('app\middleware\Cors');

// 测试路由
Route::get('hls-test', function() {
    return json(['message' => 'HLS route works', 'time' => date('Y-m-d H:i:s')]);
});

// =====================================================
// 测试接口（开发环境使用）
// =====================================================

// 临时测试接口 - 验证数据库连接
Route::get('test-db', function() {
    try {
        $videos = \think\facade\Db::table('videos')
            ->where('status', 'published')
            ->where('audit_status', 'approved')
            ->limit(5)
            ->select();

        return json([
            'code' => 200,
            'message' => '数据库连接正常',
            'data' => [
                'count' => count($videos),
                'videos' => $videos->toArray()
            ]
        ]);
    } catch (\Exception $e) {
        return json([
            'code' => 500,
            'message' => '数据库连接失败',
            'error' => $e->getMessage()
        ]);
    }
});

// 测试用户注册接口（不需要API密钥）
Route::post('test/register', 'Auth/register');
Route::post('test/login', 'Auth/login');

// =====================================================
// 管理员登录API（独立配置）
// =====================================================

Route::post('api/admin/login', 'admin/Auth/login')->middleware(\app\middleware\Cors::class);

// =====================================================
// 引入模块化路由文件
// =====================================================

// 引入API v1路由（主要API版本）
require_once __DIR__ . '/api_v1.php';

// 引入管理员API路由
require_once __DIR__ . '/admin_api.php';

// 引入兼容性路由（向后兼容）
require_once __DIR__ . '/compatibility.php';
