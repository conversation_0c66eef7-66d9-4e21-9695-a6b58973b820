import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'
import { API_CONFIG, axiosConfig } from './api-config'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Client-Version': '1.0.0',
    'X-Client-Platform': 'admin-web'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: any) => {
    const authStore = useAuthStore()

    // 开发环境下的调试日志
    if (API_CONFIG.debug) {
      console.log('🔧 API请求:', {
        url: config.url,
        method: config.method,
        environment: API_CONFIG.environment
      })
    }

    // 添加API密钥到请求头
    config.headers = config.headers || {}
    config.headers['X-API-Key'] = API_CONFIG.apiKey

    // 调试信息
    if (API_CONFIG.debug) {
      console.log('🔑 API认证:', {
        apiKey: API_CONFIG.apiKey.substring(0, 10) + '...',
        environment: API_CONFIG.environment,
        baseURL: API_CONFIG.baseURL
      })
    }

    // 如果有token，也添加到请求头（用于用户认证）
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    return config
  },
  (error) => {
    if (API_CONFIG.debug) {
      console.error('❌ 请求错误:', error)
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const responseData = response.data

    // 开发环境下的调试日志（仅在需要时启用）
    if (import.meta.env.DEV && import.meta.env.VITE_DEBUG === 'true') {
      console.log('axios响应拦截器:', responseData)
    }

    // 处理不同的响应格式
    // 格式1: {code: 200, message: '', data: {}}
    if (responseData.code !== undefined) {
      if (responseData.code === 200) {
        return responseData
      } else {
        // 检查是否是认证失败
        if (responseData.code === 401) {
          const isAuthCheck = response.config.url &&
                             (response.config.url.includes('/api/admin/profile') ||
                              response.config.url.includes('/api/admin/auth/verify'))
          handleAuthError(isAuthCheck)
          return Promise.reject(new Error('登录已过期，请重新登录'))
        }
        ElMessage.error(responseData.message || '请求失败')
        return Promise.reject(new Error(responseData.message || '请求失败'))
      }
    }

    // 格式2: {success: true, message: '', data: {}}
    if (responseData.success !== undefined) {
      if (responseData.success === true) {
        return responseData
      } else {
        ElMessage.error(responseData.message || '请求失败')
        return Promise.reject(new Error(responseData.message || '请求失败'))
      }
    }

    // 直接返回数据（兼容其他格式）
    return responseData
  },
  (error) => {
    console.error('响应错误:', error)

    // 检查是否是认证检查请求（通过URL判断）
    const isAuthCheck = error.config?.url?.includes('/api/admin/profile') ||
                       error.config?.url?.includes('/api/admin/auth/verify')

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 处理认证失败
          handleAuthError(isAuthCheck)
          return Promise.reject(new Error('登录已过期，请重新登录'))
        case 403:
          if (!isAuthCheck) {
            ElMessage.error('没有权限访问')
          }
          break
        case 404:
          if (!isAuthCheck) {
            ElMessage.error('请求的资源不存在')
          }
          break
        case 500:
          if (!isAuthCheck) {
            ElMessage.error('服务器内部错误')
          }
          break
        default:
          if (!isAuthCheck) {
            ElMessage.error(data?.message || '请求失败')
          }
      }
    } else if (error.request) {
      if (!isAuthCheck) {
        ElMessage.error('网络连接失败')
      }
    } else {
      if (!isAuthCheck) {
        ElMessage.error('请求配置错误')
      }
    }

    return Promise.reject(error)
  }
)

// 处理认证错误的函数
function handleAuthError(isAuthCheck = false) {
  const authStore = useAuthStore()

  // 清除认证信息
  authStore.logout()

  // 如果不是认证检查请求，显示友好的提示信息
  if (!isAuthCheck) {
    ElMessage.warning('登录已过期，请重新登录')

    // 延迟跳转，确保消息能够显示
    setTimeout(() => {
      // 跳转到登录页面，并保存当前路径用于登录后跳转
      const currentPath = router.currentRoute.value.fullPath
      if (currentPath !== '/login') {
        router.push({
          path: '/login',
          query: { redirect: currentPath }
        })
      }
    }, 1000)
  } else {
    // 认证检查失败，直接跳转不显示消息
    const currentPath = router.currentRoute.value.fullPath
    if (currentPath !== '/login') {
      router.push({
        path: '/login',
        query: { redirect: currentPath }
      })
    }
  }
}

export default request
