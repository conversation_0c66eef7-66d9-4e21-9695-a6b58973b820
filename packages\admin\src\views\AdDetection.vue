<template>
  <div class="ad-detection-page">
    <div class="page-header">
      <h1>广告检测管理</h1>
      <p>智能检测和管理视频中的广告内容</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.total_videos }}</div>
            <div class="stat-label">总视频数</div>
          </div>
          <el-icon class="stat-icon"><VideoPlay /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.videos_with_ads }}</div>
            <div class="stat-label">有广告视频</div>
            <div class="stat-percent">{{ statistics.ad_percentage }}%</div>
          </div>
          <el-icon class="stat-icon warning"><Warning /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.pending_detection }}</div>
            <div class="stat-label">待检测</div>
          </div>
          <el-icon class="stat-icon info"><Clock /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.ad_detection_rate }}%</div>
            <div class="stat-label">检测覆盖率</div>
          </div>
          <el-icon class="stat-icon success"><Check /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="autoDetectPending" :loading="detecting">
            <el-icon><MagicStick /></el-icon>
            自动检测待检测视频
          </el-button>
          <el-button @click="refreshStatistics" :loading="loadingStats">
            <el-icon><Refresh /></el-icon>
            刷新统计
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-select v-model="currentTab" @change="handleTabChange" style="width: 200px">
            <el-option label="待检测视频" value="pending" />
            <el-option label="有广告视频" value="ads" />
            <el-option label="无广告视频" value="no-ads" />
            <el-option label="所有视频" value="all" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 视频列表 -->
    <el-card class="video-list-card">
      <template #header>
        <div class="card-header">
          <span>{{ getTabTitle() }}</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索视频标题..."
              style="width: 200px"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </template>

      <el-table
        :data="videoList"
        v-loading="loadingVideos"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="视频信息" min-width="300">
          <template #default="{ row }">
            <div class="video-info">
              <img :src="row.cover_image || '/default-cover.jpg'" class="video-cover" />
              <div class="video-details">
                <div class="video-title">{{ row.title }}</div>
                <div class="video-meta">
                  <span>ID: {{ row.id }}</span>
                  <span>时长: {{ formatDuration(row.duration) }}</span>
                  <span>来源: {{ getSourceText(row.source_type) }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="广告状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getAdStatusType(row.has_ads)">
              {{ getAdStatusText(row.has_ads) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="检测状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getDetectionStatusType(row.ad_detection_status)" size="small">
              {{ getDetectionStatusText(row.ad_detection_status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="置信度" width="100">
          <template #default="{ row }">
            <div v-if="row.ad_confidence_score !== null">
              <el-progress
                :percentage="Math.round(row.ad_confidence_score * 100)"
                :color="getConfidenceColor(row.ad_confidence_score)"
                :stroke-width="6"
                text-inside
              />
            </div>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <el-table-column label="广告段" width="120">
          <template #default="{ row }">
            <div v-if="row.ad_segments && row.ad_segments.length > 0">
              <el-tag size="small" v-for="(segment, index) in row.ad_segments" :key="index">
                {{ segment.start }}s-{{ segment.end }}s
              </el-tag>
            </div>
            <span v-else class="text-muted">无</span>
          </template>
        </el-table-column>

        <el-table-column label="检测时间" width="150">
          <template #default="{ row }">
            <span v-if="row.ad_last_detected_at">
              {{ formatDate(row.ad_last_detected_at) }}
            </span>
            <span v-else class="text-muted">未检测</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="detectSingleVideo(row.id)" :loading="row.detecting">
                检测
              </el-button>
              <el-button size="small" @click="showManualMarkDialog(row)">
                标记
              </el-button>
              <el-button size="small" @click="resetDetection(row.id)" type="warning">
                重置
              </el-button>
              <el-button size="small" @click="viewVideoDetail(row)" type="info">
                详情
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作栏 -->
    <div v-if="selectedVideos.length > 0" class="batch-actions">
      <el-card>
        <div class="batch-content">
          <span>已选择 {{ selectedVideos.length }} 个视频</span>
          <div class="batch-buttons">
            <el-button @click="batchDetect" :loading="batchDetecting">
              批量检测
            </el-button>
            <el-button @click="batchMarkAsAds">
              批量标记为有广告
            </el-button>
            <el-button @click="batchMarkAsNoAds">
              批量标记为无广告
            </el-button>
            <el-button @click="clearSelection">
              清除选择
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 手动标记对话框 -->
    <el-dialog
      v-model="manualMarkDialog.visible"
      title="手动标记广告状态"
      width="600px"
    >
      <el-form :model="manualMarkDialog.form" label-width="120px">
        <el-form-item label="视频标题">
          <span>{{ manualMarkDialog.video?.title }}</span>
        </el-form-item>
        
        <el-form-item label="是否有广告">
          <el-radio-group v-model="manualMarkDialog.form.hasAds">
            <el-radio :label="true">有广告</el-radio>
            <el-radio :label="false">无广告</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="manualMarkDialog.form.hasAds" label="广告段">
          <div class="ad-segments">
            <div
              v-for="(segment, index) in manualMarkDialog.form.adSegments"
              :key="index"
              class="segment-item"
            >
              <el-input-number
                v-model="segment.start"
                :min="0"
                :max="segment.end - 1"
                placeholder="开始时间"
                size="small"
              />
              <span class="segment-separator">-</span>
              <el-input-number
                v-model="segment.end"
                :min="segment.start + 1"
                placeholder="结束时间"
                size="small"
              />
              <el-select v-model="segment.type" size="small" style="width: 100px">
                <el-option label="开头" value="opening" />
                <el-option label="中间" value="middle" />
                <el-option label="结尾" value="ending" />
              </el-select>
              <el-button size="small" type="danger" @click="removeSegment(index)">
                删除
              </el-button>
            </div>
            <el-button size="small" @click="addSegment">添加广告段</el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="manualMarkDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmManualMark" :loading="manualMarkDialog.loading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 视频详情对话框 -->
    <el-dialog
      v-model="videoDetailDialog.visible"
      title="视频详情"
      width="800px"
    >
      <div v-if="videoDetailDialog.video" class="video-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="视频ID">
            {{ videoDetailDialog.video.id }}
          </el-descriptions-item>
          <el-descriptions-item label="标题">
            {{ videoDetailDialog.video.title }}
          </el-descriptions-item>
          <el-descriptions-item label="时长">
            {{ formatDuration(videoDetailDialog.video.duration) }}
          </el-descriptions-item>
          <el-descriptions-item label="来源类型">
            {{ getSourceText(videoDetailDialog.video.source_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="广告状态">
            <el-tag :type="getAdStatusType(videoDetailDialog.video.has_ads)">
              {{ getAdStatusText(videoDetailDialog.video.has_ads) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="检测状态">
            <el-tag :type="getDetectionStatusType(videoDetailDialog.video.ad_detection_status)">
              {{ getDetectionStatusText(videoDetailDialog.video.ad_detection_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="置信度">
            <span v-if="videoDetailDialog.video.ad_confidence_score !== null">
              {{ Math.round(videoDetailDialog.video.ad_confidence_score * 100) }}%
            </span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="检测方法">
            {{ videoDetailDialog.video.ad_detection_method || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="匹配关键词" span="2">
            {{ videoDetailDialog.video.ad_keywords_matched || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="检测时间" span="2">
            {{ videoDetailDialog.video.ad_last_detected_at ? formatDate(videoDetailDialog.video.ad_last_detected_at) : '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="videoDetailDialog.video.ad_segments && videoDetailDialog.video.ad_segments.length > 0" class="ad-segments-detail">
          <h4>广告段详情</h4>
          <el-table :data="videoDetailDialog.video.ad_segments" size="small">
            <el-table-column label="类型" prop="type" width="80" />
            <el-table-column label="开始时间" width="100">
              <template #default="{ row }">{{ row.start }}s</template>
            </el-table-column>
            <el-table-column label="结束时间" width="100">
              <template #default="{ row }">{{ row.end }}s</template>
            </el-table-column>
            <el-table-column label="时长" width="80">
              <template #default="{ row }">{{ row.end - row.start }}s</template>
            </el-table-column>
            <el-table-column label="置信度" width="100">
              <template #default="{ row }">
                <span v-if="row.confidence">{{ Math.round(row.confidence * 100) }}%</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay,
  Warning,
  Clock,
  Check,
  MagicStick,
  Refresh,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const currentTab = ref('pending')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loadingStats = ref(false)
const loadingVideos = ref(false)
const detecting = ref(false)
const batchDetecting = ref(false)
const selectedVideos = ref<any[]>([])
const videoList = ref<any[]>([])

// 统计数据
const statistics = ref({
  total_videos: 0,
  videos_with_ads: 0,
  videos_without_ads: 0,
  pending_detection: 0,
  detected_videos: 0,
  manually_marked: 0,
  collected_videos: 0,
  high_confidence: 0,
  medium_confidence: 0,
  low_confidence: 0,
  ad_detection_rate: 0,
  ad_percentage: 0
})

// 手动标记对话框
const manualMarkDialog = reactive({
  visible: false,
  loading: false,
  video: null as any,
  form: {
    hasAds: false,
    adSegments: [] as Array<{start: number, end: number, type: string}>
  }
})

// 视频详情对话框
const videoDetailDialog = reactive({
  visible: false,
  video: null as any
})

// 计算属性
const getTabTitle = () => {
  const titles = {
    pending: '待检测视频',
    ads: '有广告视频',
    'no-ads': '无广告视频',
    all: '所有视频'
  }
  return titles[currentTab.value] || '视频列表'
}

// 方法
const refreshStatistics = async () => {
  try {
    loadingStats.value = true
    // TODO: 调用API获取统计数据
    const response = await fetch('/api/admin/ad-detection/statistics')
    const result = await response.json()

    if (result.success) {
      statistics.value = result.data
    } else {
      ElMessage.error('获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loadingStats.value = false
  }
}

const loadVideoList = async () => {
  try {
    loadingVideos.value = true

    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: pageSize.value.toString(),
      tab: currentTab.value,
      search: searchKeyword.value
    })

    let endpoint = '/api/admin/videos'
    if (currentTab.value === 'pending') {
      endpoint = '/api/admin/ad-detection/pending-videos'
    } else if (currentTab.value === 'ads') {
      endpoint = '/api/admin/ad-detection/ads-videos'
    }

    const response = await fetch(`${endpoint}?${params}`)
    const result = await response.json()

    if (result.success) {
      videoList.value = result.data.data || result.data
      total.value = result.data.total || 0
    } else {
      ElMessage.error('获取视频列表失败')
    }
  } catch (error) {
    console.error('获取视频列表失败:', error)
    ElMessage.error('获取视频列表失败')
  } finally {
    loadingVideos.value = false
  }
}

const autoDetectPending = async () => {
  try {
    detecting.value = true

    const response = await fetch('/api/admin/ad-detection/auto-detect-pending', {
      method: 'POST'
    })
    const result = await response.json()

    if (result.success) {
      ElMessage.success(`已处理 ${result.data.summary.total} 个视频的广告检测`)
      refreshStatistics()
      loadVideoList()
    } else {
      ElMessage.error('自动检测失败')
    }
  } catch (error) {
    console.error('自动检测失败:', error)
    ElMessage.error('自动检测失败')
  } finally {
    detecting.value = false
  }
}

const detectSingleVideo = async (videoId: number) => {
  try {
    const video = videoList.value.find(v => v.id === videoId)
    if (video) video.detecting = true

    const response = await fetch(`/api/admin/ad-detection/detect/${videoId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ force_redetect: true })
    })
    const result = await response.json()

    if (result.success) {
      ElMessage.success('检测完成')
      loadVideoList()
    } else {
      ElMessage.error('检测失败: ' + result.message)
    }
  } catch (error) {
    console.error('检测失败:', error)
    ElMessage.error('检测失败')
  } finally {
    const video = videoList.value.find(v => v.id === videoId)
    if (video) video.detecting = false
  }
}

const showManualMarkDialog = (video: any) => {
  manualMarkDialog.video = video
  manualMarkDialog.form.hasAds = video.has_ads === 1
  manualMarkDialog.form.adSegments = video.ad_segments ? [...video.ad_segments] : []
  manualMarkDialog.visible = true
}

const confirmManualMark = async () => {
  try {
    manualMarkDialog.loading = true

    const response = await fetch('/api/admin/ad-detection/manual-mark', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        video_id: manualMarkDialog.video.id,
        has_ads: manualMarkDialog.form.hasAds,
        ad_segments: manualMarkDialog.form.adSegments
      })
    })
    const result = await response.json()

    if (result.success) {
      ElMessage.success('标记成功')
      manualMarkDialog.visible = false
      loadVideoList()
      refreshStatistics()
    } else {
      ElMessage.error('标记失败: ' + result.message)
    }
  } catch (error) {
    console.error('标记失败:', error)
    ElMessage.error('标记失败')
  } finally {
    manualMarkDialog.loading = false
  }
}

const addSegment = () => {
  manualMarkDialog.form.adSegments.push({
    start: 0,
    end: 17,
    type: 'opening'
  })
}

const removeSegment = (index: number) => {
  manualMarkDialog.form.adSegments.splice(index, 1)
}

const resetDetection = async (videoId: number) => {
  try {
    await ElMessageBox.confirm('确定要重置此视频的广告检测状态吗？', '确认重置', {
      type: 'warning'
    })

    const response = await fetch(`/api/admin/ad-detection/reset/${videoId}`, {
      method: 'POST'
    })
    const result = await response.json()

    if (result.success) {
      ElMessage.success('重置成功')
      loadVideoList()
      refreshStatistics()
    } else {
      ElMessage.error('重置失败: ' + result.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置失败:', error)
      ElMessage.error('重置失败')
    }
  }
}

const viewVideoDetail = (video: any) => {
  videoDetailDialog.video = video
  videoDetailDialog.visible = true
}

const batchDetect = async () => {
  try {
    batchDetecting.value = true

    const videoIds = selectedVideos.value.map(v => v.id)
    const response = await fetch('/api/admin/ad-detection/batch-detect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        video_ids: videoIds,
        force_redetect: true
      })
    })
    const result = await response.json()

    if (result.success) {
      ElMessage.success(`批量检测完成，成功: ${result.data.summary.success}，失败: ${result.data.summary.error}`)
      loadVideoList()
      refreshStatistics()
      clearSelection()
    } else {
      ElMessage.error('批量检测失败')
    }
  } catch (error) {
    console.error('批量检测失败:', error)
    ElMessage.error('批量检测失败')
  } finally {
    batchDetecting.value = false
  }
}

const batchMarkAsAds = async () => {
  try {
    await ElMessageBox.confirm(`确定要将选中的 ${selectedVideos.value.length} 个视频标记为有广告吗？`, '确认标记', {
      type: 'warning'
    })

    for (const video of selectedVideos.value) {
      await fetch('/api/admin/ad-detection/manual-mark', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          video_id: video.id,
          has_ads: true,
          ad_segments: []
        })
      })
    }

    ElMessage.success('批量标记完成')
    loadVideoList()
    refreshStatistics()
    clearSelection()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量标记失败:', error)
      ElMessage.error('批量标记失败')
    }
  }
}

const batchMarkAsNoAds = async () => {
  try {
    await ElMessageBox.confirm(`确定要将选中的 ${selectedVideos.value.length} 个视频标记为无广告吗？`, '确认标记', {
      type: 'warning'
    })

    for (const video of selectedVideos.value) {
      await fetch('/api/admin/ad-detection/manual-mark', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          video_id: video.id,
          has_ads: false,
          ad_segments: []
        })
      })
    }

    ElMessage.success('批量标记完成')
    loadVideoList()
    refreshStatistics()
    clearSelection()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量标记失败:', error)
      ElMessage.error('批量标记失败')
    }
  }
}

// 事件处理
const handleTabChange = () => {
  currentPage.value = 1
  loadVideoList()
}

const handleSearch = () => {
  currentPage.value = 1
  loadVideoList()
}

const handleSizeChange = () => {
  currentPage.value = 1
  loadVideoList()
}

const handleCurrentChange = () => {
  loadVideoList()
}

const handleSelectionChange = (selection: any[]) => {
  selectedVideos.value = selection
}

const clearSelection = () => {
  selectedVideos.value = []
}

// 工具函数
const formatDuration = (seconds: number) => {
  if (!seconds) return '-'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getSourceText = (sourceType: string) => {
  const sourceMap = {
    upload: '用户上传',
    collect: '采集',
    mac_cms: '苹果CMS'
  }
  return sourceMap[sourceType] || sourceType
}

const getAdStatusText = (hasAds: number | null) => {
  if (hasAds === null) return '未检测'
  return hasAds ? '有广告' : '无广告'
}

const getAdStatusType = (hasAds: number | null) => {
  if (hasAds === null) return 'info'
  return hasAds ? 'warning' : 'success'
}

const getDetectionStatusText = (status: string) => {
  const statusMap = {
    pending: '待检测',
    detected: '已检测',
    manual: '手动标记',
    skipped: '已跳过'
  }
  return statusMap[status] || status
}

const getDetectionStatusType = (status: string) => {
  const typeMap = {
    pending: 'info',
    detected: 'success',
    manual: 'warning',
    skipped: 'info'
  }
  return typeMap[status] || 'info'
}

const getConfidenceColor = (score: number) => {
  if (score >= 0.8) return '#67c23a'
  if (score >= 0.5) return '#e6a23c'
  if (score >= 0.3) return '#f56c6c'
  return '#909399'
}

// 生命周期
onMounted(() => {
  refreshStatistics()
  loadVideoList()
})
</script>

<style scoped>
.ad-detection-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-percent {
  font-size: 12px;
  color: #999;
}

.stat-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
  z-index: 1;
}

.stat-icon.warning {
  color: #e6a23c;
}

.stat-icon.info {
  color: #409eff;
}

.stat-icon.success {
  color: #67c23a;
}

.toolbar-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.video-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.video-cover {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.video-details {
  flex: 1;
}

.video-title {
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.4;
}

.video-meta {
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 12px;
}

.text-muted {
  color: #999;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.batch-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 16px;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.ad-segments {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.segment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.segment-separator {
  color: #666;
  font-weight: 500;
}

.ad-segments-detail {
  margin-top: 20px;
}

.ad-segments-detail h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left {
    justify-content: center;
  }

  .batch-actions {
    left: 20px;
    right: 20px;
    transform: none;
  }

  .batch-content {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .batch-buttons {
    justify-content: center;
  }
}
</style>
