# 开发模式 - PHP 8.2 Apache镜像
FROM php:8.2-apache

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装系统依赖和PHP扩展
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    zip \
    unzip \
    curl \
    git \
    netcat-openbsd \
    ffmpeg \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd pdo pdo_mysql zip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Redis扩展 (暂时跳过，使用文件缓存)
# RUN pecl install redis && docker-php-ext-enable redis

# 安装Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 配置Apache
RUN a2enmod rewrite headers expires
COPY apache-config.conf /etc/apache2/sites-available/000-default.conf

# 开发模式PHP配置
RUN echo "display_errors = On" >> /usr/local/etc/php/conf.d/dev.ini \
    && echo "error_reporting = E_ALL" >> /usr/local/etc/php/conf.d/dev.ini \
    && echo "log_errors = On" >> /usr/local/etc/php/conf.d/dev.ini

# 设置工作目录
WORKDIR /var/www/html

# 开发模式：直接挂载代码，不复制
# 这样代码修改可以实时生效

# 设置权限脚本
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

EXPOSE 80

# 使用entrypoint脚本启动
ENTRYPOINT ["/entrypoint.sh"]
