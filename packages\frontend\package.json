{"name": "zhengshiban-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3002", "build": "vite build", "build:check": "vue-tsc && vite build", "build:dev": "vue-tsc && vite build --mode development", "build:staging": "vue-tsc && vite build --mode staging", "build:prod": "vue-tsc && vite build --mode production", "preview": "vite preview --host 0.0.0.0 --port 3002", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:performance": "vitest run tests/performance", "test:security": "vitest run tests/security", "test:components": "vitest run tests/components", "test:watch": "vitest --watch", "test:ci": "vitest run --coverage --reporter=junit --outputFile=test-results.xml", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "env:dev": "node scripts/env-switch.js development", "env:staging": "node scripts/env-switch.js staging", "env:prod": "node scripts/env-switch.js production", "env:switch": "node scripts/env-switch.js", "env:local": "node scripts/switch-env.js local", "env:remote": "node scripts/switch-env.js remote", "env:status": "node scripts/switch-env.js status"}, "dependencies": {"@videojs/http-streaming": "^3.17.2", "axios": "^1.6.0", "element-plus": "^2.10.4", "hls.js": "^1.6.7", "pinia": "^2.1.0", "shaka-player": "^4.15.9", "video.js": "^8.23.3", "videojs-contrib-quality-levels": "^4.1.0", "videojs-hls-quality-selector": "^2.0.0", "vue": "^3.4.0", "vue-router": "^4.2.0", "xgplayer": "^3.0.22", "xgplayer-hls": "^3.0.22"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.46.0", "@rollup/rollup-linux-x64-musl": "^4.46.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/video.js": "^7.3.58", "@vitejs/plugin-vue": "^5.0.0", "@vue/test-utils": "^2.4.0", "jsdom": "^23.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vitest": "^1.0.0", "vue-tsc": "^2.0.0"}}