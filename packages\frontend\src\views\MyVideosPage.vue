<template>
  <div class="my-videos-page">
    <!-- 顶部导航 -->
    <div class="header">
      <button class="back-btn" @click="goBack">
        <span class="back-icon">←</span>
      </button>
      <h1 class="title">我的作品</h1>
      <button class="upload-btn" @click="goToUpload">
        <span class="upload-icon">+</span>
      </button>
    </div>

    <!-- 状态筛选 -->
    <div class="filter-tabs">
      <button 
        v-for="tab in statusTabs" 
        :key="tab.value"
        class="filter-tab"
        :class="{ active: currentStatus === tab.value }"
        @click="changeStatus(tab.value)"
      >
        {{ tab.label }}
        <span v-if="tab.count > 0" class="count-badge">{{ tab.count }}</span>
      </button>
    </div>

    <!-- 视频列表 -->
    <div class="videos-container">
      <div v-if="loading" class="loading">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <div v-else-if="videos.length === 0" class="empty-state">
        <div class="empty-icon">📹</div>
        <p class="empty-text">{{ getEmptyText() }}</p>
        <button v-if="currentStatus === 'all'" class="upload-empty-btn" @click="goToUpload">
          上传第一个视频
        </button>
      </div>

      <div v-else class="videos-list">
        <div 
          v-for="video in videos" 
          :key="video.id" 
          class="video-item"
          @click="viewVideo(video)"
        >
          <!-- 视频封面 -->
          <div class="video-cover">
            <img
              :src="video.cover_image || '/default-cover.svg'"
              :alt="video.title"
              @error="handleImageError"
            />
            <div class="video-duration">{{ formatDuration(video.duration) }}</div>
            
            <!-- 处理状态覆盖层 -->
            <div v-if="isProcessing(video)" class="processing-overlay">
              <div class="processing-content">
                <div class="processing-spinner"></div>
                <p class="processing-text">{{ getProcessingText(video) }}</p>
                <div v-if="video.processing_summary" class="processing-details">
                  <div class="processing-progress">
                    <div class="progress-bar">
                      <div
                        class="progress-fill"
                        :style="{ width: video.processing_summary.overall_progress + '%' }"
                      ></div>
                    </div>
                    <span class="progress-text">{{ video.processing_summary.overall_progress }}%</span>
                  </div>
                  <p class="current-step">{{ video.processing_summary.current_step }}</p>
                </div>
                <div v-else-if="video.processing_progress" class="processing-progress">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{ width: video.processing_progress + '%' }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ video.processing_progress }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 视频信息 -->
          <div class="video-info">
            <h3 class="video-title">{{ video.title }}</h3>
            <div class="video-meta">
              <span class="video-stats">
                <span class="stat-item">👁 {{ formatCount(video.view_count) }}</span>
                <span class="stat-item">👍 {{ formatCount(video.like_count) }}</span>
              </span>
              <span class="video-date">{{ formatDate(video.created_at) }}</span>
            </div>
            
            <!-- 状态标签 -->
            <div class="status-tags">
              <span class="status-tag" :class="getStatusClass(video.status)">
                {{ getStatusText(video.status) }}
              </span>
              <span class="audit-tag" :class="getAuditClass(video.audit_status)">
                {{ getAuditText(video.audit_status) }}
              </span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="video-actions">
            <button class="action-btn" @click.stop="editVideo(video)">
              <span class="action-icon">✏️</span>
            </button>
            <button class="action-btn delete" @click.stop="deleteVideo(video)">
              <span class="action-icon">🗑️</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && !loading" class="load-more">
      <button class="load-more-btn" @click="loadMore">加载更多</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useGlobalStore } from '@/stores/global'
import { useUserStore } from '@/stores/user'
import { getUserVideos, getVideoProcessingStatus } from '@/api/video'

const router = useRouter()
const globalStore = useGlobalStore()
const userStore = useUserStore()

// 响应式数据
const videos = ref<any[]>([])
const loading = ref(false)
const currentStatus = ref('all')
const currentPage = ref(1)
const hasMore = ref(true)
const statusCounts = ref({
  all: 0,
  uploading: 0,
  processing: 0,
  pending: 0,
  approved: 0,
  rejected: 0
})

// 状态筛选标签
const statusTabs = computed(() => [
  { label: '全部', value: 'all', count: statusCounts.value.all },
  { label: '上传中', value: 'uploading', count: statusCounts.value.uploading },
  { label: '转码中', value: 'processing', count: statusCounts.value.processing },
  { label: '待审核', value: 'pending', count: statusCounts.value.pending },
  { label: '已通过', value: 'approved', count: statusCounts.value.approved },
  { label: '未通过', value: 'rejected', count: statusCounts.value.rejected }
])

// 轮询定时器
let pollingTimer: NodeJS.Timeout | null = null

// 方法
const goBack = () => {
  router.go(-1)
}

const goToUpload = () => {
  router.push('/upload')
}

const changeStatus = (status: string) => {
  currentStatus.value = status
  currentPage.value = 1
  hasMore.value = true
  loadVideos(true)
}

const loadVideos = async (reset = false) => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    const params: any = {
      page: reset ? 1 : currentPage.value,
      limit: 10
    }
    
    if (currentStatus.value !== 'all') {
      if (currentStatus.value === 'uploading') {
        params.status = 'uploading'
      } else if (currentStatus.value === 'processing') {
        params.status = 'processing'
      } else {
        params.audit_status = currentStatus.value
      }
    }
    
    const result = await getUserVideos(params)
    
    if (reset) {
      videos.value = result.data.videos || []
      currentPage.value = 1
    } else {
      videos.value.push(...(result.data.videos || []))
    }
    
    hasMore.value = (result.data.videos || []).length === params.limit
    if (!reset) {
      currentPage.value++
    }
    
    // 更新状态计数
    updateStatusCounts(result.data.statusCounts || {})
    
    // 开始轮询处理中的视频状态
    startPolling()
    
  } catch (error: any) {
    globalStore.showToast(error.message || '加载失败', 'error')
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  loadVideos(false)
}

const updateStatusCounts = (counts: any) => {
  statusCounts.value = {
    all: counts.all || 0,
    uploading: counts.uploading || 0,
    processing: counts.processing || 0,
    pending: counts.pending || 0,
    approved: counts.approved || 0,
    rejected: counts.rejected || 0
  }
}

const startPolling = () => {
  // 清除现有定时器
  if (pollingTimer) {
    clearInterval(pollingTimer)
  }
  
  // 检查是否有处理中的视频
  const processingVideos = videos.value.filter(video => 
    isProcessing(video)
  )
  
  if (processingVideos.length > 0) {
    pollingTimer = setInterval(async () => {
      // 更新处理状态
      for (const video of processingVideos) {
        try {
          const statusResult = await getVideoProcessingStatus(video.id)
          if (statusResult.data) {
            // 更新处理状态摘要
            video.processing_summary = statusResult.data
            video.processing_progress = statusResult.data.overall_progress
            video.processing_status = statusResult.data.overall_status
            video.processing_message = statusResult.data.current_step

            // 如果处理完成，停止轮询该视频
            if (statusResult.data.overall_status === 'completed' || statusResult.data.overall_status === 'failed') {
              // 重新加载视频列表以获取最新状态
              setTimeout(() => {
                loadVideos()
              }, 1000)
            }
          }
        } catch (error) {
          console.error('获取处理状态失败:', error)
        }
      }
    }, 3000) // 每3秒轮询一次
  }
}

const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

const isProcessing = (video: any): boolean => {
  // 检查处理状态摘要
  if (video.processing_summary) {
    return video.processing_summary.overall_status === 'processing' ||
           video.processing_summary.overall_status === 'pending'
  }

  // 兼容旧的状态检查
  return video.status === 'uploading' ||
         video.status === 'processing' ||
         video.processing_status === 'processing' ||
         video.processing_status === 'pending'
}

const getProcessingText = (video: any): string => {
  // 优先使用处理状态摘要
  if (video.processing_summary && video.processing_summary.current_step) {
    return video.processing_summary.current_step
  }

  // 兼容旧的状态文本
  if (video.status === 'uploading') return '上传中...'
  if (video.status === 'processing') return '转码中...'
  if (video.processing_status === 'processing') return '处理中...'
  if (video.processing_status === 'pending') return '等待处理...'
  return '处理中...'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    uploading: '上传中',
    processing: '转码中',
    published: '已发布',
    draft: '草稿'
  }
  return statusMap[status] || status
}

const getStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    uploading: 'status-uploading',
    processing: 'status-processing',
    published: 'status-published',
    draft: 'status-draft'
  }
  return classMap[status] || ''
}

const getAuditText = (auditStatus: string): string => {
  const auditMap: Record<string, string> = {
    pending: '待审核',
    approved: '已通过',
    rejected: '未通过'
  }
  return auditMap[auditStatus] || auditStatus
}

const getAuditClass = (auditStatus: string): string => {
  const classMap: Record<string, string> = {
    pending: 'audit-pending',
    approved: 'audit-approved',
    rejected: 'audit-rejected'
  }
  return classMap[auditStatus] || ''
}

const getEmptyText = (): string => {
  const textMap: Record<string, string> = {
    all: '还没有上传任何视频',
    uploading: '没有上传中的视频',
    processing: '没有转码中的视频',
    pending: '没有待审核的视频',
    approved: '没有已通过的视频',
    rejected: '没有被拒绝的视频'
  }
  return textMap[currentStatus.value] || '暂无数据'
}

const formatCount = (count: number): string => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}w`
  }
  return count.toString()
}

const formatDuration = (seconds: number): string => {
  if (!seconds) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`
  
  return date.toLocaleDateString()
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/default-cover.svg'
}

const viewVideo = (video: any) => {
  if (video.status === 'published') {
    router.push(`/video/${video.id}`)
  } else {
    globalStore.showToast('视频还未发布', 'info')
  }
}

const editVideo = (video: any) => {
  globalStore.showToast('编辑功能开发中...', 'info')
}

const deleteVideo = (video: any) => {
  globalStore.showToast('删除功能开发中...', 'info')
}

// 生命周期
onMounted(() => {
  // 检查登录状态
  if (!userStore.isAuthenticated) {
    globalStore.showToast('请先登录', 'warning')
    router.push('/login')
    return
  }
  
  loadVideos(true)
})

onUnmounted(() => {
  stopPolling()
})
</script>

<style scoped>
.my-videos-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: white;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(0,0,0,0.3);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn, .upload-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.back-btn:hover, .upload-btn:hover {
  background: rgba(255,255,255,0.1);
}

.upload-btn {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.4);
  color: #4CAF50;
}

.title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.filter-tabs {
  display: flex;
  padding: 0 20px 10px;
  gap: 10px;
  overflow-x: auto;
}

.filter-tab {
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-tab.active {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
  color: #4CAF50;
}

.count-badge {
  background: rgba(255,255,255,0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.8rem;
  min-width: 18px;
  text-align: center;
}

.videos-container {
  padding: 20px;
}

.loading {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255,255,255,0.1);
  border-top: 3px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-text {
  font-size: 1.1rem;
  color: #cccccc;
  margin-bottom: 30px;
}

.upload-empty-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border: none;
  color: white;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-empty-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.videos-list {
  display: grid;
  gap: 15px;
}

.video-item {
  display: flex;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-item:hover {
  background: rgba(255,255,255,0.1);
  transform: translateY(-2px);
}

.video-cover {
  position: relative;
  width: 120px;
  height: 80px;
  flex-shrink: 0;
}

.video-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.processing-content {
  text-align: center;
  padding: 10px;
}

.processing-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 8px;
}

.processing-text {
  font-size: 0.8rem;
  color: #4CAF50;
  margin: 0 0 8px 0;
}

.processing-progress {
  width: 80px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255,255,255,0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: #4CAF50;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.7rem;
  color: #4CAF50;
}

.processing-details {
  width: 100%;
}

.current-step {
  font-size: 0.7rem;
  color: #4CAF50;
  margin: 4px 0 0 0;
  text-align: center;
  opacity: 0.9;
}

.video-info {
  flex: 1;
  padding: 12px 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.video-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.video-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  font-size: 0.8rem;
  color: #888888;
}

.video-date {
  font-size: 0.8rem;
  color: #888888;
}

.status-tags {
  display: flex;
  gap: 8px;
}

.status-tag, .audit-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.status-uploading { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
.status-processing { background: rgba(33, 150, 243, 0.2); color: #2196F3; }
.status-published { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.status-draft { background: rgba(158, 158, 158, 0.2); color: #9E9E9E; }

.audit-pending { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
.audit-approved { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.audit-rejected { background: rgba(244, 67, 54, 0.2); color: #F44336; }

.video-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 12px;
  gap: 8px;
}

.action-btn {
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255,255,255,0.2);
}

.action-btn.delete:hover {
  background: rgba(244, 67, 54, 0.2);
  border-color: #F44336;
  color: #F44336;
}

.load-more {
  text-align: center;
  padding: 20px;
}

.load-more-btn {
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  color: white;
  padding: 12px 30px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  background: rgba(255,255,255,0.2);
}

@media (max-width: 480px) {
  .video-item {
    flex-direction: column;
  }
  
  .video-cover {
    width: 100%;
    height: 200px;
  }
  
  .video-actions {
    flex-direction: row;
    justify-content: flex-end;
    padding: 12px 15px;
  }
}
</style>
