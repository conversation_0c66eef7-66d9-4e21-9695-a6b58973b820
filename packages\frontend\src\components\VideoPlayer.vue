<template>
  <div class="video-player-container">
    <video
      ref="videoElement"
      class="video-element"
      :autoplay="autoplay"
      :muted="muted"
      :loop="loop"
      :poster="poster"
      playsinline
      webkit-playsinline
    ></video>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'

interface Props {
  src: string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  poster?: string
  playerId?: string
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: false,
  muted: false,
  loop: false,
  playerId: 'clean-video-player'
})

const emit = defineEmits<{
  play: []
  pause: []
  ended: []
  timeupdate: [time: number]
  loadstart: []
  canplay: []
  error: [error: any]
  ready: []
}>()

const videoElement = ref<HTMLVideoElement>()
let player: any = null

const initPlayer = () => {
  if (!videoElement.value || !props.src) return

  try {
    console.log('🎬 初始化Video.js播放器...')

    const options = {
      controls: false,
      autoplay: props.autoplay,
      muted: props.muted,
      loop: props.loop,
      preload: 'metadata',
      poster: props.poster,
      fluid: false,
      responsive: false,
      fill: true,
      width: '100%',
      height: '100%',
      playsinline: true,
      html5: {
        vhs: {
          overrideNative: true,
          enableLowInitialPlaylist: true,
          smoothQualityChange: true,
          allowSeeksWithinUnsafeLiveWindow: true,
          xhr: {
            beforeRequest: function(options: any) {
              console.log('🎯 VHS拦截请求:', options.uri)

              // 强制修复URL
              if (options.uri.startsWith('http://api/')) {
                options.uri = options.uri.replace('http://api/', 'http://localhost:3000/api/')
                console.log('🔧 VHS URL强制修复:', options.uri)
              }

              // 强制添加认证头 - 无条件添加
              options.headers = options.headers || {}
              options.headers['X-API-Key'] = 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+'
              options.headers['Authorization'] = 'Bearer ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+'
              options.headers['Cache-Control'] = 'no-cache'
              options.headers['Accept'] = '*/*'

              console.log('🔑 VHS强制添加认证头:', options.uri)
              console.log('📋 VHS请求头:', options.headers)

              return options
            },
            onError: function(error: any) {
              console.error('🚨 VHS请求错误:', error)
              return error
            }
          }
        }
      },
      sources: [{
        src: props.src,
        type: props.src.includes('.m3u8') ? 'application/x-mpegURL' : 'video/mp4'
      }]
    }

    player = videojs(videoElement.value, options)

    player.ready(() => {
      console.log('✅ 播放器就绪')
      emit('ready')
    })

    player.on('play', () => emit('play'))
    player.on('pause', () => emit('pause'))
    player.on('ended', () => emit('ended'))
    player.on('timeupdate', () => emit('timeupdate', player.currentTime()))
    player.on('loadstart', () => emit('loadstart'))
    player.on('canplay', () => emit('canplay'))
    
    player.on('error', (error: any) => {
      console.error('❌ 播放器错误:', error)
      emit('error', error)
    })

  } catch (error) {
    console.error('❌ 播放器初始化失败:', error)
    emit('error', error)
  }
}

const destroyPlayer = () => {
  if (player) {
    try {
      player.dispose()
      player = null
      console.log('🗑️ 播放器已销毁')
    } catch (error) {
      console.error('❌ 销毁播放器失败:', error)
    }
  }
}

const play = async () => {
  if (player) {
    try {
      await player.play()
    } catch (error) {
      console.error('❌ 播放失败:', error)
    }
  }
}

const pause = () => {
  if (player) {
    player.pause()
  }
}

const seek = (time: number) => {
  if (player) {
    player.currentTime(time)
  }
}

const setVolume = (volume: number) => {
  if (player) {
    player.volume(volume)
  }
}

const getCurrentTime = () => {
  return player ? player.currentTime() : 0
}

const getDuration = () => {
  return player ? player.duration() : 0
}

const isPaused = () => {
  return player ? player.paused() : true
}

watch(() => props.src, (newSrc) => {
  if (newSrc && player) {
    player.src({
      src: newSrc,
      type: newSrc.includes('.m3u8') ? 'application/x-mpegURL' : 'video/mp4'
    })
  }
})

onMounted(() => {
  initPlayer()
})

onUnmounted(() => {
  destroyPlayer()
})

defineExpose({
  play,
  pause,
  seek,
  setVolume,
  getCurrentTime,
  getDuration,
  isPaused,
  player: () => player
})
</script>

<style scoped>
.video-player-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

:deep(.video-js) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  background: #000 !important;
}

:deep(.video-js video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
}

:deep(.vjs-tech) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
}

:deep(.vjs-control-bar) {
  display: none !important;
}

:deep(.vjs-big-play-button) {
  display: none !important;
}

:deep(.vjs-loading-spinner) {
  display: none !important;
}

:deep(.vjs-poster) {
  display: none !important;
}

:deep(.vjs-error-display) {
  display: none !important;
}
</style>
