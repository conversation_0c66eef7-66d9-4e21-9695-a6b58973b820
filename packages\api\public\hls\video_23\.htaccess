# HLS文件MIME类型配置
AddType video/mp2t .ts
AddType application/vnd.apple.mpegurl .m3u8
AddType application/x-mpegURL .m3u8

# 强制设置MIME类型
<FilesMatch "\.ts$">
    ForceType video/mp2t
</FilesMatch>

<FilesMatch "\.m3u8$">
    ForceType application/vnd.apple.mpegurl
</FilesMatch>

# CORS配置
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key, Range"
Header always set Access-Control-Max-Age "86400"

# 处理OPTIONS预检请求
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
