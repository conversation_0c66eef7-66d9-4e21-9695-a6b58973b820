// 🚀 立即安装全局API拦截器 - 必须在所有其他导入之前
import './utils/apiInterceptor'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// 导入错误边界组件
import ErrorBoundary from '@/components/ErrorBoundary.vue'

const app = createApp(App)

// 全局注册错误边界组件
app.component('ErrorBoundary', ErrorBoundary)

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('全局错误捕获:', err, info)

  // 上报错误到监控系统
  if (import.meta.env.PROD) {
    // 这里可以集成错误监控服务
    console.log('上报错误到监控系统:', { err, info })
  }
}

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  if (import.meta.env.DEV) {
    console.warn('Vue警告:', msg, trace)
  }
}

app.use(createPinia())
app.use(router)

// 简化的路由监控
router.beforeEach((to, from, next) => {
  const startTime = performance.now()
  to.meta.routeStartTime = startTime
  next()
})

router.afterEach((to, from) => {
  const endTime = performance.now()
  const startTime = to.meta.routeStartTime as number

  if (startTime) {
    const routeChangeTime = endTime - startTime
    console.log(`路由切换耗时: ${routeChangeTime.toFixed(2)}ms (${from.path} -> ${to.path})`)
  }
})

app.mount('#app')

// 开发环境下的简化提示
if (import.meta.env.DEV) {
  console.log('🚀 应用已启动')
}
