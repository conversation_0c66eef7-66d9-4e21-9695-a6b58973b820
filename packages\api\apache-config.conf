<VirtualHost *:80>
    DocumentRoot /var/www/html/public

    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted

        # URL重写规则
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>

    # HLS文件MIME类型配置
    AddType video/mp2t .ts
    AddType application/vnd.apple.mpegurl .m3u8
    AddType application/x-mpegURL .m3u8

    # CORS配置 - 为静态文件（包括HLS文件）添加CORS头
    <Directory /var/www/html/public/hls>
        # 强制设置HLS文件的MIME类型
        <FilesMatch "\.ts$">
            ForceType video/mp2t
        </FilesMatch>
        <FilesMatch "\.m3u8$">
            ForceType application/vnd.apple.mpegurl
        </FilesMatch>

        # 允许跨域访问HLS文件
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key, Range"
        Header always set Access-Control-Max-Age "86400"

        # 处理OPTIONS预检请求
        RewriteEngine On
        RewriteCond %{REQUEST_METHOD} OPTIONS
        RewriteRule ^(.*)$ $1 [R=200,L]
    </Directory>

    # 为上传的文件也添加CORS支持
    <Directory /var/www/html/public/uploads>
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
    </Directory>

    # 为存储的视频文件添加CORS支持
    <Directory /var/www/html/public/storage>
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
    </Directory>

    # 为API接口添加CORS支持
    <LocationMatch "^/api/">
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key, X-Request-ID, X-Client-Version, X-Client-Platform"
        Header always set Access-Control-Allow-Credentials "false"
        Header always set Access-Control-Max-Age "86400"
    </LocationMatch>
    
    # 错误日志
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
    
    # PHP配置
    php_value upload_max_filesize 500M
    php_value post_max_size 500M
    php_value max_execution_time 300
    php_value memory_limit 512M
</VirtualHost>
