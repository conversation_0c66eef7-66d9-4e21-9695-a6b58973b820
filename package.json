{"name": "zhengshiban-video-platform", "version": "1.0.0", "description": "正式版视频平台 - Monore<PERSON>", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "npm run build --workspaces", "dev": "npm run dev --workspaces", "clean": "npm run clean --workspaces", "install:all": "npm install --workspaces", "build:shared": "npm run build --workspace=packages/shared-config", "build:frontend": "npm run build --workspace=packages/frontend", "build:admin": "npm run build --workspace=packages/admin", "dev:frontend": "npm run dev --workspace=packages/frontend", "dev:admin": "npm run dev --workspace=packages/admin", "test": "npm run test --workspaces --if-present", "lint": "npm run lint --workspaces --if-present", "type-check": "npm run type-check --workspaces --if-present", "env:switch": "node scripts/env-manager.js switch", "env:dev": "node scripts/env-manager.js switch development --restart", "env:staging": "node scripts/env-manager.js switch staging --restart", "env:prod": "node scripts/env-manager.js switch production --backup --restart", "env:status": "node scripts/env-manager.js status", "env:health": "node scripts/env-manager.js health", "env:init": "node scripts/env-manager.js init", "env:backup": "node scripts/env-manager.js backup", "env:validate": "node scripts/env-manager.js validate", "env:logs": "node scripts/env-manager.js logs", "start": "node scripts/env-manager.js start", "start:dev": "node scripts/env-manager.js start development", "start:prod": "node scripts/env-manager.js start production", "stop": "node scripts/env-manager.js stop", "restart": "node scripts/env-manager.js restart", "restart:dev": "node scripts/env-manager.js restart development", "restart:prod": "node scripts/env-manager.js restart production", "logs": "node scripts/env-manager.js logs", "logs:api": "node scripts/env-manager.js logs api", "logs:frontend": "node scripts/env-manager.js logs frontend", "logs:admin": "node scripts/env-manager.js logs admin", "logs:nginx": "node scripts/env-manager.js logs nginx"}, "devDependencies": {"@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "rimraf": "^5.0.5", "terser": "^5.43.1", "typescript": "~5.7.2", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.28.0", "vite": "^6.0.3", "vue": "^3.5.13", "vue-tsc": "^2.1.10"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["video-platform", "vue3", "typescript", "monorepo", "thinkphp"], "author": "Video Platform Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/zhengshiban-video-platform.git"}}