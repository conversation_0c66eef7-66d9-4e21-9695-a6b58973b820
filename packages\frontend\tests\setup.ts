/**
 * 🧪 测试环境配置
 * 
 * 为企业级视频平台配置完整的测试环境
 * 
 * <AUTHOR> Video Platform Team
 * @version 2.0.0
 */

import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// 全局测试配置
beforeEach(() => {
  // 清理所有模拟
  vi.clearAllMocks()
  
  // 重置DOM
  document.body.innerHTML = ''
  
  // 重置localStorage
  localStorage.clear()
  sessionStorage.clear()
})

// Vue Test Utils 全局配置
config.global.mocks = {
  $t: (key: string) => key, // 模拟国际化
  $route: {
    path: '/',
    params: {},
    query: {},
    meta: {}
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }
}

// 模拟环境变量
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_API_BASE_URL: 'http://localhost:3000',
    VITE_APP_ENV: 'test',
    VITE_API_KEY_USER: 'test-api-key',
    MODE: 'test'
  },
  writable: true
})

// 模拟Web APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// 模拟IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟PerformanceObserver
global.PerformanceObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟fetch
global.fetch = vi.fn()

// 模拟navigator
Object.defineProperty(navigator, 'sendBeacon', {
  writable: true,
  value: vi.fn().mockReturnValue(true),
})

Object.defineProperty(navigator, 'connection', {
  writable: true,
  value: {
    effectiveType: '4g',
    downlink: 10,
    rtt: 100,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  },
})

// 模拟HTMLMediaElement
Object.defineProperty(HTMLMediaElement.prototype, 'play', {
  writable: true,
  value: vi.fn().mockResolvedValue(undefined),
})

Object.defineProperty(HTMLMediaElement.prototype, 'pause', {
  writable: true,
  value: vi.fn(),
})

Object.defineProperty(HTMLMediaElement.prototype, 'load', {
  writable: true,
  value: vi.fn(),
})

// 模拟视频相关属性
Object.defineProperty(HTMLVideoElement.prototype, 'videoWidth', {
  writable: true,
  value: 1920,
})

Object.defineProperty(HTMLVideoElement.prototype, 'videoHeight', {
  writable: true,
  value: 1080,
})

// 模拟HLS.js
vi.mock('hls.js', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      loadSource: vi.fn(),
      attachMedia: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      destroy: vi.fn(),
      isSupported: vi.fn().mockReturnValue(true),
    })),
    isSupported: vi.fn().mockReturnValue(true),
  }
})

// 模拟性能监控
vi.mock('@/utils/advanced-performance', () => ({
  advancedPerformanceMonitor: {
    startMeasure: vi.fn(),
    endMeasure: vi.fn(),
    recordCustomMetric: vi.fn(),
    getMetrics: vi.fn().mockReturnValue({}),
  }
}))

// 模拟认证服务
vi.mock('@/utils/auth', () => ({
  authService: {
    login: vi.fn(),
    logout: vi.fn(),
    isAuthenticated: vi.fn().mockReturnValue(false),
    getAccessToken: vi.fn().mockReturnValue(null),
    getUserInfo: vi.fn().mockReturnValue(null),
    hasPermission: vi.fn().mockReturnValue(false),
    hasRole: vi.fn().mockReturnValue(false),
    getAuthHeaders: vi.fn().mockReturnValue({}),
  }
}))

// 测试工具函数
export const createMockVideo = (overrides = {}) => {
  return {
    id: 1,
    title: 'Test Video',
    description: 'Test Description',
    url: 'https://example.com/video.mp4',
    thumbnail: 'https://example.com/thumb.jpg',
    duration: 120,
    views: 1000,
    likes: 50,
    created_at: '2024-01-01T00:00:00Z',
    ...overrides
  }
}

export const createMockUser = (overrides = {}) => {
  return {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
    role: 'user',
    permissions: ['video.view'],
    ...overrides
  }
}

export const createMockApiResponse = (data: any, success = true) => {
  return {
    code: success ? 200 : 400,
    message: success ? 'Success' : 'Error',
    data,
    success
  }
}

// 等待函数
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 触发事件的工具函数
export const triggerEvent = (element: Element, eventType: string, eventInit = {}) => {
  const event = new Event(eventType, { bubbles: true, ...eventInit })
  element.dispatchEvent(event)
}

// 模拟网络延迟
export const mockNetworkDelay = (ms = 100) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 清理函数
afterEach(() => {
  // 清理定时器
  vi.clearAllTimers()
  
  // 清理事件监听器
  document.removeAllListeners?.()
  
  // 重置模拟状态
  vi.resetAllMocks()
})

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Test error:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
})

console.log('🧪 测试环境配置完成')
