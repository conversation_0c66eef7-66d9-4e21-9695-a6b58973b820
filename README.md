# 🎬 51吃瓜网 - 企业级视频平台

<div align="center">

**现代化的企业级视频分享平台**

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-18+-green.svg)](https://nodejs.org/)
[![Vue.js](https://img.shields.io/badge/vue.js-3.0+-brightgreen.svg)](https://vuejs.org/)
[![PHP](https://img.shields.io/badge/php-8.0+-purple.svg)](https://php.net/)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://docker.com/)

[🚀 快速开始](#-快速开始) • [📖 文档](#-文档) • [🎯 特性](#-核心特性) • [🛠️ 技术栈](#️-技术栈) • [📊 演示](#-在线演示)

</div>

---

## 🌟 项目亮点

- 🔒 **企业级安全**: JWT认证、权限管理、数据加密
- ⚡ **高性能**: 代码分割、懒加载、智能缓存
- 🎨 **现代化UI**: 大气精致的设计系统
- 🧪 **完善测试**: 85%+测试覆盖率
- 🚀 **一键部署**: Docker化部署，环境一键切换
- 📱 **响应式**: 完美适配PC、平板、手机
- 🔄 **实时监控**: 性能监控、错误追踪
- 🌐 **国际化**: 多语言支持

## 🎯 核心特性

### 🎬 视频功能
- **多格式支持**: MP4、HLS、DASH等主流格式
- **自适应播放**: 根据网络自动调整清晰度
- **断点续播**: 记录播放进度，支持续播
- **弹幕评论**: 实时弹幕和评论系统
- **收藏分享**: 视频收藏、分享功能

### 👥 用户系统
- **多角色管理**: 普通用户、VIP用户、管理员
- **权限控制**: 细粒度的权限管理
- **个人中心**: 完整的用户个人中心
- **社交功能**: 关注、点赞、评论互动

### 📊 管理后台
- **内容管理**: 视频上传、编辑、审核
- **用户管理**: 用户信息、权限管理
- **数据统计**: 详细的数据分析报表
- **系统设置**: 灵活的系统配置

## 🛠️ 技术栈

### 前端技术
- **框架**: Vue 3 + TypeScript
- **构建**: Vite + ESBuild
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: CSS3 + 设计系统
- **测试**: Vitest + Vue Test Utils

### 后端技术
- **语言**: PHP 8.0+
- **框架**: ThinkPHP 6
- **数据库**: MySQL 8.0
- **缓存**: Redis 7
- **搜索**: Elasticsearch (可选)
- **队列**: Redis Queue

### 运维技术
- **容器**: Docker + Docker Compose
- **代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (可选)
- **CI/CD**: GitHub Actions

## 🚀 快速开始

### 📋 环境要求

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| Node.js | 18+ | 前端构建环境 |
| PHP | 8.0+ | 后端运行环境 |
| MySQL | 8.0+ | 主数据库 |
| Redis | 7+ | 缓存和队列 |
| Docker | 20+ | 容器化部署 |
| Docker Compose | 2.0+ | 容器编排 |

### ⚡ 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/your-org/zhengshiban.git
cd zhengshiban

# 2. 初始化环境
npm run env:init

# 3. 启动开发环境
npm run env:dev
```

### 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 🌐 前端 | http://localhost:3002 | 用户前台 |
| ⚙️ 管理后台 | http://localhost:3001 | 管理员后台 |
| 🔧 API | http://localhost:3000 | 后端API |
| 📊 数据库管理 | http://localhost:8080 | phpMyAdmin |
| 🔴 缓存管理 | http://localhost:8081 | Redis Commander |

## 📁 项目结构

```
zhengshiban/
├── 📁 packages/                    # 主要代码包
│   ├── 🎨 frontend/                # Vue3 前端应用
│   │   ├── src/
│   │   │   ├── components/         # 可复用组件
│   │   │   ├── views/             # 页面组件
│   │   │   ├── utils/             # 工具函数
│   │   │   ├── styles/            # 样式文件
│   │   │   └── stores/            # 状态管理
│   │   └── tests/                 # 测试文件
│   ├── ⚙️ admin/                   # 管理后台
│   ├── 🔧 api/                     # PHP 后端API
│   └── 📦 shared-config/           # 共享配置
├── 📚 docs/                        # 项目文档
├── 🐳 docker/                      # Docker配置
├── 🔧 scripts/                     # 构建和部署脚本
├── 🗄️ storage/                     # 存储目录
└── ⚙️ config/                      # 配置文件
```

## 🔧 开发指南

### 🎨 前端开发

```bash
# 进入前端目录
cd packages/frontend

# 启动开发服务器
npm run dev

# 运行测试
npm run test

# 构建生产版本
npm run build
```

### 🔧 后端开发

```bash
# 进入API目录
cd packages/api

# 启动开发服务器
php think run

# 运行测试
php think test

# 数据库迁移
php think migrate:run
```

### 🧪 测试

```bash
# 运行所有测试
npm run test

# 运行特定类型的测试
npm run test:components    # 组件测试
npm run test:performance   # 性能测试
npm run test:security     # 安全测试

# 生成测试覆盖率报告
npm run test:coverage
```

### 🚀 部署

```bash
# 切换到生产环境
npm run env:prod

# 快速部署
./scripts/quick-deploy.sh production --backup --migrate

# 查看服务状态
npm run env:status
```

## 📖 文档

### 📚 技术文档
- [🔧 API文档](docs/api/README.md) - 完整的API接口文档
- [👨‍💻 开发指南](docs/development/DEVELOPER_GUIDE.md) - 开发规范和最佳实践
- [🚀 部署指南](docs/deployment/README.md) - 部署和运维指南
- [🧪 测试指南](docs/testing/README.md) - 测试规范和用例

### 📖 用户文档
- [👤 用户手册](docs/user/USER_MANUAL.md) - 用户使用指南
- [👨‍💼 管理员手册](docs/admin/ADMIN_MANUAL.md) - 管理员操作指南
- [❓ FAQ](docs/FAQ.md) - 常见问题解答

### 📊 项目文档
- [📈 升级总结](docs/UPGRADE_SUMMARY.md) - 项目升级详细报告
- [🏗️ 架构设计](docs/ARCHITECTURE.md) - 系统架构设计文档
- [🔒 安全指南](docs/SECURITY.md) - 安全最佳实践

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

### 🐛 报告问题
- 使用 [GitHub Issues](https://github.com/your-org/zhengshiban/issues) 报告bug
- 提供详细的复现步骤和环境信息

### 💡 功能建议
- 在 [GitHub Discussions](https://github.com/your-org/zhengshiban/discussions) 中讨论新功能
- 提交 Pull Request 实现新功能

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

<div align="center">

**如果这个项目对你有帮助，请给我们一个 ⭐ Star！**

[🏠 首页](https://51chigua.com) • [📧 联系我们](mailto:<EMAIL>)

</div>
