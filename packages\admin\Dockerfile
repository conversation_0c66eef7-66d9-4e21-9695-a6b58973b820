# 开发模式 - Node.js 18
FROM node:18-alpine

# 设置时区
ENV TZ=Asia/Shanghai
RUN apk update && apk add --no-cache tzdata \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && apk del tzdata

# 安装pnpm
RUN npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 开发模式：代码通过volume挂载，不需要复制

# 创建启动脚本
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

EXPOSE 3001

# 使用entrypoint脚本启动开发服务器
ENTRYPOINT ["/entrypoint.sh"]
