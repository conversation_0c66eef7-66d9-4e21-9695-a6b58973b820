<?php
declare(strict_types=1);

namespace app\controller;

use think\Request;
use think\Response;

/**
 * 静态文件控制器
 * 处理视频、图片等静态文件的访问，支持Range请求，CORS由Nginx处理
 */
class Static
{
    /**
     * 处理静态文件请求
     */
    public function serve(Request $request): Response
    {
        $path = $request->param('path', '');
        $requestUri = $request->pathinfo();

        // 调试日志
        error_log("Static file request: " . $requestUri . " -> " . $path);
        error_log("Resolved file path: " . $filePath ?? 'null');

        // 根据请求路径确定文件路径
        if (strpos($requestUri, 'storage/videos/') === 0) {
            $filePath = public_path() . 'storage/videos/' . $path;
        } elseif (strpos($requestUri, 'storage/images/') === 0) {
            $filePath = public_path() . 'storage/images/' . $path;
        } elseif (strpos($requestUri, 'storage/avatars/') === 0) {
            $filePath = public_path() . 'storage/avatars/' . $path;
        } elseif (strpos($requestUri, 'uploads/') === 0) {
            $filePath = public_path() . 'uploads/' . $path;
        } elseif (strpos($requestUri, 'hls/') === 0) {
            $filePath = public_path() . 'hls/' . $path;
        } else {
            $filePath = public_path() . 'storage/' . $path;
        }
        
        // 检查文件是否存在
        if (!file_exists($filePath) || !is_file($filePath)) {
            error_log("File not found: " . $filePath);
            return response('File not found', 404);
        }

        error_log("File found, size: " . filesize($filePath));
        
        // 获取文件信息
        $fileSize = filesize($filePath);
        $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        // 设置Content-Type
        $mimeTypes = [
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'mov' => 'video/quicktime',
            'mkv' => 'video/x-matroska',
            'webm' => 'video/webm',
            'flv' => 'video/x-flv',
            'wmv' => 'video/x-ms-wmv',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon'
        ];
        
        $contentType = $mimeTypes[$fileExtension] ?? 'application/octet-stream';
        
        // 检查是否是Range请求
        $range = $request->header('Range');
        if ($range && strpos($range, 'bytes=') === 0) {
            return $this->handleRangeRequest($filePath, $fileSize, $contentType, $range);
        }
        
        // 普通请求 - 使用流式输出避免内存问题
        $headers = [
            'Content-Type' => $contentType,
            'Content-Length' => $fileSize,
            'Accept-Ranges' => 'bytes',
            'Cache-Control' => 'public, max-age=31536000'
        ];

        // 添加CORS头（开发环境需要）
        $origin = $request->header('Origin', '');
        if (!empty($origin)) {
            // 允许的源
            $allowedOrigins = [
                'http://localhost:3001', 'http://localhost:3002', 'http://127.0.0.1:3001', 'http://127.0.0.1:3002'
            ];

            if (in_array($origin, $allowedOrigins) ||
                preg_match('/^https?:\/\/(localhost|127\.0\.0\.1)(:\d+)?$/', $origin)) {
                $headers['Access-Control-Allow-Origin'] = $origin;
            }
        } else {
            $headers['Access-Control-Allow-Origin'] = '*';
        }

        $headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS';
        $headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-API-Key';

        // 设置响应头
        foreach ($headers as $key => $value) {
            header($key . ': ' . $value);
        }

        // 流式输出文件
        $handle = fopen($filePath, 'rb');
        if ($handle) {
            while (!feof($handle)) {
                echo fread($handle, 8192);
                if (ob_get_level()) {
                    ob_flush();
                    flush();
                }
            }
            fclose($handle);
        }

        exit;
    }
    
    /**
     * 处理Range请求（断点续传）
     */
    private function handleRangeRequest(string $filePath, int $fileSize, string $contentType, string $range): Response
    {
        $ranges = explode('-', substr($range, 6));
        $start = intval($ranges[0]);
        $end = isset($ranges[1]) && $ranges[1] !== '' ? intval($ranges[1]) : $fileSize - 1;
        
        if ($start >= $fileSize || $end >= $fileSize || $start > $end) {
            return response('Requested Range Not Satisfiable', 416)
                ->header('Content-Range', 'bytes */' . $fileSize);
        }
        
        $file = fopen($filePath, 'rb');
        fseek($file, $start);
        
        $content = fread($file, $end - $start + 1);
        fclose($file);
        
        $response = response($content, 206);
        $response->header([
            'Content-Type' => $contentType,
            'Accept-Ranges' => 'bytes',
            'Content-Range' => 'bytes ' . $start . '-' . $end . '/' . $fileSize,
            'Content-Length' => $end - $start + 1,
            'Cache-Control' => 'public, max-age=31536000'
            // CORS由Nginx处理，这里不设置CORS头
        ]);
        
        return $response;
    }

    /**
     * 处理HLS文件请求（专门支持CORS）
     */
    public function serveHLS(Request $request): Response
    {
        $path = $request->param('path', '');
        $filePath = public_path() . 'hls/' . $path;

        // 调试日志
        error_log("HLS file request: " . $path . " -> " . $filePath);

        // 检查文件是否存在
        if (!file_exists($filePath) || !is_file($filePath)) {
            error_log("HLS file not found: " . $filePath);
            return response('File not found', 404);
        }

        // 获取文件信息
        $fileSize = filesize($filePath);
        $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        // 设置HLS相关的Content-Type
        $mimeTypes = [
            'm3u8' => 'application/x-mpegURL',
            'ts' => 'video/MP2T',
            'mp4' => 'video/mp4',
            'key' => 'application/octet-stream'
        ];

        $contentType = $mimeTypes[$fileExtension] ?? 'application/octet-stream';

        // 读取文件内容
        $content = file_get_contents($filePath);

        // 创建响应
        $response = response($content);
        $response->header([
            'Content-Type' => $contentType,
            'Content-Length' => $fileSize,
            'Accept-Ranges' => 'bytes',
            'Cache-Control' => 'public, max-age=3600', // HLS文件缓存时间较短
            // CORS由Nginx处理，这里不设置CORS头
        ]);

        return $response;
    }
}
