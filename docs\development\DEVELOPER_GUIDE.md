# 👨‍💻 开发者指南

> **版本**: v1.0.0  
> **更新时间**: 2025-07-30  
> **适用对象**: 前端开发者、后端开发者、全栈开发者

---

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [项目结构](#项目结构)
- [技术栈](#技术栈)
- [开发规范](#开发规范)
- [API开发](#api开发)
- [前端开发](#前端开发)
- [数据库设计](#数据库设计)
- [测试指南](#测试指南)
- [部署流程](#部署流程)
- [常见问题](#常见问题)

---

## 🛠️ 开发环境搭建

### 系统要求

| 工具 | 版本要求 | 说明 |
|------|----------|------|
| **PHP** | >= 8.2 | 后端运行环境 |
| **Node.js** | >= 18.0 | 前端构建工具 |
| **Composer** | >= 2.0 | PHP包管理器 |
| **npm/pnpm** | 最新版 | 前端包管理器 |
| **MySQL** | >= 8.0 | 数据库 |
| **Redis** | >= 7.0 | 缓存服务 |
| **Git** | >= 2.30 | 版本控制 |

### 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd zhengshiban

# 2. 安装后端依赖
cd packages/api
composer install

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 4. 数据库迁移
php think migrate:run

# 5. 启动后端服务
php think run -H 0.0.0.0 -p 3000

# 6. 安装前端依赖
cd ../admin
npm install
# 或使用 pnpm
pnpm install

# 7. 启动前端服务
npm run dev
```

### IDE配置

#### VS Code推荐插件

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "vue.volar",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bmewburn.vscode-intelephense-client",
    "xdebug.php-debug"
  ]
}
```

#### 代码格式化配置

```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[php]": {
    "editor.defaultFormatter": "bmewburn.vscode-intelephense-client"
  }
}
```

---

## 📁 项目结构

```
zhengshiban/
├── packages/
│   ├── api/                 # 后端API服务
│   │   ├── app/            # 应用核心代码
│   │   │   ├── controller/ # 控制器
│   │   │   ├── model/      # 数据模型
│   │   │   ├── service/    # 业务逻辑
│   │   │   ├── middleware/ # 中间件
│   │   │   └── validate/   # 验证器
│   │   ├── config/         # 配置文件
│   │   ├── database/       # 数据库迁移
│   │   ├── route/          # 路由定义
│   │   └── public/         # 公共资源
│   ├── admin/              # 管理后台
│   │   ├── src/
│   │   │   ├── components/ # Vue组件
│   │   │   ├── views/      # 页面视图
│   │   │   ├── router/     # 路由配置
│   │   │   ├── store/      # 状态管理
│   │   │   ├── api/        # API接口
│   │   │   └── utils/      # 工具函数
│   │   ├── public/         # 静态资源
│   │   └── dist/           # 构建输出
│   └── frontend/           # 用户前端
│       └── ...
├── docs/                   # 项目文档
│   ├── api/               # API文档
│   ├── deployment/        # 部署文档
│   ├── development/       # 开发文档
│   └── project/           # 项目文档
├── docker-compose.yml     # Docker配置
└── README.md              # 项目说明
```

---

## 🔧 技术栈

### 后端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| **PHP** | 8.2+ | 核心语言 |
| **ThinkPHP** | 8.0 | Web框架 |
| **MySQL** | 8.0+ | 主数据库 |
| **Redis** | 7.0+ | 缓存/会话 |
| **JWT** | - | 身份认证 |
| **Composer** | 2.0+ | 依赖管理 |

### 前端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| **Vue.js** | 3.5+ | 前端框架 |
| **TypeScript** | 5.0+ | 类型系统 |
| **Vite** | 5.0+ | 构建工具 |
| **Element Plus** | 2.0+ | UI组件库 |
| **Vue Router** | 4.0+ | 路由管理 |
| **Pinia** | 2.0+ | 状态管理 |
| **Axios** | 1.0+ | HTTP客户端 |

### 开发工具

| 工具 | 用途 |
|------|------|
| **Docker** | 容器化部署 |
| **Git** | 版本控制 |
| **ESLint** | 代码检查 |
| **Prettier** | 代码格式化 |
| **PHPStan** | PHP静态分析 |

---

## 📝 开发规范

### 代码风格

#### PHP代码规范

```php
<?php

namespace app\controller;

use app\service\VideoService;
use think\Request;
use think\Response;

/**
 * 视频控制器
 * 
 * @package app\controller
 */
class VideoController
{
    /**
     * 视频服务
     * 
     * @var VideoService
     */
    private VideoService $videoService;

    /**
     * 构造函数
     * 
     * @param VideoService $videoService
     */
    public function __construct(VideoService $videoService)
    {
        $this->videoService = $videoService;
    }

    /**
     * 获取视频列表
     * 
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $page = $request->param('page', 1);
            $limit = $request->param('limit', 20);
            
            $result = $this->videoService->getVideoList($page, $limit);
            
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => $e->getMessage()
            ]);
        }
    }
}
```

#### Vue/TypeScript代码规范

```vue
<template>
  <div class="video-list">
    <div class="video-list__header">
      <h2 class="video-list__title">{{ title }}</h2>
      <el-button 
        type="primary" 
        @click="handleRefresh"
        :loading="loading"
      >
        刷新
      </el-button>
    </div>
    
    <div class="video-list__content">
      <video-card
        v-for="video in videoList"
        :key="video.id"
        :video="video"
        @click="handleVideoClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import VideoCard from '@/components/VideoCard.vue'
import { getVideoList } from '@/api/video'
import type { Video } from '@/types/video'

// Props定义
interface Props {
  title?: string
  categoryId?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: '视频列表',
  categoryId: 0
})

// 响应式数据
const loading = ref(false)
const videoList = ref<Video[]>([])

// 方法定义
const fetchVideoList = async (): Promise<void> => {
  try {
    loading.value = true
    const { data } = await getVideoList({
      categoryId: props.categoryId,
      page: 1,
      limit: 20
    })
    videoList.value = data.list
  } catch (error) {
    ElMessage.error('获取视频列表失败')
    console.error('Error fetching video list:', error)
  } finally {
    loading.value = false
  }
}

const handleRefresh = (): void => {
  fetchVideoList()
}

const handleVideoClick = (video: Video): void => {
  // 处理视频点击事件
  console.log('Video clicked:', video)
}

// 生命周期
onMounted(() => {
  fetchVideoList()
})
</script>

<style scoped>
.video-list {
  @apply p-4;
}

.video-list__header {
  @apply flex justify-between items-center mb-4;
}

.video-list__title {
  @apply text-2xl font-bold text-gray-800;
}

.video-list__content {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
}
</style>
```

### 命名规范

#### 文件命名

- **PHP文件**: PascalCase (如: `VideoController.php`)
- **Vue组件**: PascalCase (如: `VideoCard.vue`)
- **TypeScript文件**: camelCase (如: `videoApi.ts`)
- **样式文件**: kebab-case (如: `video-card.scss`)

#### 变量命名

- **PHP**: camelCase (如: `$videoList`)
- **JavaScript/TypeScript**: camelCase (如: `videoList`)
- **CSS类名**: kebab-case (如: `.video-card`)
- **常量**: UPPER_SNAKE_CASE (如: `MAX_FILE_SIZE`)

### Git提交规范

```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式化
refactor: 重构代码
test:     测试相关
chore:    构建/工具相关

# 示例
feat(video): 添加视频上传功能
fix(auth): 修复登录token过期问题
docs(api): 更新API文档
```

---

## 🔌 API开发

### RESTful API设计

```php
// 路由定义
Route::group('api/v1', function () {
    // 认证相关
    Route::post('auth/login', 'AuthController@login');
    Route::post('auth/register', 'AuthController@register');
    Route::post('auth/logout', 'AuthController@logout');
    
    // 视频相关
    Route::group('videos', function () {
        Route::get('', 'VideoController@index');        // GET /api/v1/videos
        Route::post('', 'VideoController@store');       // POST /api/v1/videos
        Route::get(':id', 'VideoController@show');      // GET /api/v1/videos/1
        Route::put(':id', 'VideoController@update');    // PUT /api/v1/videos/1
        Route::delete(':id', 'VideoController@delete'); // DELETE /api/v1/videos/1
    });
    
    // 用户相关
    Route::group('users', function () {
        Route::get('profile', 'UserController@profile');
        Route::put('profile', 'UserController@updateProfile');
    });
})->middleware(['cors', 'auth']);
```

### 响应格式标准

```php
// 成功响应
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [...],
        "total": 100,
        "page": 1,
        "limit": 20
    }
}

// 错误响应
{
    "code": 400,
    "message": "参数错误",
    "errors": {
        "email": ["邮箱格式不正确"]
    }
}
```

### 中间件开发

```php
<?php

namespace app\middleware;

use think\Request;
use think\Response;
use think\facade\Cache;

/**
 * API频率限制中间件
 */
class RateLimit
{
    /**
     * 处理请求
     * 
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        $key = 'rate_limit:' . $request->ip();
        $requests = Cache::inc($key);
        
        if ($requests === 1) {
            Cache::expire($key, 60); // 1分钟窗口
        }
        
        if ($requests > 100) { // 每分钟最多100次请求
            return json([
                'code' => 429,
                'message' => '请求过于频繁，请稍后再试'
            ], 429);
        }
        
        return $next($request);
    }
}
```

---

## 🎨 前端开发

### 组件开发规范

```vue
<!-- VideoCard.vue -->
<template>
  <div class="video-card" @click="handleClick">
    <div class="video-card__thumbnail">
      <img 
        :src="video.thumbnail" 
        :alt="video.title"
        @error="handleImageError"
      >
      <div class="video-card__duration">{{ formatDuration(video.duration) }}</div>
    </div>
    
    <div class="video-card__content">
      <h3 class="video-card__title">{{ video.title }}</h3>
      <p class="video-card__description">{{ video.description }}</p>
      <div class="video-card__meta">
        <span class="video-card__views">{{ formatViews(video.views) }} 次观看</span>
        <span class="video-card__date">{{ formatDate(video.created_at) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Video } from '@/types/video'
import { formatDuration, formatViews, formatDate } from '@/utils/format'

// Props
interface Props {
  video: Video
}

const props = defineProps<Props>()

// Emits
interface Emits {
  click: [video: Video]
}

const emit = defineEmits<Emits>()

// Methods
const handleClick = (): void => {
  emit('click', props.video)
}

const handleImageError = (event: Event): void => {
  const target = event.target as HTMLImageElement
  target.src = '/images/default-thumbnail.jpg'
}
</script>

<style scoped>
.video-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden cursor-pointer transition-transform hover:scale-105;
}

.video-card__thumbnail {
  @apply relative;
}

.video-card__thumbnail img {
  @apply w-full h-48 object-cover;
}

.video-card__duration {
  @apply absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded;
}

.video-card__content {
  @apply p-4;
}

.video-card__title {
  @apply text-lg font-semibold text-gray-800 mb-2 line-clamp-2;
}

.video-card__description {
  @apply text-gray-600 text-sm mb-3 line-clamp-3;
}

.video-card__meta {
  @apply flex justify-between items-center text-xs text-gray-500;
}
</style>
```

### 状态管理 (Pinia)

```typescript
// stores/video.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Video, VideoListParams } from '@/types/video'
import { getVideoList, getVideoDetail } from '@/api/video'

export const useVideoStore = defineStore('video', () => {
  // State
  const videoList = ref<Video[]>([])
  const currentVideo = ref<Video | null>(null)
  const loading = ref(false)
  const total = ref(0)
  const page = ref(1)
  const limit = ref(20)

  // Getters
  const hasMore = computed(() => videoList.value.length < total.value)
  const isEmpty = computed(() => videoList.value.length === 0)

  // Actions
  const fetchVideoList = async (params: VideoListParams = {}) => {
    try {
      loading.value = true
      const { data } = await getVideoList({
        page: page.value,
        limit: limit.value,
        ...params
      })
      
      if (page.value === 1) {
        videoList.value = data.list
      } else {
        videoList.value.push(...data.list)
      }
      
      total.value = data.total
    } catch (error) {
      console.error('Failed to fetch video list:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchVideoDetail = async (id: number) => {
    try {
      const { data } = await getVideoDetail(id)
      currentVideo.value = data
      return data
    } catch (error) {
      console.error('Failed to fetch video detail:', error)
      throw error
    }
  }

  const loadMore = async () => {
    if (hasMore.value && !loading.value) {
      page.value++
      await fetchVideoList()
    }
  }

  const reset = () => {
    videoList.value = []
    currentVideo.value = null
    page.value = 1
    total.value = 0
  }

  return {
    // State
    videoList,
    currentVideo,
    loading,
    total,
    page,
    limit,
    
    // Getters
    hasMore,
    isEmpty,
    
    // Actions
    fetchVideoList,
    fetchVideoDetail,
    loadMore,
    reset
  }
})
```

### API封装

```typescript
// api/video.ts
import request from '@/utils/request'
import type { Video, VideoListParams, VideoListResponse } from '@/types/video'

/**
 * 获取视频列表
 */
export const getVideoList = (params: VideoListParams): Promise<VideoListResponse> => {
  return request.get('/videos', { params })
}

/**
 * 获取视频详情
 */
export const getVideoDetail = (id: number): Promise<{ data: Video }> => {
  return request.get(`/videos/${id}`)
}

/**
 * 创建视频
 */
export const createVideo = (data: Partial<Video>): Promise<{ data: Video }> => {
  return request.post('/videos', data)
}

/**
 * 更新视频
 */
export const updateVideo = (id: number, data: Partial<Video>): Promise<{ data: Video }> => {
  return request.put(`/videos/${id}`, data)
}

/**
 * 删除视频
 */
export const deleteVideo = (id: number): Promise<void> => {
  return request.delete(`/videos/${id}`)
}

/**
 * 上传视频
 */
export const uploadVideo = (file: File, onProgress?: (progress: number) => void): Promise<{ data: { url: string } }> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post('/videos/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}
```

---

## 🗄️ 数据库设计

### 表结构设计

```sql
-- 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 0:禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 视频表
CREATE TABLE `videos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '标题',
  `description` text COMMENT '描述',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT '缩略图',
  `video_url` varchar(255) NOT NULL COMMENT '视频地址',
  `duration` int(11) DEFAULT NULL COMMENT '时长(秒)',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `user_id` int(11) NOT NULL COMMENT '上传用户ID',
  `views` int(11) DEFAULT 0 COMMENT '观看次数',
  `likes` int(11) DEFAULT 0 COMMENT '点赞数',
  `status` enum('draft','published','private','deleted') DEFAULT 'draft' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_videos_category_id` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_videos_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频表';
```

### 数据库迁移

```php
<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateVideosTable extends Migrator
{
    /**
     * 创建表
     */
    public function up()
    {
        $table = $this->table('videos', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '视频表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('title', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '标题'
        ])
        ->addColumn('description', 'text', [
            'null' => true,
            'comment' => '描述'
        ])
        ->addColumn('thumbnail', 'string', [
            'limit' => 255,
            'null' => true,
            'comment' => '缩略图'
        ])
        ->addColumn('video_url', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '视频地址'
        ])
        ->addColumn('duration', 'integer', [
            'null' => true,
            'comment' => '时长(秒)'
        ])
        ->addColumn('file_size', 'biginteger', [
            'null' => true,
            'comment' => '文件大小(字节)'
        ])
        ->addColumn('category_id', 'integer', [
            'null' => true,
            'comment' => '分类ID'
        ])
        ->addColumn('user_id', 'integer', [
            'null' => false,
            'comment' => '上传用户ID'
        ])
        ->addColumn('views', 'integer', [
            'default' => 0,
            'comment' => '观看次数'
        ])
        ->addColumn('likes', 'integer', [
            'default' => 0,
            'comment' => '点赞数'
        ])
        ->addColumn('status', 'enum', [
            'values' => ['draft', 'published', 'private', 'deleted'],
            'default' => 'draft',
            'comment' => '状态'
        ])
        ->addColumn('created_at', 'timestamp', [
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['category_id'], ['name' => 'idx_category_id'])
        ->addIndex(['user_id'], ['name' => 'idx_user_id'])
        ->addIndex(['status'], ['name' => 'idx_status'])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->create();
    }
    
    /**
     * 删除表
     */
    public function down()
    {
        $this->dropTable('videos');
    }
}
```

---

## 🧪 测试指南

### 单元测试

```php
<?php

namespace tests\unit;

use PHPUnit\Framework\TestCase;
use app\service\VideoService;
use app\model\Video;

class VideoServiceTest extends TestCase
{
    private VideoService $videoService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->videoService = new VideoService();
    }
    
    /**
     * 测试获取视频列表
     */
    public function testGetVideoList()
    {
        $result = $this->videoService->getVideoList(1, 10);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertLessThanOrEqual(10, count($result['list']));
    }
    
    /**
     * 测试创建视频
     */
    public function testCreateVideo()
    {
        $data = [
            'title' => '测试视频',
            'description' => '这是一个测试视频',
            'video_url' => 'http://example.com/test.mp4',
            'user_id' => 1
        ];
        
        $video = $this->videoService->createVideo($data);
        
        $this->assertInstanceOf(Video::class, $video);
        $this->assertEquals($data['title'], $video->title);
        $this->assertEquals($data['description'], $video->description);
    }
}
```

### 前端测试

```typescript
// tests/components/VideoCard.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import VideoCard from '@/components/VideoCard.vue'
import type { Video } from '@/types/video'

const mockVideo: Video = {
  id: 1,
  title: '测试视频',
  description: '这是一个测试视频',
  thumbnail: 'http://example.com/thumbnail.jpg',
  video_url: 'http://example.com/video.mp4',
  duration: 120,
  views: 1000,
  created_at: '2025-01-01 00:00:00'
}

describe('VideoCard', () => {
  it('renders video information correctly', () => {
    const wrapper = mount(VideoCard, {
      props: {
        video: mockVideo
      }
    })
    
    expect(wrapper.find('.video-card__title').text()).toBe(mockVideo.title)
    expect(wrapper.find('.video-card__description').text()).toBe(mockVideo.description)
    expect(wrapper.find('img').attributes('src')).toBe(mockVideo.thumbnail)
  })
  
  it('emits click event when clicked', async () => {
    const wrapper = mount(VideoCard, {
      props: {
        video: mockVideo
      }
    })
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')?.[0]).toEqual([mockVideo])
  })
})
```

---

## 🚀 部署流程

### 开发环境部署

```bash
# 1. 启动开发服务
docker-compose up -d

# 2. 运行数据库迁移
docker-compose exec shipin-api php think migrate:run

# 3. 安装前端依赖
cd packages/admin && npm install

# 4. 启动前端开发服务
npm run dev
```

### 生产环境部署

```bash
# 1. 构建前端
cd packages/admin
npm run build

# 2. 构建Docker镜像
docker build -t shipin-api:latest -f docker/api/Dockerfile .
docker build -t shipin-admin:latest -f docker/admin/Dockerfile .

# 3. 部署到生产环境
docker-compose -f docker-compose.production.yml up -d

# 4. 运行数据库迁移
docker-compose exec shipin-api php think migrate:run
```

---

## ❓ 常见问题

### 开发环境问题

#### Q: 前端启动失败，提示端口被占用

```bash
# 查看端口占用
netstat -ano | findstr :3001

# 杀死进程
taskkill /PID <PID> /F

# 或者修改端口
npm run dev -- --port 3002
```

#### Q: 后端API请求跨域错误

```php
// 在中间件中添加CORS支持
class Cors
{
    public function handle($request, \Closure $next)
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        
        if ($request->method() === 'OPTIONS') {
            return response('', 200);
        }
        
        return $next($request);
    }
}
```

#### Q: 数据库连接失败

```bash
# 检查数据库服务状态
docker-compose ps

# 查看数据库日志
docker-compose logs shipin-mysql

# 重启数据库服务
docker-compose restart shipin-mysql
```

### 性能优化问题

#### Q: 页面加载缓慢

1. **启用Gzip压缩**
2. **使用CDN加速静态资源**
3. **优化图片大小和格式**
4. **实现懒加载**
5. **减少HTTP请求数量**

#### Q: 数据库查询慢

1. **添加适当的索引**
2. **优化SQL查询语句**
3. **使用缓存减少数据库访问**
4. **分页查询大数据集**

---

## 📞 技术支持

如遇到开发问题，请按以下步骤处理：

1. **查看日志**: 检查应用和错误日志
2. **搜索文档**: 查阅相关技术文档
3. **社区求助**: 在技术社区提问
4. **联系团队**: 联系项目技术负责人

---

**最后更新**: 2025-07-30  
**文档版本**: v1.0.0  
**维护团队**: 开发团队