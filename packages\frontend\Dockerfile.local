# 使用本地构建的静态文件
FROM nginx:alpine

# 设置时区
ENV TZ=Asia/Shanghai
RUN apk update && apk add --no-cache tzdata wget \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && apk del tzdata

# 复制本地构建的静态文件
COPY dist /usr/share/nginx/html

# 复制Nginx配置
COPY nginx.prod.conf /etc/nginx/conf.d/default.conf

# 创建日志目录
RUN mkdir -p /var/log/nginx \
    && chown -R nginx:nginx /var/log/nginx

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
