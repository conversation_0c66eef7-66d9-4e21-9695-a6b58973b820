{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.0.0", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0|^4.0", "topthink/think-filesystem": "^2.0", "predis/predis": "^3.0", "topthink/think-queue": "^3.0"}, "require-dev": {"topthink/think-dumper": "^1.0", "topthink/think-trace": "^1.0", "phpunit/phpunit": "^10.0", "mockery/mockery": "^1.5", "fakerphp/faker": "^1.21", "zircote/swagger-php": "^4.7", "monolog/monolog": "^3.0", "elasticsearch/elasticsearch": "^8.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"], "test": "phpunit", "test:unit": "phpunit --testsuite=Unit", "test:feature": "phpunit --testsuite=Feature", "test:integration": "phpunit --testsuite=Integration", "test:coverage": "phpunit --coverage-html coverage"}}