import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Test.vue'),
    meta: {
      title: '测试页面',
      requiresAuth: false
    }
  },
  {
    path: '/simple',
    name: 'Simple',
    component: () => import('@/views/Simple.vue'),
    meta: {
      title: '简化页面',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表盘',
      requiresAuth: true
    }
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/Users.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true
    }
  },
  {
    path: '/users/vip',
    name: 'VipManagement',
    component: () => import('@/views/users/VipManagement.vue'),
    meta: {
      title: 'VIP用户管理',
      requiresAuth: true
    }
  },
  {
    path: '/users/behavior',
    name: 'BehaviorAnalysis',
    component: () => import('@/views/users/BehaviorAnalysis.vue'),
    meta: {
      title: '用户行为分析',
      requiresAuth: true
    }
  },
  {
    path: '/videos',
    name: 'Videos',
    component: () => import('@/views/Videos.vue'),
    meta: {
      title: '视频管理',
      requiresAuth: true
    }
  },
  {
    path: '/videos/review',
    name: 'VideoReview',
    component: () => import('@/views/videos/Review.vue'),
    meta: {
      title: '视频审核',
      requiresAuth: true
    }
  },
  {
    path: '/videos/analytics',
    name: 'VideoAnalytics',
    component: () => import('@/views/videos/Analytics.vue'),
    meta: {
      title: '视频分析',
      requiresAuth: true
    }
  },
  {
    path: '/ad-detection',
    name: 'AdDetection',
    component: () => import('@/views/AdDetection.vue'),
    meta: {
      title: '广告检测管理',
      requiresAuth: true
    }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: () => import('@/views/Categories.vue'),
    meta: {
      title: '分类管理',
      requiresAuth: true
    }
  },
  {
    path: '/comments',
    name: 'Comments',
    component: () => import('@/views/Comments.vue'),
    meta: {
      title: '评论管理',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: {
      title: '系统设置',
      requiresAuth: true
    }
  },
  {
    path: '/video-processing',
    name: 'VideoProcessing',
    component: () => import('@/views/VideoProcessing.vue'),
    meta: {
      title: '视频处理管理',
      requiresAuth: true
    }
  },
  // 安全管理路由
  {
    path: '/security',
    redirect: '/security/auth'
  },
  {
    path: '/security/auth',
    name: 'SecurityAuth',
    component: () => import('@/views/security/Auth.vue'),
    meta: {
      title: '认证管理',
      requiresAuth: true
    }
  },
  {
    path: '/security/permissions',
    name: 'SecurityPermissions',
    component: () => import('@/views/security/Permissions.vue'),
    meta: {
      title: '权限管理',
      requiresAuth: true
    }
  },
  {
    path: '/security/logs',
    name: 'SecurityLogs',
    component: () => import('@/views/security/Logs.vue'),
    meta: {
      title: '安全日志',
      requiresAuth: true
    }
  },
  {
    path: '/security/sessions',
    name: 'SecuritySessions',
    component: () => import('@/views/security/Sessions.vue'),
    meta: {
      title: '会话管理',
      requiresAuth: true
    }
  },
  // 采集管理路由
  {
    path: '/collect',
    redirect: '/collect/sources'
  },
  {
    path: '/collect/sources',
    name: 'CollectSources',
    component: () => import('@/views/collect/Sources.vue'),
    meta: {
      title: '采集源管理',
      requiresAuth: true
    }
  },
  {
    path: '/collect/mapping',
    name: 'CollectMapping',
    component: () => import('@/views/collect/CategoryMapping.vue'),
    meta: {
      title: '分类映射',
      requiresAuth: true
    }
  },
  {
    path: '/collect/tasks',
    name: 'CollectTasks',
    component: () => import('@/views/collect/Tasks.vue'),
    meta: {
      title: '采集任务',
      requiresAuth: true
    }
  },
  {
    path: '/collect/logs',
    name: 'CollectLogs',
    component: () => import('@/views/collect/Logs.vue'),
    meta: {
      title: '采集日志',
      requiresAuth: true
    }
  },
  {
    path: '/collect/config',
    name: 'CollectConfig',
    component: () => import('@/views/collect/Config.vue'),
    meta: {
      title: '采集配置',
      requiresAuth: true
    }
  },
  {
    path: '/collect/monitor',
    name: 'CollectMonitor',
    component: () => import('@/views/collect/Monitor.vue'),
    meta: {
      title: '采集监控',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 防止无限循环的标记
  if (to.meta.skipAuth) {
    next()
    return
  }

  // 如果访问登录页
  if (to.path === '/login') {
    // 如果已经有token，尝试验证
    if (authStore.token) {
      try {
        const isValid = await authStore.checkAuth()
        if (isValid) {
          // token有效，重定向到dashboard
          const redirect = to.query.redirect as string
          if (redirect && redirect !== '/login' && !redirect.includes('login')) {
            next(redirect)
          } else {
            next('/dashboard')
          }
          return
        }
      } catch (error) {
        // 验证失败，清除token
        authStore.logout()
      }
    }
    // 允许访问登录页
    next()
    return
  }

  // 如果访问需要认证的页面
  if (to.meta.requiresAuth) {
    if (!authStore.token) {
      // 没有token，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 有token，先允许导航，让AuthGuard组件处理认证检查
    // 这样可以避免路由守卫和AuthGuard的重复检查
    next()
  } else {
    // 不需要认证的页面，直接通过
    next()
  }
})

export default router
