#!/bin/bash

# ==========================================
# 🚀 快速部署脚本
# ==========================================
# 一键部署企业级视频平台到不同环境
# 
# <AUTHOR> Video Platform Team
# @version 2.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "🚀 企业级视频平台 - 快速部署工具"
    echo "=================================================="
    echo -e "${NC}"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  dev, development    部署到开发环境"
    echo "  staging            部署到测试环境"
    echo "  prod, production   部署到生产环境"
    echo ""
    echo "选项:"
    echo "  --build            强制重新构建镜像"
    echo "  --backup           部署前备份数据"
    echo "  --migrate          运行数据库迁移"
    echo "  --seed             运行数据库种子"
    echo "  --no-cache         构建时不使用缓存"
    echo "  --pull             拉取最新镜像"
    echo "  --help, -h         显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev --build"
    echo "  $0 production --backup --migrate"
    echo "  $0 staging --pull --no-cache"
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 检查环境配置
check_environment() {
    local env=$1
    log_step "检查${env}环境配置..."
    
    # 检查环境配置文件
    if [[ ! -f "docker-compose.${env}.yml" ]]; then
        log_error "缺少 docker-compose.${env}.yml 配置文件"
        exit 1
    fi
    
    # 检查环境变量文件
    if [[ ! -f ".env" ]]; then
        log_warning "缺少 .env 文件，将使用默认配置"
        if [[ -f ".env.example" ]]; then
            cp .env.example .env
            log_info "已从 .env.example 创建 .env 文件"
        fi
    fi
    
    log_success "${env}环境配置检查通过"
}

# 备份数据
backup_data() {
    local env=$1
    log_step "备份${env}环境数据..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)_${env}"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    if docker-compose ps mysql | grep -q "Up"; then
        log_info "备份MySQL数据库..."
        docker-compose exec -T mysql mysqldump -u root -p"${DB_ROOT_PASSWORD:-root123456}" --all-databases > "$backup_dir/mysql_backup.sql"
        log_success "MySQL数据库备份完成"
    fi
    
    # 备份Redis数据
    if docker-compose ps redis | grep -q "Up"; then
        log_info "备份Redis数据..."
        docker-compose exec -T redis redis-cli BGSAVE
        docker cp "$(docker-compose ps -q redis):/data/dump.rdb" "$backup_dir/redis_backup.rdb"
        log_success "Redis数据备份完成"
    fi
    
    # 备份上传文件
    if [[ -d "storage/uploads" ]]; then
        log_info "备份上传文件..."
        tar -czf "$backup_dir/uploads_backup.tar.gz" storage/uploads/
        log_success "上传文件备份完成"
    fi
    
    log_success "数据备份完成，保存在: $backup_dir"
}

# 构建镜像
build_images() {
    local env=$1
    local no_cache=$2
    local pull=$3
    
    log_step "构建${env}环境镜像..."
    
    local build_args=""
    if [[ "$no_cache" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi
    
    if [[ "$pull" == "true" ]]; then
        build_args="$build_args --pull"
    fi
    
    # 构建镜像
    docker-compose -f "docker-compose.${env}.yml" build $build_args
    
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    local env=$1
    log_step "部署${env}环境服务..."
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose down --remove-orphans
    
    # 启动新服务
    log_info "启动新服务..."
    docker-compose -f "docker-compose.${env}.yml" up -d
    
    log_success "服务部署完成"
}

# 等待服务启动
wait_for_services() {
    log_step "等待服务启动..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "检查服务状态... (${attempt}/${max_attempts})"
        
        # 检查API服务
        if curl -f -s http://localhost:3000/health > /dev/null 2>&1; then
            log_success "API服务已启动"
            break
        fi
        
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "服务启动超时，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 运行数据库迁移
run_migrations() {
    log_step "运行数据库迁移..."
    
    # 等待数据库启动
    sleep 10
    
    # 运行迁移
    docker-compose exec api php think migrate:run
    
    log_success "数据库迁移完成"
}

# 运行数据库种子
run_seeds() {
    log_step "运行数据库种子..."
    
    docker-compose exec api php think seed:run
    
    log_success "数据库种子完成"
}

# 显示部署结果
show_deployment_result() {
    local env=$1
    
    echo -e "${GREEN}"
    echo "=================================================="
    echo "🎉 ${env}环境部署完成！"
    echo "=================================================="
    echo -e "${NC}"
    
    echo "服务访问地址:"
    case $env in
        "development")
            echo "  🌐 前端: http://localhost:3002"
            echo "  ⚙️  管理后台: http://localhost:3001"
            echo "  🔧 API: http://localhost:3000"
            echo "  📊 phpMyAdmin: http://localhost:8080"
            echo "  🔴 Redis Commander: http://localhost:8081"
            echo "  📧 Mailhog: http://localhost:8025"
            ;;
        "production")
            echo "  🌐 前端: https://your-domain.com"
            echo "  ⚙️  管理后台: https://admin.your-domain.com"
            echo "  📊 监控: http://localhost:3001 (Grafana)"
            echo "  📈 指标: http://localhost:9090 (Prometheus)"
            ;;
    esac
    
    echo ""
    echo "常用命令:"
    echo "  查看日志: npm run logs"
    echo "  查看状态: npm run env:status"
    echo "  重启服务: npm run restart"
    echo "  停止服务: npm run stop"
}

# 主函数
main() {
    show_banner
    
    # 解析参数
    local env=""
    local build=false
    local backup=false
    local migrate=false
    local seed=false
    local no_cache=false
    local pull=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            dev|development)
                env="development"
                shift
                ;;
            staging)
                env="staging"
                shift
                ;;
            prod|production)
                env="production"
                shift
                ;;
            --build)
                build=true
                shift
                ;;
            --backup)
                backup=true
                shift
                ;;
            --migrate)
                migrate=true
                shift
                ;;
            --seed)
                seed=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --pull)
                pull=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [[ -z "$env" ]]; then
        log_error "请指定部署环境"
        show_help
        exit 1
    fi
    
    log_info "开始部署到${env}环境..."
    
    # 执行部署步骤
    check_dependencies
    check_environment "$env"
    
    if [[ "$backup" == "true" ]]; then
        backup_data "$env"
    fi
    
    if [[ "$build" == "true" ]]; then
        build_images "$env" "$no_cache" "$pull"
    fi
    
    deploy_services "$env"
    wait_for_services
    
    if [[ "$migrate" == "true" ]]; then
        run_migrations
    fi
    
    if [[ "$seed" == "true" ]]; then
        run_seeds
    fi
    
    show_deployment_result "$env"
}

# 运行主函数
main "$@"
