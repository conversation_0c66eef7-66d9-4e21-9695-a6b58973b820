import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/api/user'
import { authService } from '@/utils/auth'
import type { User, LoginRequest, RegisterRequest } from '@/types/user'

/**
 * 用户状态管理 - 重新设计版本
 * 
 * 简化的用户状态管理，专注于基本的认证功能
 */
export const useUserStore = defineStore('user', () => {
  // ================================
  // 状态定义
  // ================================
  
  // 用户信息
  const user = ref<User | null>(authService.getUserInfo())

  // 认证状态
  const isLoggedIn = ref(authService.isAuthenticated())
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // ================================
  // 计算属性
  // ================================

  const isAuthenticated = computed(() => authService.isAuthenticated())
  
  // ================================
  // 认证方法
  // ================================
  
  /**
   * 用户登录
   */
  const login = async (credentials: LoginRequest) => {
    try {
      isLoading.value = true
      error.value = null

      // 使用新的认证服务
      const userInfo = await authService.login(credentials)

      // 更新store状态
      user.value = userInfo
      isLoggedIn.value = true

      return { success: true, user: userInfo }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登录失败'
      error.value = errorMessage
      return { success: false, error: errorMessage }
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 用户注册
   */
  const register = async (userData: RegisterRequest) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await userApi.register(userData)
      
      if (response.success) {
        return { success: true, message: '注册成功' }
      } else {
        error.value = response.message
        return { success: false, error: response.message }
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '注册失败'
      error.value = errorMessage
      return { success: false, error: errorMessage }
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 用户登出
   */
  const logout = async () => {
    try {
      if (token.value) {
        await userApi.logout()
      }
    } catch (err) {
      console.warn('登出请求失败:', err)
    } finally {
      // 清理本地状态
      user.value = null
      token.value = null
      isLoggedIn.value = false
      error.value = null
      
      // 清理本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')
    }
  }
  
  /**
   * 获取当前用户信息
   */
  const getCurrentUser = async () => {
    if (!token.value) {
      throw new Error('No access token')
    }
    
    try {
      const response = await userApi.getCurrentUser()
      
      if (response.success && response.data) {
        user.value = response.data
        isLoggedIn.value = true
        
        // 更新本地存储
        localStorage.setItem('user_info', JSON.stringify(response.data))
        
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      // 获取用户信息失败，清理认证状态
      await logout()
      throw err
    }
  }
  
  /**
   * 初始化用户状态
   */
  const initializeAuth = async () => {
    // 从本地存储恢复用户信息
    const savedUser = localStorage.getItem('user_info')
    const savedToken = localStorage.getItem('token')

    console.log('🔄 初始化认证状态:', {
      hasSavedUser: !!savedUser,
      hasSavedToken: !!savedToken
    })

    if (savedUser && savedToken) {
      try {
        user.value = JSON.parse(savedUser)
        token.value = savedToken
        isLoggedIn.value = true

        console.log('✅ 恢复用户信息成功:', user.value)

        // 尝试验证token是否有效，但不强制要求成功
        try {
          await getCurrentUser()
          console.log('✅ Token验证成功')
        } catch (err) {
          console.warn('⚠️ Token验证失败，但保留本地认证状态:', err)
          // 不调用logout()，保留本地认证状态
          // 让用户在实际使用时再处理token过期问题
        }
      } catch (err) {
        console.warn('❌ 解析保存的用户信息失败:', err)
        await logout()
      }
    } else {
      console.log('ℹ️ 没有保存的认证信息')
    }
  }
  
  // ================================
  // 返回状态和方法
  // ================================
  
  return {
    // 状态
    user,
    token,
    isLoggedIn,
    isLoading,
    error,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    register,
    logout,
    getCurrentUser,
    initializeAuth,
  }
})
