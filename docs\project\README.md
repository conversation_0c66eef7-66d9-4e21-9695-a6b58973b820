# 🎬 企业级视频平台项目文档

> 基于现代化技术栈构建的全功能视频平台，支持短视频、长视频和直播功能

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![PHP Version](https://img.shields.io/badge/php-%3E%3D8.2-blue.svg)](https://php.net/)
[![Node Version](https://img.shields.io/badge/node-%3E%3D18.0-green.svg)](https://nodejs.org/)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](https://docker.com/)

## 📋 文档导航

### 📖 项目概述
- [项目总结](./SUMMARY.md) - 项目整体评估和技术架构
- [功能特色](./FEATURES.md) - 核心功能和技术特点

### 🚀 部署指南
- [部署指南](../deployment/DEPLOYMENT.md) - 完整的部署文档
- [环境配置](../deployment/ENVIRONMENT.md) - 环境配置说明

### 🛠️ 开发文档
- [开发指南](../development/DEVELOPER_GUIDE.md) - 开发环境搭建和规范
- [API文档](../api/README.md) - 完整的API接口文档
- [贡献指南](../development/CONTRIBUTING.md) - 代码贡献规范

### 📊 分析报告
- [问题修复报告](../reports/BUG_FIXES.md) - 历史问题修复记录
- [性能优化报告](../reports/OPTIMIZATION.md) - 性能优化分析
- [测试报告](../reports/TESTING.md) - 测试结果和覆盖率

### 📚 用户手册
- [用户手册](../guides/USER_MANUAL.md) - 用户操作指南
- [管理员手册](../guides/ADMIN_MANUAL.md) - 管理员操作指南

## ✨ 项目特色

- 🚀 **现代化架构**: 前后端分离，微服务化设计
- 🎥 **多媒体支持**: 短视频、长视频、直播全覆盖
- 🔒 **企业级安全**: JWT认证、权限控制、视频加密
- 📊 **智能分析**: 用户行为分析、内容推荐算法
- 🕷️ **内容采集**: 多源内容自动采集和处理
- 📱 **响应式设计**: 完美适配PC和移动端
- 🐳 **容器化部署**: Docker一键部署，支持集群
- 📈 **性能监控**: 实时性能监控和告警
- 🎬 **视频处理**: 自动转码、切片、加密、缩略图生成
- 🛡️ **广告过滤**: 智能广告检测和自动跳过
- 🔄 **状态管理**: 完整的视频处理状态跟踪
- 👥 **用户管理**: 完善的用户权限和审核系统

## 🏗️ 技术架构

### 后端技术栈
- **PHP 8.2** + **ThinkPHP 8.0** - 现代化PHP框架
- **MySQL 8.0** - 高性能关系型数据库
- **Redis 7.0** - 内存数据库，缓存和队列
- **Nginx** - 高性能Web服务器
- **FFmpeg** - 视频处理和转码

### 前端技术栈
- **Vue 3.5** + **TypeScript 5.7** - 现代化前端框架
- **Element Plus** - 企业级UI组件库
- **Vite 6.0** - 现代化构建工具
- **Pinia** - Vue 3状态管理
- **Video.js** - 专业视频播放器

### 开发工具
- **Docker** + **Docker Compose** - 容器化开发和部署
- **PHPUnit** - PHP单元测试框架
- **Vitest** - 现代化JavaScript测试框架
- **ESLint** + **Prettier** - 代码质量和格式化

## 🎯 核心功能

### 📹 视频管理
- **视频上传**: 支持多种格式的视频文件上传
- **自动转码**: 自动转换为多种分辨率和格式
- **视频切片**: HLS切片技术，支持流式播放
- **缩略图生成**: 自动生成视频封面和预览图
- **视频加密**: 企业级视频内容保护
- **播放统计**: 详细的播放数据分析

### 👥 用户系统
- **用户注册/登录**: 支持多种登录方式
- **权限管理**: 基于角色的权限控制
- **用户画像**: 智能用户行为分析
- **VIP会员**: 会员等级和权益管理
- **社交功能**: 评论、点赞、收藏、分享

### 🕷️ 内容采集
- **多源采集**: 支持多个视频源自动采集
- **智能分类**: 自动内容分类和标签
- **质量控制**: 内容质量评估和过滤
- **采集监控**: 实时采集状态监控
- **自动更新**: 定时自动更新内容

### 📊 数据分析
- **实时统计**: 实时用户和内容数据
- **趋势分析**: 数据趋势图表展示
- **用户行为**: 详细的用户行为追踪
- **内容分析**: 内容热度和推荐算法
- **性能监控**: 系统性能实时监控

## 🚀 快速开始

### 环境要求
- **PHP**: >= 8.2
- **Node.js**: >= 18.0
- **MySQL**: >= 8.0
- **Redis**: >= 7.0
- **Docker**: >= 20.0 (可选)

### 安装部署

1. **克隆项目**
```bash
git clone <repository-url>
cd zhengshiban
```

2. **Docker部署 (推荐)**
```bash
docker-compose up -d
```

3. **手动部署**
```bash
# 安装后端依赖
cd packages/api
composer install

# 安装前端依赖
cd ../admin
npm install

# 启动服务
npm run dev
```

详细部署说明请参考 [部署指南](../deployment/DEPLOYMENT.md)

## 📞 技术支持

如有问题，请查看相关文档或联系技术支持团队。

---

**最后更新**: 2025-07-30  
**文档版本**: v2.0.0