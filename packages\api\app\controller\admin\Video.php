<?php
declare(strict_types=1);

namespace app\controller\admin;

use think\Request;
use think\Response;
use think\facade\Db;
use think\facade\Validate;
use app\service\ResponseService;
use app\service\VideoProcessingStatusService;
use app\service\ValidationService;

/**
 * 管理员视频管理控制器
 */
class Video
{
    
    protected $responseService;
    protected $validationService;
    protected $videoProcessingStatusService;

    public function __construct()
    {
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
        $this->videoProcessingStatusService = new VideoProcessingStatusService();
    }
    /**
     * 获取视频列表
     */
    public function index(Request $request): Response
    {
        try {
            $params = $request->get();
            
            // 分页参数
            $page = (int)($params['page'] ?? 1);
            $limit = (int)($params['limit'] ?? 20);
            $offset = ($page - 1) * $limit;
            
            // 查询条件
            $where = [];

            // 默认过滤掉已删除的视频
            $where[] = ['v.status', '<>', 'deleted'];

            // 搜索条件
            if (!empty($params['search'])) {
                $keyword = $params['search'];
                $where[] = ['v.title', 'like', "%{$keyword}%"];
            }

            // 状态筛选
            if (!empty($params['status'])) {
                $where[] = ['v.status', '=', $params['status']];
            }
            
            // 视频类型筛选
            if (!empty($params['videoType']) && $params['videoType'] !== 'all') {
                $where[] = ['v.video_type', '=', $params['videoType']];
            }
            
            // 分类筛选
            if (!empty($params['categoryId'])) {
                $where[] = ['v.category_id', '=', $params['categoryId']];
            }
            
            // 日期筛选
            if (!empty($params['startDate']) && !empty($params['endDate'])) {
                $where[] = ['v.created_at', 'between', [$params['startDate'], $params['endDate'] . ' 23:59:59']];
            }
            
            // 查询视频列表
            $videos = Db::table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field('v.*, u.username, u.nickname, c.name as category_name')
                ->where($where)
                ->order('v.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 处理视频数据，添加处理状态描述
            foreach ($videos as &$video) {
                $video['processing_info'] = $this->getProcessingInfo($video);
            }
            
            // 获取总数
            $total = Db::table('videos')->alias('v')->where($where)->count();
            
            return json([
                'success' => true,
                'message' => 'success',
                'data' => [
                    'videos' => $videos,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取视频列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取视频处理状态信息
     */
    private function getProcessingInfo(array $video): array
    {
        $processingInfo = [
            'status' => 'none',
            'status_text' => '无需处理',
            'progress' => 0,
            'current_step' => '',
            'can_audit' => true,
            'audit_message' => '',
            'video_source' => 'collected' // 默认为采集视频
        ];

        // 如果没有本地文件路径，说明是采集的视频，无需处理
        if (empty($video['file_path'])) {
            $processingInfo['video_source'] = 'collected';
            return $processingInfo;
        }

        // 标记为上传视频
        $processingInfo['video_source'] = 'uploaded';

        // 使用VideoProcessingStatusService获取详细状态
        try {
            $summary = $this->videoProcessingStatusService->getProcessingSummary($video['id']);

            if (!empty($summary['steps'])) {
                $processingInfo['status'] = $summary['overall_status'];
                $processingInfo['progress'] = $summary['overall_progress'];
                $processingInfo['current_step'] = $summary['current_step'] ?? '';
                $processingInfo['error_message'] = $summary['error_message'] ?? '';

                // 根据整体状态设置状态文本和审核权限
                switch ($summary['overall_status']) {
                    case 'pending':
                        $processingInfo['status_text'] = '等待处理';
                        $processingInfo['can_audit'] = false;
                        $processingInfo['audit_message'] = '视频正在等待处理，请稍后审核！';
                        break;

                    case 'processing':
                        $processingInfo['status_text'] = '处理中';
                        $processingInfo['can_audit'] = false;
                        $processingInfo['audit_message'] = '视频正在转码处理中，请稍后审核！';
                        break;

                    case 'completed':
                        $processingInfo['status_text'] = '处理完成';
                        $processingInfo['can_audit'] = true;
                        break;

                    case 'failed':
                        $processingInfo['status_text'] = '处理失败';
                        $processingInfo['can_audit'] = false;
                        $processingInfo['audit_message'] = '视频处理失败，无法审核！';
                        break;
                }

                // 添加详细的步骤信息
                $processingInfo['steps'] = $summary['steps'];
            } else {
                // 没有处理状态记录，可能需要重新处理
                $processingInfo['status'] = 'need_process';
                $processingInfo['status_text'] = '需要处理';
                $processingInfo['can_audit'] = false;
                $processingInfo['audit_message'] = '视频需要先进行转码处理！';
            }
        } catch (\Exception $e) {
            Log::error('获取视频处理状态失败', [
                'video_id' => $video['id'],
                'error' => $e->getMessage()
            ]);
            // 发生错误时，默认为需要处理
            $processingInfo['status'] = 'need_process';
            $processingInfo['status_text'] = '需要处理';
            $processingInfo['can_audit'] = false;
            $processingInfo['audit_message'] = '获取处理状态失败，请重新处理！';
        }

        return $processingInfo;
    }

    /**
     * 获取视频详情
     */
    public function read($id): Response
    {
        try {
            $video = Db::table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field('v.*, u.username, u.nickname, c.name as category_name')
                ->where('v.id', $id)
                ->find();
            
            if (!$video) {
                return $this->responseService->error('视频不存在', 400);
            }
            
            return json([
                'success' => true,
                'message' => 'success',
                'data' => $video
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取视频详情失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建视频
     */
    public function save(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证参数
            $validate = Validate::rule([
                'title' => 'require|length:1,200',
                'video_type' => 'require|in:long,short',
                'category_id' => 'require|integer|gt:0'
            ])->message([
                'title.require' => '视频标题不能为空',
                'title.length' => '标题长度为1-200个字符',
                'video_type.require' => '视频类型不能为空',
                'video_type.in' => '视频类型必须是long或short',
                'category_id.require' => '分类不能为空'
            ]);

            if (!$validate->check($data)) {
                return json([
                    'success' => false,
                    'message' => $validate->getError(),
                    'data' => null
                ]);
            }
            
            // 创建视频记录
            $videoData = [
                'title' => $data['title'],
                'description' => $data['description'] ?? '',
                'video_type' => $data['video_type'],
                'category_id' => $data['category_id'],
                'user_id' => 1, // 管理员创建
                'file_path' => $data['file_path'] ?? '',
                'cover_image' => $data['cover_image'] ?? '',
                'status' => $data['status'] ?? 'published',
                'audit_status' => 'approved',
                'published_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $videoId = Db::table('videos')->insertGetId($videoData);
            
            return json([
                'success' => true,
                'message' => '视频创建成功',
                'data' => ['video_id' => $videoId]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '视频创建失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新视频
     */
    public function update(Request $request, $id): Response
    {
        try {
            $data = $request->put();
            
            // 检查视频是否存在
            $video = Db::table('videos')->where('id', $id)->find();
            if (!$video) {
                return $this->responseService->error('视频不存在', 400);
            }
            
            // 更新数据
            $updateData = [];
            $allowedFields = ['title', 'description', 'video_type', 'category_id', 'status', 'audit_status', 'is_featured'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            if (!empty($updateData)) {
                $updateData['updated_at'] = date('Y-m-d H:i:s');
                Db::table('videos')->where('id', $id)->update($updateData);
            }
            
            return json([
                'success' => true,
                'message' => '视频更新成功',
                'data' => ['video_id' => $id]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '视频更新失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除视频
     */
    public function delete($id): Response
    {
        try {
            // 检查视频是否存在
            $video = Db::table('videos')->where('id', $id)->find();
            if (!$video) {
                return $this->responseService->error('视频不存在', 400);
            }

            // 开始事务
            Db::startTrans();

            try {
                // 1. 删除相关的评论
                Db::table('video_comments')->where('video_id', $id)->delete();

                // 2. 删除相关的点赞记录
                Db::table('video_likes')->where('video_id', $id)->delete();

                // 3. 删除相关的收藏记录
                Db::table('video_collections')->where('video_id', $id)->delete();

                // 4. 删除相关的播放地址记录（采集视频）
                Db::table('video_play_urls')->where('video_id', $id)->delete();

                // 5. 删除相关的处理状态记录
                Db::table('video_processing_status')->where('video_id', $id)->delete();

                // 6. 删除相关的队列记录
                Db::table('video_processing_queue')->where('video_id', $id)->delete();

                // 7. 删除视频文件
                $this->deleteVideoFiles($video);

                // 8. 最后删除视频记录
                Db::table('videos')->where('id', $id)->delete();

                // 提交事务
                Db::commit();

                return json([
                    'success' => true,
                    'message' => '视频及相关数据删除成功',
                    'data' => ['video_id' => $id]
                ]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '视频删除失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除视频相关文件
     */
    private function deleteVideoFiles($video): void
    {
        $rootPath = app()->getRootPath() . 'public';

        try {
            // 删除视频文件
            if (!empty($video['file_path'])) {
                $videoFilePath = $rootPath . $video['file_path'];
                if (file_exists($videoFilePath)) {
                    unlink($videoFilePath);
                }
            }

            // 删除缩略图
            if (!empty($video['cover_image'])) {
                $thumbnailPath = $rootPath . $video['cover_image'];
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                }
            }

            // 删除HLS文件夹
            if (!empty($video['hls_url'])) {
                $hlsDir = $rootPath . '/hls/video_' . $video['id'];
                if (is_dir($hlsDir)) {
                    $this->deleteDirectory($hlsDir);
                }
            }

        } catch (\Exception $e) {
            // 文件删除失败不影响数据库删除，只记录日志
            error_log('删除视频文件失败: ' . $e->getMessage());
        }
    }

    /**
     * 递归删除目录
     */
    private function deleteDirectory($dir): bool
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * 获取视频统计
     */
    public function stats(Request $request): Response
    {
        try {
            $stats = [
                'total' => Db::table('videos')->count(),
                'published' => Db::table('videos')->where('status', 'published')->count(),
                'pending' => Db::table('videos')->where('audit_status', 'pending')->count(),
                'long_videos' => Db::table('videos')->where('video_type', 'long')->count(),
                'short_videos' => Db::table('videos')->where('video_type', 'short')->count(),
                'total_views' => Db::table('videos')->sum('view_count'),
                'total_likes' => Db::table('videos')->sum('like_count')
            ];
            
            return json([
                'success' => true,
                'message' => 'success',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取视频统计失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 审核视频
     * @param int $id 视频ID
     */
    public function audit(Request $request, $id): Response
    {
        try {
            // 获取请求数据
            $data = $request->post();

            // 验证数据
            $validate = validate([
                'audit_status' => 'require|in:pending,approved,rejected',
                'audit_message' => 'string'
            ]);

            if (!$validate->check($data)) {
                return json([
                    'success' => false,
                    'message' => $validate->getError(),
                    'data' => null
                ]);
            }

            // 查询视频是否存在
            $video = Db::table('videos')
                ->alias('v')
                ->leftJoin('video_processing_status vps', 'v.id = vps.video_id')
                ->field('v.*, vps.status as processing_status, vps.progress, vps.current_step, vps.error_message')
                ->where('v.id', $id)
                ->find();

            if (!$video) {
                return json([
                    'success' => false,
                    'message' => '视频不存在',
                    'data' => null
                ]);
            }

            // 检查视频处理状态
            $processingInfo = $this->getProcessingInfo($video);
            if (!$processingInfo['can_audit']) {
                return json([
                    'success' => false,
                    'message' => $processingInfo['audit_message'],
                    'data' => [
                        'processing_status' => $processingInfo['status'],
                        'progress' => $processingInfo['progress'],
                        'current_step' => $processingInfo['current_step']
                    ]
                ]);
            }

            // 更新审核状态
            $updateData = [
                'audit_status' => $data['audit_status'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 如果提供了审核消息，则添加到更新数据中
            if (isset($data['audit_message'])) {
                $updateData['audit_message'] = $data['audit_message'];
            }

            // 如果审核通过，自动设置为已发布状态
            if ($data['audit_status'] === 'approved') {
                $updateData['status'] = 'published';
                $updateData['published_at'] = date('Y-m-d H:i:s');
            }

            Db::table('videos')->where('id', $id)->update($updateData);

            // 更新审核处理状态
            try {
                $this->videoProcessingStatusService->updateStatusByVideoAndType(
                    intval($id),
                    VideoProcessingStatusService::TYPE_AUDIT,
                    [
                        'status' => VideoProcessingStatusService::STATUS_COMPLETED,
                        'progress' => 100,
                        'message' => $data['audit_status'] === 'approved' ? '审核通过' : '审核拒绝',
                        'completed_at' => date('Y-m-d H:i:s')
                    ]
                );

                Log::info('视频审核完成', [
                    'video_id' => $id,
                    'audit_status' => $data['audit_status'],
                    'admin_id' => $request->adminInfo['id'] ?? null
                ]);
            } catch (\Exception $e) {
                Log::error('更新审核状态失败', [
                    'video_id' => $id,
                    'error' => $e->getMessage()
                ]);
            }

            return json([
                'success' => true,
                'message' => '审核成功',
                'data' => [
                    'video_id' => $id,
                    'audit_status' => $data['audit_status'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '审核视频失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
