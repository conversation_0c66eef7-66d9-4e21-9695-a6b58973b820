<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Db;
use app\service\ResponseService;
use app\service\ValidationService;

/**
 * 用户个人中心控制器
 * 
 * 处理用户个人资料、统计信息等功能
 */
class UserProfile
{
    protected ResponseService $responseService;
    protected ValidationService $validationService;

    public function __construct()
    {
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
    }

    /**
     * 获取用户个人资料
     *
     * @param Request $request
     * @return Response
     */
    public function getProfile(Request $request): Response
    {
        try {
            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            // 获取用户详细信息
            $user = Db::table('users')
                ->where('id', $userId)
                ->field('id, username, nickname, email, avatar, bio, created_at, updated_at, status')
                ->find();

            if (!$user) {
                return $this->responseService->error('用户不存在', 404);
            }

            // 获取用户统计信息
            $stats = $this->getUserStats($userId);

            return $this->responseService->success([
                'user' => $user,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('获取用户资料失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新用户个人资料
     *
     * @param Request $request
     * @return Response
     */
    public function updateProfile(Request $request): Response
    {
        try {
            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            $data = $request->post();
            
            // 验证参数
            $rules = [
                'nickname' => 'max:50',
                'bio' => 'max:200',
                'avatar' => 'url'
            ];
            
            $validation = $this->validationService->validate($data, $rules);
            if (!$validation['valid']) {
                return $this->responseService->error($validation['message'], 400);
            }

            // 准备更新数据
            $updateData = [];
            if (isset($data['nickname'])) {
                $updateData['nickname'] = trim($data['nickname']);
            }
            if (isset($data['bio'])) {
                $updateData['bio'] = trim($data['bio']);
            }
            if (isset($data['avatar'])) {
                $updateData['avatar'] = trim($data['avatar']);
            }

            if (empty($updateData)) {
                return $this->responseService->error('没有需要更新的数据', 400);
            }

            $updateData['updated_at'] = date('Y-m-d H:i:s');

            // 更新用户信息
            $result = Db::table('users')
                ->where('id', $userId)
                ->update($updateData);

            if ($result) {
                return $this->responseService->success([
                    'message' => '更新成功'
                ]);
            } else {
                return $this->responseService->error('更新失败', 500);
            }

        } catch (\Exception $e) {
            return $this->responseService->error('更新用户资料失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取用户观看历史
     *
     * @param Request $request
     * @return Response
     */
    public function getWatchHistory(Request $request): Response
    {
        try {
            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            $page = (int)($request->get('page') ?? 1);
            $limit = (int)($request->get('limit') ?? 20);
            $offset = ($page - 1) * $limit;

            // 获取观看历史
            $history = Db::table('video_views')
                ->alias('vv')
                ->join('videos v', 'vv.video_id = v.id')
                ->join('users u', 'v.user_id = u.id')
                ->where('vv.user_id', $userId)
                ->where('v.status', 'published')
                ->field('v.id, v.title, v.description, v.thumbnail, v.duration, v.view_count, v.like_count, v.created_at, u.username, u.nickname, vv.created_at as watched_at')
                ->order('vv.created_at desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 获取总数
            $total = Db::table('video_views')
                ->alias('vv')
                ->join('videos v', 'vv.video_id = v.id')
                ->where('vv.user_id', $userId)
                ->where('v.status', 'published')
                ->count();

            return $this->responseService->success([
                'history' => $history,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('获取观看历史失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 清空观看历史
     *
     * @param Request $request
     * @return Response
     */
    public function clearWatchHistory(Request $request): Response
    {
        try {
            // 从用户认证中间件获取用户ID
            $userInfo = $request->userInfo ?? null;
            if (!$userInfo) {
                return $this->responseService->error('请先登录', 401);
            }
            $userId = $userInfo['id'];

            // 删除观看历史
            $result = Db::table('video_views')
                ->where('user_id', $userId)
                ->delete();

            return $this->responseService->success([
                'message' => '观看历史已清空',
                'deleted_count' => $result
            ]);

        } catch (\Exception $e) {
            return $this->responseService->error('清空观看历史失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取用户统计信息
     *
     * @param int $userId
     * @return array
     */
    private function getUserStats(int $userId): array
    {
        try {
            // 获取用户发布的视频数量
            $videoCount = Db::table('videos')
                ->where('user_id', $userId)
                ->where('status', 'published')
                ->count();

            // 获取用户视频的总观看数
            $totalViews = Db::table('videos')
                ->where('user_id', $userId)
                ->where('status', 'published')
                ->sum('view_count') ?: 0;

            // 获取用户视频的总点赞数
            $totalLikes = Db::table('videos')
                ->where('user_id', $userId)
                ->where('status', 'published')
                ->sum('like_count') ?: 0;

            // 获取用户收藏的视频数量
            $favoriteCount = Db::table('video_collections')
                ->where('user_id', $userId)
                ->count();

            // 获取用户点赞的视频数量
            $likedCount = Db::table('video_likes')
                ->where('user_id', $userId)
                ->count();

            return [
                'video_count' => $videoCount,
                'total_views' => $totalViews,
                'total_likes' => $totalLikes,
                'favorite_count' => $favoriteCount,
                'liked_count' => $likedCount
            ];

        } catch (\Exception $e) {
            return [
                'video_count' => 0,
                'total_views' => 0,
                'total_likes' => 0,
                'favorite_count' => 0,
                'liked_count' => 0
            ];
        }
    }
}
