import request from './request'

/**
 * API接口封装
 */
export const api = {
  // 视频处理相关
  video: {
    // 获取处理状态
    getProcessingStatus: () => request.get('/api/admin/video/processing-status'),
    
    // 获取处理日志
    getProcessingLogs: () => request.get('/api/admin/video/processing-logs'),
    
    // 重新处理视频
    reprocessVideo: (videoId: number) => request.post(`/api/admin/video/${videoId}/reprocess`),
    
    // 获取视频列表
    getList: (params: any) => request.get('/api/admin/videos', { params }),
    
    // 获取视频详情
    getDetail: (id: number) => request.get(`/api/admin/videos/${id}`),
    
    // 更新视频
    update: (id: number, data: any) => request.put(`/api/admin/videos/${id}`, data),
    
    // 删除视频
    delete: (id: number) => request.delete(`/api/admin/videos/${id}`),
    
    // 批量操作
    batchOperation: (data: any) => request.post('/api/admin/videos/batch', data)
  },

  // 用户管理相关
  user: {
    // 获取用户列表
    getList: (params: any) => request.get('/api/admin/users', { params }),
    
    // 获取用户详情
    getDetail: (id: number) => request.get(`/api/admin/users/${id}`),
    
    // 更新用户
    update: (id: number, data: any) => request.put(`/api/admin/users/${id}`, data),
    
    // 删除用户
    delete: (id: number) => request.delete(`/api/admin/users/${id}`),
    
    // 批量操作
    batchOperation: (data: any) => request.post('/api/admin/users/batch', data)
  },

  // 分类管理相关
  category: {
    // 获取分类列表
    getList: (params: any) => request.get('/api/admin/categories', { params }),
    
    // 获取分类详情
    getDetail: (id: number) => request.get(`/api/admin/categories/${id}`),
    
    // 创建分类
    create: (data: any) => request.post('/api/admin/categories', data),
    
    // 更新分类
    update: (id: number, data: any) => request.put(`/api/admin/categories/${id}`, data),
    
    // 删除分类
    delete: (id: number) => request.delete(`/api/admin/categories/${id}`)
  },

  // 系统统计相关
  stats: {
    // 获取概览统计
    getOverview: () => request.get('/api/admin/stats/overview'),
    
    // 获取用户统计
    getUserStats: (params: any) => request.get('/api/admin/stats/users', { params }),
    
    // 获取视频统计
    getVideoStats: (params: any) => request.get('/api/admin/stats/videos', { params }),
    
    // 获取系统信息
    getSystemInfo: () => request.get('/api/admin/stats/system')
  },

  // 系统配置相关
  config: {
    // 获取配置
    get: (key?: string) => request.get('/api/admin/config', { params: { key } }),
    
    // 更新配置
    update: (data: any) => request.put('/api/admin/config', data)
  },

  // 文件上传相关
  upload: {
    // 上传图片
    image: (file: File) => {
      const formData = new FormData()
      formData.append('file', file)
      return request.post('/api/admin/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    },
    
    // 上传视频
    video: (file: File, onProgress?: (progress: number) => void) => {
      const formData = new FormData()
      formData.append('file', file)
      return request.post('/api/admin/upload/video', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress(progress)
          }
        }
      })
    }
  }
}

export default api
