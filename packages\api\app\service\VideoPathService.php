<?php

namespace app\service;

use think\facade\Config;

/**
 * 视频路径处理服务 - 统一路径管理
 */
class VideoPathService
{
    /**
     * 公共根目录
     * @var string
     */
    private $publicRoot;
    
    /**
     * 临时目录
     * @var string
     */
    private $tempDir;
    
    /**
     * 输出目录
     * @var string
     */
    private $outputDir;
    
    /**
     * 缩略图目录
     * @var string
     */
    private $thumbnailDir;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->publicRoot = app()->getRootPath() . 'public';
        $this->tempDir = Config::get('video.temp_dir');
        $this->outputDir = Config::get('video.output_dir');
        $this->thumbnailDir = $this->publicRoot . '/uploads/thumbnails';
        
        // 确保目录存在
        $this->ensureDirectoriesExist();
    }
    
    /**
     * 将相对路径转换为完整的文件系统路径
     * 
     * @param string $relativePath 相对路径 (如: /storage/video/xxx.mp4)
     * @return string 完整路径
     */
    public function toFullPath(string $relativePath): string
    {
        // 如果已经是完整路径，直接返回
        if ($this->isAbsolutePath($relativePath)) {
            return $relativePath;
        }
        
        // 移除开头的斜杠
        $relativePath = ltrim($relativePath, '/');
        
        return $this->publicRoot . '/' . $relativePath;
    }
    
    /**
     * 将完整路径转换为相对于public的路径
     * 
     * @param string $fullPath 完整路径
     * @return string 相对路径
     */
    public function toRelativePath(string $fullPath): string
    {
        if (strpos($fullPath, $this->publicRoot) === 0) {
            return substr($fullPath, strlen($this->publicRoot));
        }
        
        return $fullPath;
    }
    
    /**
     * 获取视频的HLS目录路径
     * 
     * @param int $videoId 视频ID
     * @param bool $fullPath 是否返回完整路径
     * @return string
     */
    public function getHlsDir(int $videoId, bool $fullPath = true): string
    {
        $relativePath = "/hls/video_{$videoId}";
        
        return $fullPath ? $this->publicRoot . $relativePath : $relativePath;
    }
    
    /**
     * 获取视频的HLS播放列表路径
     * 
     * @param int $videoId 视频ID
     * @param bool $fullPath 是否返回完整路径
     * @return string
     */
    public function getHlsPlaylist(int $videoId, bool $fullPath = true): string
    {
        $relativePath = "/hls/video_{$videoId}/playlist.m3u8";
        
        return $fullPath ? $this->publicRoot . $relativePath : $relativePath;
    }
    
    /**
     * 获取缩略图路径
     * 
     * @param int $videoId 视频ID
     * @param bool $fullPath 是否返回完整路径
     * @return string
     */
    public function getThumbnailPath(int $videoId, bool $fullPath = true): string
    {
        $relativePath = "/uploads/thumbnails/thumbnail_{$videoId}.jpg";
        
        return $fullPath ? $this->publicRoot . $relativePath : $relativePath;
    }
    
    /**
     * 获取临时处理目录
     * 
     * @param int $videoId 视频ID
     * @return string
     */
    public function getTempDir(int $videoId): string
    {
        return $this->tempDir . "/video_{$videoId}";
    }
    
    /**
     * 检查路径是否为绝对路径
     * 
     * @param string $path 路径
     * @return bool
     */
    public function isAbsolutePath(string $path): bool
    {
        return strpos($path, '/') === 0 || preg_match('/^[A-Za-z]:/', $path);
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param string $path 文件路径（可以是相对路径或绝对路径）
     * @return bool
     */
    public function fileExists(string $path): bool
    {
        $fullPath = $this->toFullPath($path);
        return file_exists($fullPath);
    }
    
    /**
     * 获取文件大小
     * 
     * @param string $path 文件路径
     * @return int|false
     */
    public function getFileSize(string $path)
    {
        $fullPath = $this->toFullPath($path);
        return file_exists($fullPath) ? filesize($fullPath) : false;
    }
    
    /**
     * 创建目录
     * 
     * @param string $dir 目录路径
     * @param int $permissions 权限
     * @return bool
     */
    public function createDirectory(string $dir, int $permissions = 0755): bool
    {
        if (!is_dir($dir)) {
            return mkdir($dir, $permissions, true);
        }
        
        return true;
    }
    
    /**
     * 删除目录及其内容
     * 
     * @param string $dir 目录路径
     * @return bool
     */
    public function removeDirectory(string $dir): bool
    {
        if (!is_dir($dir)) {
            return true;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        
        return rmdir($dir);
    }
    
    /**
     * 确保必要的目录存在
     * 
     * @return void
     */
    private function ensureDirectoriesExist(): void
    {
        $directories = [
            $this->tempDir,
            $this->thumbnailDir,
            $this->publicRoot . '/hls',
            $this->publicRoot . '/uploads',
        ];
        
        foreach ($directories as $dir) {
            $this->createDirectory($dir);
        }
    }
    
    /**
     * 清理临时文件
     * 
     * @param int $videoId 视频ID
     * @return bool
     */
    public function cleanupTempFiles(int $videoId): bool
    {
        $tempDir = $this->getTempDir($videoId);
        return $this->removeDirectory($tempDir);
    }
    
    /**
     * 获取所有路径信息
     * 
     * @return array
     */
    public function getPathInfo(): array
    {
        return [
            'public_root' => $this->publicRoot,
            'temp_dir' => $this->tempDir,
            'output_dir' => $this->outputDir,
            'thumbnail_dir' => $this->thumbnailDir,
        ];
    }
}
