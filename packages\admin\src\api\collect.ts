/**
 * 采集管理相关API
 */
import request from '@/utils/request'

// 采集源管理
export const collectSourcesApi = {
  // 获取采集源列表
  getList: (params?: any) => request.get('/api/admin/collect/sources', { params }),

  // 获取采集源详情
  getDetail: (id: number) => request.get(`/api/admin/collect/sources/${id}`),

  // 创建采集源
  create: (data: any) => request.post('/api/admin/collect/sources', data),

  // 更新采集源
  update: (id: number, data: any) => request.put(`/api/admin/collect/sources/${id}`, data),

  // 删除采集源
  delete: (id: number) => request.delete(`/api/admin/collect/sources/${id}`),

  // 测试采集源连接
  test: (data: any) => request.post('/api/admin/collect/sources/test', data),

  // 批量操作采集源
  batchOperate: (data: any) => request.post('/api/admin/collect/sources/batch', data),

  // 批量健康检查
  batchHealthCheck: () => request.post('/api/admin/collect/sources/batch-health'),

  // 检查连通性
  checkConnectivity: (id: number) => request.get(`/api/admin/collect/sources/${id}/connectivity`)
}

// 采集任务管理
export const collectTasksApi = {
  // 获取任务列表
  getList: (params?: any) => request.get('/api/admin/collect/tasks', { params }),
  
  // 获取任务详情
  getDetail: (id: number) => request.get(`/api/admin/collect/tasks/${id}`),
  
  // 创建任务
  create: (data: any) => request.post('/api/admin/collect/tasks', data),
  
  // 更新任务
  update: (id: number, data: any) => request.put(`/api/admin/collect/tasks/${id}`, data),
  
  // 删除任务
  delete: (id: number) => request.delete(`/api/admin/collect/tasks/${id}`),
  
  // 启动任务
  start: (id: number) => request.post(`/api/admin/collect/tasks/${id}/start`),
  
  // 停止任务
  stop: (id: number) => request.post(`/api/admin/collect/tasks/${id}/stop`),
  
  // 重启任务
  restart: (id: number) => request.post(`/api/admin/collect/tasks/${id}/restart`)
}

// 采集日志管理
export const collectLogsApi = {
  // 获取日志列表
  getList: (params?: any) => request.get('/api/admin/collect/logs', { params }),
  
  // 获取日志详情
  getDetail: (id: number) => request.get(`/api/admin/collect/logs/${id}`),
  
  // 清理日志
  clear: (params?: any) => request.delete('/api/admin/collect/logs', { params })
}

// 采集配置管理
export const collectConfigApi = {
  // 获取配置
  get: () => request.get('/api/admin/collect/config'),
  
  // 更新配置
  update: (data: any) => request.put('/api/admin/collect/config', data),
  
  // 重置配置
  reset: () => request.post('/api/admin/collect/config/reset')
}

// 采集监控
export const collectMonitorApi = {
  // 获取监控数据
  getStats: () => request.get('/api/admin/collect/monitor/stats'),

  // 获取实时状态
  getStatus: () => request.get('/api/admin/collect/monitor/status'),

  // 获取性能指标
  getMetrics: (params?: any) => request.get('/api/admin/collect/monitor/metrics', { params }),

  // 获取统计数据
  getStatistics: (period?: string) => request.get('/api/admin/collect/statistics', { params: { period } }),

  // 获取采集源健康状态
  getSourceHealth: () => request.get('/api/admin/collect/source-health'),

  // 自动健康检查
  autoHealthCheck: () => request.post('/api/admin/collect/auto-health-check'),

  // 重试失败任务
  retryFailedTasks: (maxRetries?: number) => request.post('/api/admin/collect/retry-failed-tasks', { max_retries: maxRetries })
}

// 分类映射管理
export const collectMappingApi = {
  // 获取映射列表
  getList: (params?: any) => request.get('/api/admin/collect/mapping', { params }),

  // 获取映射详情
  getDetail: (id: number) => request.get(`/api/admin/collect/mapping/${id}`),

  // 创建映射
  create: (data: any) => request.post('/api/admin/collect/mapping', data),

  // 更新映射
  update: (id: number, data: any) => request.put(`/api/admin/collect/mapping/${id}`, data),

  // 删除映射
  delete: (id: number) => request.delete(`/api/admin/collect/mapping/${id}`),

  // 批量导入映射
  batchImport: (data: any) => request.post('/api/admin/collect/mapping/batch', data),

  // 导出映射
  export: (params?: any) => request.get('/api/admin/collect/mapping/export', { params }),

  // 获取远程分类
  getRemoteCategories: (sourceId: number) => request.get(`/api/admin/collect/categories/${sourceId}`),

  // 保存分类映射
  saveCategoryMapping: (data: any) => request.post('/api/admin/collect/categories/mapping', data)
}

// 统一导出
export const collectApi = {
  sources: collectSourcesApi,
  tasks: collectTasksApi,
  logs: collectLogsApi,
  config: collectConfigApi,
  monitor: collectMonitorApi,
  mapping: collectMappingApi,

  // 为了向后兼容，直接暴露常用的方法
  getSources: collectSourcesApi.getList,
  testSource: collectSourcesApi.test,
  addSource: collectSourcesApi.create,
  updateSource: collectSourcesApi.update,
  deleteSource: collectSourcesApi.delete,
  batchOperateSources: collectSourcesApi.batchOperate,
  batchCheckHealth: collectSourcesApi.batchHealthCheck,
  checkSourceConnectivity: collectSourcesApi.checkConnectivity,

  getTasks: collectTasksApi.getList,
  createTask: collectTasksApi.create,
  updateTask: collectTasksApi.update,
  deleteTask: collectTasksApi.delete,
  startTask: collectTasksApi.start,
  stopTask: collectTasksApi.stop,
  restartTask: collectTasksApi.restart,

  getLogs: collectLogsApi.getList,
  clearLogs: collectLogsApi.clear,

  getConfig: collectConfigApi.get,
  updateConfig: collectConfigApi.update,
  resetConfig: collectConfigApi.reset,

  // 监控相关方法
  getStatistics: collectMonitorApi.getStatistics,
  getSourceHealth: collectMonitorApi.getSourceHealth,
  autoHealthCheck: collectMonitorApi.autoHealthCheck,
  retryFailedTasks: collectMonitorApi.retryFailedTasks,
  getCollectTrends: (days: number) => collectMonitorApi.getMetrics({ type: 'trends', days }),
  getSourcePerformance: () => collectMonitorApi.getMetrics({ type: 'performance' }),

  // 分类映射相关方法
  getRemoteCategories: collectMappingApi.getRemoteCategories,
  saveCategoryMapping: collectMappingApi.saveCategoryMapping
}
