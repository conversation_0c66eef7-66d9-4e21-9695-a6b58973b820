"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefinitionBase = void 0;
const ID_1 = require("../ID");
const generator = (0, ID_1.createIdGenerator)();
class DefinitionBase {
    constructor(type, name, node, parent) {
        /**
         * A unique ID for this instance - primarily used to help debugging and testing
         */
        this.$id = generator();
        this.type = type;
        this.name = name;
        this.node = node;
        this.parent = parent;
    }
}
exports.DefinitionBase = DefinitionBase;
//# sourceMappingURL=DefinitionBase.js.map