#!/bin/sh

echo "🚀 启动用户前端 (开发模式)"

# 等待API服务启动
echo "⏳ 等待API服务..."
while ! wget --spider --quiet --header="X-API-Key: ShiPinAdmin2024ProductionKey32Bytes!@#\$%^&*()_+" http://shipin-api/api/v1/health 2>/dev/null; do
    sleep 2
done
echo "✅ API服务连接成功"

# 检查是否已有node_modules
if [ -d "node_modules" ] && [ -f "package-lock.json" ]; then
    echo "✅ 发现本地依赖，跳过安装"
else
    echo "📦 安装npm依赖..."

    # 设置npm配置
    npm config set fund false
    npm config set audit false
    npm config set progress false
    npm config set registry https://registry.npmmirror.com

    # 检测系统架构和平台
    ARCH=$(uname -m)
    PLATFORM=$(uname -s | tr '[:upper:]' '[:lower:]')
    echo "🔍 检测到系统: $PLATFORM-$ARCH"

    # 安装依赖
    echo "📦 开始安装依赖..."
    if npm install --no-fund --no-audit --loglevel=error; then
        echo "✅ 依赖安装成功"
    else
        echo "❌ npm安装失败，尝试使用yarn..."
        if command -v yarn >/dev/null 2>&1 || npm install -g yarn; then
            yarn install --non-interactive --silent
        else
            echo "❌ 所有安装方法都失败了"
            exit 1
        fi
    fi
fi

# 验证vite是否可用
if ! npx vite --version >/dev/null 2>&1; then
    echo "❌ vite不可用，尝试全局安装..."
    npm install -g vite
fi

echo "✅ 用户前端启动完成 (开发模式)"
echo "🌐 访问地址: http://localhost:${FRONTEND_PORT:-3002}"

# 启动开发服务器
exec npm run dev
