<?php
declare (strict_types = 1);

namespace app\middleware;

use think\Request;
use think\Response;

/**
 * CORS中间件
 * 处理跨域请求，配合API密钥认证使用
 */
class Cors
{
    /**
     * 处理请求
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // 处理OPTIONS预检请求
        if ($request->isOptions()) {
            return $this->handlePreflightRequest($request);
        }

        // 处理实际请求
        $response = $next($request);
        
        return $this->addCorsHeaders($response, $request);
    }

    /**
     * 处理预检请求
     */
    private function handlePreflightRequest(Request $request): Response
    {
        $response = Response::create('', 'html', 200);
        return $this->addCorsHeaders($response, $request);
    }

    /**
     * 添加CORS头
     */
    private function addCorsHeaders(Response $response, Request $request): Response
    {
        $origin = $request->header('Origin', '');

        // 允许的源
        $allowedOrigins = [
            'http://localhost:3001',  // 管理后台
            'http://localhost:3002',  // 用户前端
            'http://127.0.0.1:3001',
            'http://127.0.0.1:3002',
            'http://localhost:3000',  // 额外端口
            'http://127.0.0.1:3000',
            'http://localhost:8080',  // 常用开发端口
            'http://127.0.0.1:8080',
            'http://localhost:5173',  // Vite默认端口
            'http://127.0.0.1:5173',
            // 生产环境域名 - HTTP
            'http://*************:3001',  // 生产环境管理后台
            'http://*************:3002',  // 生产环境用户前端
            'http://*************:3000',  // 生产环境API
            // 生产环境域名 - HTTPS
            'https://*************:3001',
            'https://*************:3002',
            'https://*************:3000',
            // 如果有域名，也添加进来
            'https://zhengshiban.com',
            'https://www.zhengshiban.com',
            'http://zhengshiban.com',
            'http://www.zhengshiban.com',
        ];

        $headers = [];

        // 检查源是否被允许
        if (in_array($origin, $allowedOrigins)) {
            $headers['Access-Control-Allow-Origin'] = $origin;
        } else if (empty($origin)) {
            // 如果没有Origin头（比如直接访问），允许所有
            $headers['Access-Control-Allow-Origin'] = '*';
        } else {
            // 开发环境：允许localhost和127.0.0.1的任何端口
            if (preg_match('/^https?:\/\/(localhost|127\.0\.0\.1)(:\d+)?$/', $origin)) {
                $headers['Access-Control-Allow-Origin'] = $origin;
            } else {
                // 生产环境：检查是否是服务器IP
                if (preg_match('/^https?:\/\/67\.211\.220\.34(:\d+)?$/', $origin)) {
                    $headers['Access-Control-Allow-Origin'] = $origin;
                }
            }
        }

        $headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
        $headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-API-Key, X-Request-ID, X-Client-Version, X-Client-Platform, Cache-Control, Pragma, Expires, If-Modified-Since, If-None-Match';
        $headers['Access-Control-Allow-Credentials'] = 'false'; // API密钥认证不需要credentials
        $headers['Access-Control-Max-Age'] = '86400'; // 24小时

        $response->header($headers);

        return $response;
    }
}
